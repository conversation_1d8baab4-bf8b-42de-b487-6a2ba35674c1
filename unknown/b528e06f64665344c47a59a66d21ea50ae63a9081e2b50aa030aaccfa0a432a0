package com.enttribe.promptanalyzer.audit;

import com.enttribe.promptanalyzer.dto.prompt.PromptDtoSdk;

public class AuditContext {

    private PromptDtoSdk promptModel;
    private String auditId;

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private final AuditContext auditContext;

        public Builder() {
            this.auditContext = new AuditContext();
        }

        public Builder promptModel(PromptDtoSdk promptModel) {
            auditContext.promptModel = promptModel;
            return this;
        }

        public Builder auditId(String auditId) {
            auditContext.auditId = auditId;
            return this;
        }

        public AuditContext build() {
            return auditContext;
        }

    }

    public PromptDtoSdk getPromptModel() {
        return promptModel;
    }

    public void setPromptModel(PromptDtoSdk promptModel) {
        this.promptModel = promptModel;
    }

    public String getAuditId() {
        return auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

}