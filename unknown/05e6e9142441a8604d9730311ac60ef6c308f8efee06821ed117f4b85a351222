package com.enttribe.promptanalyzer.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.enttribe.promptanalyzer.exception.BusinessException;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.CSVRecord;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * Utility class for handling CSV file operations such as import, export, and validation.
 * Provides methods to process CSV files and convert data to and from CSV format.
 *
 * <AUTHOR> Dangi
 *  @version 1.0
 */
public class CSVUtils {

    private CSVUtils() {
    }

    private static final Logger logger = LogManager.getLogger(CSVUtils.class);
    private static final String FILE_EXTENSION = "csv";
    private static final String ATTACHMENT_FILENAME = "attachment; filename=";
    private static final String NOT_FOUND_MESSAGE = "' is not found.";

    /**
     * Imports a CSV file, processes each record, and returns a response entity with the results.
     *
     * @param csvFile the CSV file to import
     * @param csvColumnHeader the expected CSV column headers
     * @param recordTransformer a function to transform CSV records into entities
     * @param recordProcessor a consumer to process each transformed entity
     * @param exportedFileName the name of the exported file
     * @param maxRecords the maximum number of records allowed
     * @param <T> the type of the entity
     * @return a ResponseEntity containing the import results
     * @throws IOException if an I/O error occurs
     */
    public static <T> ResponseEntity<Resource> importCSV(MultipartFile csvFile,
                                                         List<String> csvColumnHeader,
                                                         Function<CSVRecord, T> recordTransformer,
                                                         Consumer<T> recordProcessor,
                                                         String exportedFileName,
                                                         int maxRecords) throws IOException {

        ArrayList<String> csvResponseHeader = new ArrayList<>(csvColumnHeader);
        csvResponseHeader.add("Status");
        csvResponseHeader.add("Error message");

        CSVFormat printerFormat = CSVFormat.DEFAULT.builder()
                .setHeader(csvResponseHeader.toArray(new String[0]))
                .build();

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             CSVPrinter csvPrinter = new CSVPrinter(
                     new OutputStreamWriter(byteArrayOutputStream, StandardCharsets.UTF_8),
                     printerFormat)) {

            try (InputStreamReader reader = new InputStreamReader(csvFile.getInputStream(), StandardCharsets.UTF_8)) {
                validateCsvFile(csvFile);
                validateFileColumnSequence(reader, csvColumnHeader);
            }

            try (InputStreamReader reader1 = new InputStreamReader(csvFile.getInputStream(), StandardCharsets.UTF_8)) {
                validateFileRecordLimit(reader1, maxRecords);
            }

            CSVFormat parserFormat = CSVFormat.DEFAULT.builder()
                    .setHeader(csvColumnHeader.toArray(new String[0]))
                    .setSkipHeaderRecord(true)
                    .build();

            try (InputStreamReader reader = new InputStreamReader(csvFile.getInputStream(), StandardCharsets.UTF_8);
                 CSVParser csvParser = new CSVParser(reader, parserFormat)) {

                for (CSVRecord csvRecord : csvParser) {
                    try {
                        T entity = recordTransformer.apply(csvRecord);
                        recordProcessor.accept(entity);

                        List<String> newRecordData = new ArrayList<>();
                        for (String field : csvRecord) {
                            newRecordData.add(field);
                        }
                        newRecordData.add("success");
                        newRecordData.add(""); // no error message
                        csvPrinter.printRecord(newRecordData);
                    } catch (Exception e) {
                        logger.error("Exception while importing data. Exception message is: {}", e.getMessage());

                        List<String> errorRecordData = new ArrayList<>();
                        for (String field : csvRecord) {
                            errorRecordData.add(field);
                        }
                        errorRecordData.add("error");
                        errorRecordData.add(e.getMessage());
                        csvPrinter.printRecord(errorRecordData);
                    }
                }
            } catch (IOException e) {
                logger.error("Error occurred during CSV import. Exception message : {}", e.getMessage());
                throw new BusinessException(e.getMessage());
            } catch (Exception e) {
                logger.error("Something Went Wrong. Exception message : {}", e.getMessage());
                throw new BusinessException(e.getMessage());
            }

            csvPrinter.flush();
            byte[] dataBytes = byteArrayOutputStream.toByteArray();
            ByteArrayResource resource = new ByteArrayResource(dataBytes);

            return ResponseEntity
                    .ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, ATTACHMENT_FILENAME + exportedFileName + "_Response.csv")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        }
    }

    /**
     * Exports a list of records to a CSV file and returns a response entity with the file.
     *
     * @param recordList the list of records to export
     * @param csvColumnHeader the CSV column headers
     * @param fileName the name of the exported file
     * @param fieldExtractors a list of functions to extract fields from each record
     * @param <T> the type of the record
     * @return a ResponseEntity containing the exported CSV file
     */
    public static <T> ResponseEntity<Resource> exportCSV(List<T> recordList, List<String> csvColumnHeader, String fileName, List<Function<T, Object>> fieldExtractors) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             CSVPrinter csvPrinter = new CSVPrinter(
                     new PrintWriter(outputStream),
                     CSVFormat.DEFAULT.builder()
                             .setHeader(csvColumnHeader.toArray(new String[0]))
                             .build())) {

            for (T item : recordList) {
                List<String> rowData = new ArrayList<>();
                for (Function<T, Object> fieldExtractor : fieldExtractors) {
                    Object value = fieldExtractor.apply(item);
                    rowData.add(value != null ? value.toString() : null);
                }
                csvPrinter.printRecord(rowData);
            }

            csvPrinter.flush();
            byte[] dataBytes = outputStream.toByteArray();
            ByteArrayResource resource = new ByteArrayResource(dataBytes);

            return ResponseEntity
                    .ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, ATTACHMENT_FILENAME + fileName + ".csv")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    /**
     * Exports a CSV template with the specified column headers and returns a response entity with the file.
     *
     * @param csvColumnHeader the CSV column headers
     * @param templateName the name of the template file
     * @return a ResponseEntity containing the exported CSV template
     */
    public static ResponseEntity<Resource> exportCSVTemplate(List<String> csvColumnHeader, String templateName) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             CSVPrinter csvPrinter = new CSVPrinter(
                     new PrintWriter(outputStream),
                     CSVFormat.DEFAULT.builder()
                             .setHeader(csvColumnHeader.toArray(new String[0]))
                             .build())) {

            csvPrinter.printRecord(); // Print an empty row for column headers
            csvPrinter.flush();
            byte[] dataBytes = outputStream.toByteArray();
            ByteArrayResource resource = new ByteArrayResource(dataBytes);

            return ResponseEntity
                    .ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, ATTACHMENT_FILENAME + templateName + ".csv")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }


    /**
     * Exports a CSV template with the specified column headers and sample data, and returns a response entity with the file.
     *
     * @param csvColumnHeader the CSV column headers
     * @param sampleData the sample data to include in the template
     * @param templateName the name of the template file
     * @return a ResponseEntity containing the exported CSV template
     */
    public static ResponseEntity<Resource> exportCSVTemplate(List<String> csvColumnHeader, List<String> sampleData, String templateName) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             CSVPrinter csvPrinter = new CSVPrinter(
                     new PrintWriter(outputStream),
                     CSVFormat.DEFAULT.builder()
                             .setHeader(csvColumnHeader.toArray(new String[0]))
                             .build())) {

            csvPrinter.printRecord(sampleData);
            csvPrinter.flush();

            byte[] dataBytes = outputStream.toByteArray();
            ByteArrayResource resource = new ByteArrayResource(dataBytes);

            return ResponseEntity
                    .ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, ATTACHMENT_FILENAME + templateName + ".csv")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }


    /**
     * Exports a single record to a CSV file and returns a response entity with the file.
     *
     * @param item the record to export
     * @param csvColumnHeader the CSV column headers
     * @param fileName the name of the exported file
     * @param fieldExtractors a list of functions to extract fields from the record
     * @param <T> the type of the record
     * @return a ResponseEntity containing the exported CSV file
     */
    public static <T> ResponseEntity<Resource> exportCSV(T item, List<String> csvColumnHeader, String fileName, List<Function<T, Object>> fieldExtractors) {
        List<T> recordList = List.of(item);
        return exportCSV(recordList, csvColumnHeader, fileName, fieldExtractors);
    }

    /**
     * Retrieves a set of entities by their names using the specified find function.
     *
     * @param entityNames the names of the entities to retrieve
     * @param findEntity the function to find an entity by name
     * @param <T> the type of the entity
     * @return a set of entities
     */
    public static <T> Set<T> getEntitySetByNames(String entityNames, Function<String, T> findEntity) {
        String[] namesArray = entityNames.split(", ");
        return Arrays.stream(namesArray)
                .filter(entityName -> entityName != null && !entityName.isEmpty() && !entityName.equals("N/A"))
                .map(entityName -> {
                    try {
                        return findEntity.apply(entityName);
                    } catch (Exception e) {
                        logger.error("something went wrong : {}", e.getMessage());
                        throw new BusinessException("Entity with Name '" + entityName + NOT_FOUND_MESSAGE);
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * Finds an entity by its name using the specified find function.
     *
     * @param entityName the name of the entity to find
     * @param findFunction the function to find an entity by name
     * @param <T> the type of the entity
     * @return the found entity, or null if not found
     */
    public static <T> T findEntityByName(String entityName, Function<String, T> findFunction) {
        try {
            if (entityName != null && !entityName.isBlank() && !entityName.equals("N/A")) {
                T entity = findFunction.apply(entityName);
                if (entity != null) {
                    return entity;
                }
            }
        } catch (Exception e) {
            logger.error("Error while finding '{}'. Exception message : {}", entityName, e.getMessage());
            throw new BusinessException("Entity with name '" + entityName + NOT_FOUND_MESSAGE);
        }
        return null;
    }

    /**
     * Retrieves a comma-separated string of entity names using the specified get name function.
     *
     * @param entities the collection of entities
     * @param getNameFunction the function to get the name of an entity
     * @param <T> the type of the entity
     * @return a comma-separated string of entity names
     */
    public static <T> String getEntityNames(Collection<T> entities, Function<T, String> getNameFunction) {
        return entities.stream()
                .map(getNameFunction)
                .collect(Collectors.joining(", "));
    }

    /**
     * Finds an entity by its ID using the specified find function.
     *
     * @param entityIdAndName the ID and name of the entity to find
     * @param findFunction the function to find an entity by ID
     * @param <T> the type of the entity
     * @return the found entity, or null if not found
     */
    public static <T> T findEntityById(String entityIdAndName, Function<Integer, T> findFunction) {
        try {
            if (entityIdAndName != null && !entityIdAndName.isBlank() && !entityIdAndName.equals("N/A")) {
                int commaIndex = entityIdAndName.indexOf(',');
                if (commaIndex != -1) {
                    String entityId = entityIdAndName.substring(0, commaIndex);

                    T entity = findFunction.apply(Integer.parseInt(entityId));
                    if (entity != null) {
                        return entity;
                    }
                }
            }
        } catch (NumberFormatException e) {
            logger.error("Error parsing entityId. Exception message: {}", e.getMessage());
            throw new BusinessException("Error parsing entityId. Message: " + e.getMessage());
        } catch (Exception e) {
            logger.error("something went wrong : {}", e.getMessage());
            throw new BusinessException("Entity with Id and Name '" + entityIdAndName + NOT_FOUND_MESSAGE);
        }
        return null;
    }

    /**
     * Validates the CSV file for correct format and data presence.
     *
     * @param file the CSV file to validate
     * @throws IllegalArgumentException if the file is invalid
     * @throws IOException if an I/O error occurs
     */
    private static void validateCsvFile(MultipartFile file)
            throws IllegalArgumentException, IOException {
        if (file == null) {
            throw new IllegalArgumentException("MultipartFile is null");
        }

        String fileExtension;
        try {
            fileExtension = getFileExtensionFromFileName(Objects.requireNonNull(file.getOriginalFilename()));
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to determine file extension: " + e.getMessage());
        }
        if (!fileExtension.equalsIgnoreCase(CSVUtils.FILE_EXTENSION)) {
            throw new IllegalArgumentException(
                    "Invalid file extension. Expected: " + CSVUtils.FILE_EXTENSION + ", Actual: " + fileExtension);
        }
        if (!hasData(file.getInputStream())) {
            throw new IllegalArgumentException("CSV file does not contain any data.");
        }
    }

    /**
     * Retrieves the file extension from the file name.
     *
     * @param fileName the file name
     * @return the file extension
     */
    private static String getFileExtensionFromFileName(String fileName) {
        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex >= 0 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1);
        }
        return "File does not have an extension";
    }

    /**
     * Checks if the CSV file contains data.
     *
     * @param inputStream the input stream of the CSV file
     * @return true if the file contains data, false otherwise
     * @throws IOException if an I/O error occurs
     */
    private static boolean hasData(InputStream inputStream) throws IOException {
        try (InputStreamReader reader = new InputStreamReader(inputStream);
             CSVParser csvParser = new CSVParser(reader,
                     CSVFormat.DEFAULT.builder()
                             .setSkipHeaderRecord(true)
                             .setHeader() // use auto header detection
                             .setDelimiter(',')
                             .setIgnoreEmptyLines(true)
                             .build())) {

            for (CSVRecord csvRecord : csvParser) {
                if (csvRecord.getRecordNumber() > 0) {
                    return true;
                }
            }
            return false;
        }
    }


    /**
     * Validates the column sequence of the CSV file against the expected sequence.
     *
     * @param reader the input stream reader of the CSV file
     * @param expectedSequence the expected column sequence
     * @throws IllegalArgumentException if the column sequence is incorrect
     */
    private static void validateFileColumnSequence(InputStreamReader reader, List<String> expectedSequence) throws IllegalArgumentException {
        if (expectedSequence == null || expectedSequence.isEmpty()) {
            throw new IllegalArgumentException("Expected sequence is null or empty");
        }

        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader()
                .setSkipHeaderRecord(false)  // We want to access header row via getHeaderMap()
                .build();

        try (CSVParser csvParser = new CSVParser(reader, csvFormat)) {
            String[] headerRecord = csvParser.getHeaderMap().keySet().toArray(new String[0]);
            List<String> actualSequence = List.of(headerRecord);
            if (!actualSequence.equals(expectedSequence)) {
                throw new IllegalArgumentException("Header sequence is incorrect. Expected: " + expectedSequence + ", Actual: " + actualSequence);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("Error occurred during CSV validation: " + e.getMessage(), e);
        }
    }



    /**
     * Validates the record limit of the CSV file against the maximum allowed records.
     *
     * @param reader the input stream reader of the CSV file
     * @param maxRecords the maximum allowed records
     * @throws IllegalArgumentException if the record limit is exceeded
     */
    private static void validateFileRecordLimit(InputStreamReader reader, Integer maxRecords) throws IllegalArgumentException {
        if (reader == null) {
            throw new IllegalArgumentException("MultipartFile is null");
        } else {
            try (CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT)) {
                long recordCount = csvParser.getRecords().size();
                if (recordCount > maxRecords) {
                    throw new IllegalArgumentException("File exceeds the maximum allowed records. Max records: " + maxRecords);
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("Error occurred during CSV validation: " + e.getMessage());
            }
        }
    }
}