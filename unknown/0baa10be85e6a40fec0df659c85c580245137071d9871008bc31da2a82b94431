package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.Query;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for Query entity operations.
 * Provides methods to interact with the Query table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface QueryDao  extends JpaRepository<Query,Integer> {
}
