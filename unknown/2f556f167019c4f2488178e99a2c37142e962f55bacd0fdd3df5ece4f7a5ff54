package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;

import java.util.List;
import java.util.Map;


/**
 * Service interface for managing Processor operations.
 * This interface defines the contract for handling processor-related business operations
 * such as creating, updating, and searching processors within the application.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface ProcessorService {

    Map<String, Object> save(ProcessorRequestDto requestDto);
    Map<String, Object> update(Integer id, ProcessorRequestDto requestDto);
    List<ProcessorResponseDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType);
    Long count(String filter);
}
