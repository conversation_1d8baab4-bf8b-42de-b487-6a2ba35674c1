package com.enttribe.promptanalyzer.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Utility class for generating random messages.
 * Provides methods to retrieve random plan and trigger messages from predefined lists.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class MessageUtils {

    // Private constructor to prevent instantiation
    private MessageUtils() {
    }

    // Static Random instance to be reused
    private static final Random RANDOM = new Random();

    /**
     * Returns a random plan message from a predefined list of messages.
     *
     * @return a randomly selected plan message
     */
    public static String getPlanMessage() {
        List<String> messageList = new ArrayList<>();
        messageList.add("Thank you for sharing your input. Here is the plan crafted based on your specifications.");
        messageList.add("The following plan has been created based on the input you provided. Please review and let me know if adjustments are needed.");
        messageList.add("Here is the plan we generated based on the information you shared. Let us know if it requires further refinement.");
        messageList.add("We have analyzed your input and created the following plan tailored to your needs.");
        messageList.add("Based on the details you provided, the following plan has been generated to meet your requirements.");
        int index = RANDOM.nextInt(messageList.size());
        return messageList.get(index);
    }

    /**
     * Returns a random trigger message from a predefined list of messages.
     *
     * @return a randomly selected trigger message
     */
    public static String getTriggerMessage() {
        List<String> messageList = new ArrayList<>();
        messageList.add("The triggers have been successfully created based on your input.");
        messageList.add("We have successfully generated the triggers as per your specifications.");
        messageList.add("The triggers were created successfully. Please review them to ensure they meet your requirements.");
        messageList.add("Your input has been processed, and the triggers have been successfully created.");
        messageList.add("Triggers have been generated successfully. Feel free to verify if they align with your expectations.");
        int index = RANDOM.nextInt(messageList.size());
        return messageList.get(index);
    }

    public static String getErrorMessage() {
        List<String> messageList = new ArrayList<>();
        messageList.add("I apologize for the inconvenience, but I’m currently not capable of handling this request. I'm constantly evolving, and I appreciate your understanding.");
        messageList.add("I'm sorry, but this task is beyond my current capabilities. I'm working to expand my skills to better support you in the future.");
        messageList.add("My sincere apologies! At this time, I'm not equipped to manage this request. I'm committed to improving and hope to assist you better soon.");
        messageList.add("I regret to inform you that I'm currently not capable of performing this action. Thank you for your patience as I continue to improve.");
        messageList.add("Apologies for the inconvenience. Unfortunately, I'm not yet capable of handling this task. I appreciate your understanding and look forward to growing my abilities.");
        int index = RANDOM.nextInt(messageList.size());
        return messageList.get(index);
    }
}
