package com.enttribe.promptanalyzer.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * Entity representing an audit record for prompts.
 * Captures details about prompt interactions and responses for auditing purposes.
 *
 * <AUTHOR>
 * @version 1.0
 */
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"})
@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "PROMPT_AUDIT")
public class PromptAudit {

    /**
     * Unique ID of the prompt audit record.
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID")
    private Integer id;

    /**
     * Model used for generating the response.
     */
    @Column(name = "MODEL")
    private String model;

    /**
     * Text of the response generated.
     */
    @Column(name = "RESPONSE_TEXT", columnDefinition = "TEXT")
    private String responseText;

    /**
     * Number of tokens in the prompt.
     */
    @Column(name = "PROMPT_TOKEN")
    private Long promptToken;

    /**
     * Number of tokens generated in the response.
     */
    @Column(name = "GENERATION_TOKENS")
    private Long generationTokens;

    /**
     * Total number of tokens used.
     */
    @Column(name = "TOTAL_TOKEN")
    private Long totalToken;

    /**
     * Name of the application associated with the prompt.
     */
    @Column(name = "APPLICATION_NAME")
    private String applicationName;

    /**
     * Category of the prompt.
     */
    @Column(name = "CATEGORY")
    private String category;

    /**
     * Unique identifier for the prompt.
     */
    @Column(name = "PROMPT_ID")
    private String promptId;

    /**
     * Time taken to generate the response.
     */
    @Column(name = "RESPONSE_TIME")
    private Double responseTime;

    /**
     * Metadata tags associated with the prompt.
     */
    @Column(name = "META_TAGS")
    private String metaTags;

    /**
     * Options used in the chat.
     */
    @Column(name = "CHAT_OPTIONS")
    private String chatOptions;

    /**
     * Function arguments used, specific to agentic audit.
     */
    @Column(name = "FUNCTION_ARGS")
    private String functionArgs;  //This is for Agentic audit

    /**
     * Audit identifier for tracking purposes.
     */
    @Column(name = "AUDIT_ID")
    private String auditId;

    /**
     * Name of the prompt.
     */
    @Column(name = "PROMPT_NAME")
    private String promptName;

    /**
     * Timestamp when the audit record was created.
     */
    @Column(name = "CREATION_TIME")
    private Date creationTime;

    /**
     * HTTP status code of the response.
     */
    @Column(name = "HTTP_STATUS")
    private Integer httpStatus;

    /**
     * Text of the request sent.
     */
    @Column(name = "REQUEST_TEXT", columnDefinition = "LONGTEXT")
    private String requestText;

    /**
     * Status of the prompt audit.
     */
    @Column(name = "STATUS")
    private String status;

    /**
     * The total cost associated with using llm model,
     * calculated based on input and output token usage.
     * Default value is set to 0.0 if not provided.
     */
    @Column(name = "TOTAL_COST", columnDefinition = "DOUBLE DEFAULT 0.0")
    private double totalCost;

    /**
     * Provider of the prompt audit to store llm model provider.
     */
    @Column(name = "PROVIDER")
    private String provider;

    /**
     * Error message details.
     */
    @Column(name = "ERROR_MESSAGE", length = 5000)
    private String errorMessage;

    @Column(name="AGENT_NAME",length = 100)
    private String agentName;

}
