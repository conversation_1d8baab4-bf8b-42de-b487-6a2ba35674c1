package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.query.QueryRequestDto;
import com.enttribe.promptanalyzer.dto.query.QueryResponseDto;

import java.util.List;
import java.util.Map;

/**
 * Manages query operations and search functionality.
 * This service handles the creation, searching, and management of queries,
 * providing a centralized interface for query-related operations with 
 * pagination and filtering support.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface QueryService {

    List<QueryResponseDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType);

    Long count(String filter);

    Map<String, String> createQuery(QueryRequestDto dto);

    Map<String, String> softDelete(Integer id);
}
