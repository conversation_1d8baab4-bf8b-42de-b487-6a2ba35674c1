package com.enttribe.promptanalyzer.dto.tool;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that manages version information for tools.
 * Provides version tracking and identification capabilities for tool versioning.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ToolVersionDetailsDto {

    private Integer id;
    private String version;

    public ToolVersionDetailsDto(Integer id, String version) {
        this.id = id;
        this.version = version;
    }
}
