package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.TagDao;
import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Tag;
import com.enttribe.promptanalyzer.service.TagService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.TagUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Implementation of the {@link TagService} interface.
 * This service class provides the business logic for managing tags,
 * including operations for searching, creating, updating, and deleting tags.
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TagServiceImpl implements TagService {

    private final CustomFilter customFilter;
    private final TagDao tagDao;

    private static final String SUCCESS= "success";

    /**
     * Searches for tags based on the provided filter criteria.
     *
     * @param filter    the search filter criteria
     * @param offset    the starting point of the result set
     * @param size      the maximum number of results to return
     * @param orderBy   the field to sort by
     * @param orderType the sort direction (ASC/DESC)
     * @return List of TagResponseDto containing the search results
     * @throws BusinessException if there's an error during the search operation
     */
    @Override
    public List<TagResponseDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        try {
            List<Tag> tags = customFilter.searchByFilter(Tag.class, filter, orderBy, orderType, offset, size);
            return TagUtils.getTagList(tags);
        } catch (Exception e) {
            log.error("Error while searching for queries: {}", e.getMessage(), e);
            throw new BusinessException("Error while searching tags: " + e.getMessage(), e);
        }
    }

     /**
     * Counts the number of tags matching the given filter criteria.
     *
     * @param filter the filter criteria to apply
     * @return the number of matching tags
     */
    @Override
    public Long count(String filter) {
        return customFilter.countByFilter(Tag.class,filter);
    }

     /**
     * Creates a new tag in the system.
     *
     * @param dto the TagRequestDto containing the tag information
     * @return Map containing the status of the operation
     * @throws BusinessException if there's an error during the save operation
     */
    @Override
    public Map<String, String> save(TagRequestDto dto) {
        log.debug("Inside save tag method with name: {}", dto.getName());
        Map<String, String> response = new HashMap<>();
        try {
            Tag tag = new Tag();
            TagUtils.mapDtoToEntity(tag, dto);
            tag.setCreatedTime(new Date());
            tag.setModifiedTime(new Date());
            tagDao.save(tag);
            response.put("status", SUCCESS);
            log.info("tag created successfully with ID: {}", tag.getId());
        } catch (Exception e) {
            log.error("Unable to save tag: {}", e.getMessage(), e);
            throw new BusinessException("Unable to save tag: " + e.getMessage());
        }
        return response;
    }

     /**
     * Updates an existing tag in the system.
     *
     * @param dto the TagRequestDto containing the updated tag information
     * @return Map containing the status of the operation
     * @throws ResourceNotFoundException if the tag to update is not found
     * @throws BusinessException if there's an error during the update operation
     */
    @Override
    public Map<String, String> update(TagRequestDto dto) {
        log.debug("Inside update tag method with id: {}", dto.getId());
        Map<String, String> response = new HashMap<>();
        try {
            Tag tag = tagDao.findById(dto.getId()).orElseThrow(
                    () -> new ResourceNotFoundException("Tag record not found for ID: " + dto.getId()));

            TagUtils.mapDtoToEntity(tag, dto);
            tag.setModifiedTime(new Date());
            tagDao.save(tag);

            response.put("status", SUCCESS);
            log.info("Tag updated successfully with ID: {}", tag.getId());
        } catch (Exception e) {
            log.error("Unable to update Tag: {}", e.getMessage(), e);
            throw new BusinessException("Unable to update Tag: " + e.getMessage(), e);
        }
        return response;
    }

    /**
     * Performs a soft delete on a tag by marking it as deleted.
     *
     * @param id the ID of the tag to delete
     * @return Map containing the result of the deletion operation
     */
    @Override
    public Map<String, String> softDelete(Integer id) {
        log.debug("Attempting to soft delete Tag with ID: {}", id);
        Map<String, String> result = new HashMap<>();
        Optional<Tag> tag = tagDao.findById(id);
        if (tag.isPresent()) {
            Tag tag1 = tag.get();
            tag1.setDeleted(true);
            tagDao.save(tag1);
            log.info("Tag with ID {} marked as deleted", id);
            result.put("result", SUCCESS);
        } else {
            log.warn("Tag with ID {} not found for deletion", id);
            result.put("result", "failed");
        }
        return result;
    }

}

