package com.enttribe.promptanalyzer.ai.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Data Transfer Object for chat completion requests.
 * Contains configuration parameters for AI chat interactions.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChatCompletionRequestDto {

    private Integer maxTokens;
    private List<MessageDto> messages;
    private String model;
    private Double temperature;
    private Double topP;
    private Boolean jsonMode;
    private String provider;

}