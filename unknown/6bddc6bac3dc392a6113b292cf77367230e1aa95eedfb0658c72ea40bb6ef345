package com.enttribe.promptanalyzer.dto.knowledge_base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) specifically designed for SDK interactions with the knowledge base.
 * Provides a simplified interface for external SDK implementations.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KnowledgeBaseSdkDto {

    private Integer id;
    private String name;
    private String type;
    private String description;
    private String docType;
    private String filter;
    private Integer topK;
    private Double similarityThreshold;
    private String fileName;
    private Boolean isContext;
    private Boolean returnDirect;

}
