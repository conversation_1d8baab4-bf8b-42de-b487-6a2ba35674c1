package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.trigger.TriggerRequestDto;
import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import com.enttribe.promptanalyzer.rest.TriggerRest;
import com.enttribe.promptanalyzer.service.TriggerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing trigger operations.
 * Provides endpoints for creating, updating, searching, and counting triggers.
 * All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/trigger")
@RequiredArgsConstructor
@Slf4j
public class TriggerRestImpl implements TriggerRest {

    private final TriggerService service;

    /**
     * Searches for triggers based on the provided filter and pagination options.
     *
     * @param filter the search filter
     * @param offset the offset for pagination
     * @param size the number of results to return
     * @param orderBy the field to order the results by
     * @param orderType the type of ordering (ascending or descending)
     * @return a list of trigger response data transfer objects matching the search criteria
     */
    @Override
    public List<TriggerResponseDto> search(
            String filter,
            Integer offset,
            Integer size,
            String orderBy,
            String orderType) {
        log.info("Received request to search triggers");
        return service.search(filter, offset, size, orderBy, orderType);
    }

    /**
     * Counts the number of triggers matching the provided filter.
     *
     * @param filter the search filter
     * @return the count of triggers matching the filter
     */
    @Override
    public Long count(String filter) {
        Long count = service.count(filter);
        log.info("Count completed. Total triggers: {}", count);
        return count;
    }

    /**
     * Saves a new trigger.
     *
     * @param dto the trigger request data transfer object to be saved
     * @return a map containing the result of the save operation
     */
    @Override
    public Map<String, String> save(@RequestBody TriggerRequestDto dto) {
        log.info("Creating new trigger");
        return  service.createTrigger(dto);
    }

    /**
     * Updates an existing trigger.
     *
     * @param dto the trigger request data transfer object containing updated information
     * @return a map containing the result of the update operation
     */
    @Override
    public Map<String, String> update(TriggerRequestDto dto) {
        log.info("Updating trigger");
        return service.updateTrigger(dto);
    }

    @Override
    public TriggerResponseDto getTriggerByName(String name) {
        return service.getTriggerByName(name);
    }


}
