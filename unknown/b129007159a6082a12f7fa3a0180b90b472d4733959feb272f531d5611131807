package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.PromptAudit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * Data Access Object interface for PromptAudit entity operations.
 * Provides methods to interact with prompt audit logs in the database,
 * tracking the history and changes of prompts.
 *
 * <AUTHOR>
 * @version 1.0
 * @see PromptAudit
 * @see JpaRepository
 */
public interface PromptAuditDao extends JpaRepository<PromptAudit, Integer> {

    /**
     * Retrieves all prompt audit entries for a specific audit ID.
     *
     * @param auditId The unique identifier of the audit trail
     * @return List of PromptAudit entries matching the audit ID
     */
    @Query("SELECT p FROM PromptAudit p WHERE p.auditId = :auditId")
    List<PromptAudit> getPromptAuditListByAuditId(String auditId);

    @Query("SELECT p FROM PromptAudit p WHERE p.promptId = :promptId")
    List<PromptAudit> getPromptAuditListByPromptId(String promptId);
}
