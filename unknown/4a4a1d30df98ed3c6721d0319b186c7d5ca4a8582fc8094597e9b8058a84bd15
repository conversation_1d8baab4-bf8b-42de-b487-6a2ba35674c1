package com.enttribe.promptanalyzer.dto.prompt;

import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * A Data Transfer Object (DTO) that represents a complete prompt configuration
 * for conversion purposes. Includes all necessary parameters for prompt execution
 * and management.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PromptConvertorDto {

    private Integer id;
    private String application;
    private String category;
    private String promptId;
    private String status;
    private String type;
    private String prompt;
    private Double temperature;
    private Integer maxTokens;
    private String version;
    private Double topP;
    private String name;
    private String assertionTemplate;
    private String defaultFormat;
    private List<MessageConvertorDto> messages;
    private String model;
    private String inference;
    private String provider;
    private List<ToolDto> tools;
    private Boolean jsonMode;
    private Boolean llmGuard;
    private String tags;

}
