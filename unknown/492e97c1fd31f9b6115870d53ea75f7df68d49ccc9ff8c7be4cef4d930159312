package com.enttribe.promptanalyzer.dto.crawl;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents the response from a crawling operation.
 * This class encapsulates the crawl task status, results, and potential error information,
 * providing a structured format for crawl operation responses.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class CrawlResponse {

    @JsonProperty("task_id")
    private String taskId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("result")
    private Result result;

    @JsonProperty("error")
    private String error;

    public record Result(@JsonProperty("markdown") String markdown) {}

    public CrawlResponse(String error) {
        this.error = error;
    }
}
