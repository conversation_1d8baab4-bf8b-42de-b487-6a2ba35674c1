package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.agent.AgentConvertDto;
import com.enttribe.promptanalyzer.dto.agent.AgentDto;

import java.util.List;
import java.util.Map;

/**
 * Handles all agent-related business operations including creation and searching.
 * This service provides a clean interface for managing agents in the system,
 * abstracting away the underlying implementation details.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface AgentService {

    Map<String, String> createAgent(AgentDto agent);

    List<AgentConvertDto> search(String filter, Integer offset, Integer size, String orderby, String orderType);
}