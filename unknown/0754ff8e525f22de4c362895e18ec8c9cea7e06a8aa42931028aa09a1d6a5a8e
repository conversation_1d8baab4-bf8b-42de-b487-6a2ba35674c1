package com.enttribe.promptanalyzer.dto.knowledge_base;

import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.Getter;
import lombok.Builder;
import lombok.AllArgsConstructor;

/**
 * A Data Transfer Object (DTO) for creating and configuring knowledge base instances.
 * Handles database connections, API configurations, and similarity settings.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class KnowledgeBaseRequestDto {

    private String name;
    private String type;
    private String description;
    private String dbName;
    private String dbUsername;
    private String dbPassword;
    private String apiEndpoint;
    private String apiType;
    private String apiAuthType;
    private String apiAuthValue;
    private Boolean isContext;
    private Integer topK;
    private Double similarityThreshold;
    private String sourceCode;
    private String tags;
    private Boolean returnDirect;

}



