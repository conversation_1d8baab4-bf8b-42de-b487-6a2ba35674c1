package com.enttribe.promptanalyzer.dto.trigger;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents the response for trigger operations.
 * Extends TriggerRequestDto with additional response-specific fields for
 * comprehensive trigger information.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class TriggerResponseDto {

    private Integer id;
    private String description;
    private String name;
    private String displayName;
    private String metadata;
    private String application;
    private String entity;
    private String type;
}
