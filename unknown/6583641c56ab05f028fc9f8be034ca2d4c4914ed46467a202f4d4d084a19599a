package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import com.enttribe.promptanalyzer.model.Tag;

import java.util.List;

/**
 * Utility class for handling Tag entity and DTO conversions.
 * Provides methods for mapping between Tag entities and DTOs.
 *
 * <AUTHOR>
 * @version 1.0
 */
public class TagUtils {

    /**
     * Private constructor to prevent instantiation of utility class.
     */
    private TagUtils() {
    }

    /**
     * Maps data from a TagRequestDto to a Tag entity.
     *
     * @param tag The target Tag entity to be updated
     * @param tagRequestDto The source DTO containing tag data
     * @return The updated Tag entity
     */
    public static Tag mapDtoToEntity(Tag tag, TagRequestDto tagRequestDto) {
        tag.setName(tagRequestDto.getName());
        tag.setType(tagRequestDto.getType());
        return tag;
    }

    /**
     * Converts a Tag entity to a TagResponseDto.
     *
     * @param tag The Tag entity to convert
     * @return A new TagResponseDto containing the tag data, or null if the input is null
     */
    public static TagResponseDto convertToDTO(Tag tag) {
        if (tag == null) {
            return null;
        }
        return TagResponseDto.builder()
                .id(tag.getId())
                .name(tag.getName())
                .type(tag.getType())
                .build();

    }

    /**
     * Converts a list of Tag entities to a list of TagResponseDtos.
     *
     * @param tags The list of Tag entities to convert
     * @return A list of TagResponseDtos, or an empty list if the input is null or empty
     */
    public static List<TagResponseDto> getTagList(List<Tag> tags) {
        if (tags == null || tags.isEmpty()) {
            return List.of();
        }
        return tags.stream()
                .map(TagUtils::convertToDTO)
                .toList();
    }


}
