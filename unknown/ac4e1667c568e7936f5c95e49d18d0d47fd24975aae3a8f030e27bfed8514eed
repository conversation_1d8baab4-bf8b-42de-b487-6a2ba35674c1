package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.AgentHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * This interface provides data access methods for AgentHistory entities, 
 * allowing for CRUD operations and custom queries.
 * Author: VisionWaves
 * Version: 1.0
 */
@Repository
public interface AgentHistoryDao extends JpaRepository<AgentHistory, Integer> {

    /**
     * Retrieves the agent history for a given agent name.
     *
     * @param agentName the name of the agent whose history is to be retrieved
     * @return an Optional containing the AgentHistory if found, otherwise empty
     */
    @Query("SELECT a FROM AgentHistory a WHERE a.agentName = :agentName")
    Optional<AgentHistory> getAgentHistory(String agentName);

}
