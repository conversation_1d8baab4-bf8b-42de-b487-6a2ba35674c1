package com.enttribe.promptanalyzer.util;

import javax.tools.*;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;

/**
 * A custom Java file manager that stores compiled class files in memory.
 * This class extends ForwardingJavaFileManager to intercept and store class file output.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class InMemoryClassFileManager extends ForwardingJavaFileManager<JavaFileManager> {

    private final Map<String, ByteArrayOutputStream> classFiles = new HashMap<>();

    /**
     * Constructs an InMemoryClassFileManager with the specified file manager.
     *
     * @param fileManager the JavaFileManager to forward calls to
     */
    public InMemoryClassFileManager(JavaFileManager fileManager) {
        super(fileManager);
    }

    /**
     * Provides a JavaFileObject for output, storing the class file in memory.
     *
     * @param location the location to write the output
     * @param className the name of the class
     * @param kind the kind of the file
     * @param sibling a sibling file object
     * @return a JavaFileObject for writing the class file
     */
    @Override
    public JavaFileObject getJavaFileForOutput(JavaFileManager.Location location, String className,
                                               JavaFileObject.Kind kind, FileObject sibling) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        classFiles.put(className, baos);

        return new SimpleJavaFileObject(URI.create("string:///" + className.replace('.', '/') + kind.extension), kind) {
            @Override
            public OutputStream openOutputStream() {
                return baos;
            }
        };
    }

    /**
     * Returns all compiled class bytes stored in memory.
     *
     * @return a map of class names to their compiled byte arrays
     */
    public Map<String, byte[]> getAllClassBytes() {
        Map<String, byte[]> byteCodeMap = new HashMap<>();
        for (Map.Entry<String, ByteArrayOutputStream> entry : classFiles.entrySet()) {
            byteCodeMap.put(entry.getKey(), entry.getValue().toByteArray());
        }
        return byteCodeMap;
    }
}