package com.enttribe.promptanalyzer.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * An entity class that represents individual messages in an agent's conversation history.
 * Stores both user and assistant messages along with temporal information and maintains
 * a relationship with the parent AgentHistory.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@Entity
@Table(name = "AGENT_MESSAGE")
@ToString(exclude = {"agentHistory", "timeStamp"})
public class AgentMessage {

    /**
     * Unique identifier for the message record.
     * Auto-generated integer primary key.
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition = "INT")
    private Integer id;

    /**
     * User's message content.
     */
    @Column(name = "USER_MESSAGE", columnDefinition = "VARCHAR(1500)")
    private String userMessage;

    /**
     * Assistant's response message.
     */
    @Column(name = "ASSISTANT_MESSAGE", columnDefinition = "VARCHAR(6000)")
    private String assistantMessage;

    /**
     * Parent agent history reference.
     * Many-to-one relationship, excluded from JSON.
     */
    @ManyToOne
    @JoinColumn(name = "AGENT_HISTORY_ID")
    @JsonIgnore
    private AgentHistory agentHistory;

    /**
     * Message creation timestamp.
     */
    @Column(name = "TIME_STAMP", columnDefinition = "BIGINT")
    private Long timeStamp;

}
