package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * REST client interface for tag management operations.
 * Provides CRUD operations for managing tags through Feign client.
 * Author: VisionWaves
 * Version: 1.0
 */
@FeignClient(name = "TagRest", url = "${prompt-analyzer-service.url}", path = "/tag", primary = false)
public interface TagRest {

    /**
     * Searches tags with pagination and filtering.
     * Requires TAG_READ role.
     *
     * @param filter optional search criteria
     * @param offset pagination start index
     * @param size items per page
     * @param orderBy sort field
     * @param orderType sort direction (asc/desc)
     * @return list of matching tags
     */
    @Operation(
            summary = "Search for tags",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TAG_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Tags retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/search")
    List<TagResponseDto> search(
            @RequestParam(required = false) String filter,
            @RequestParam(required = true) Integer offset,
            @RequestParam(required = true) Integer size,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderType);

    /**
     * Counts total tags matching the filter.
     * Requires TAG_READ role.
     *
     * @param filter optional search criteria
     * @return total count of matching tags
     */
    @Operation(
            summary = "Count matching tags",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TAG_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Count retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/count")
    Long count(@RequestParam(required = false) String filter);

    /**
     * Creates a new tag.
     * Requires TAG_WRITE role.
     *
     * @param dto tag creation details
     * @return operation result map
     */
    @Operation(
            summary = "Create a new tag",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TAG_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Tag created successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/create", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> save(@RequestBody TagRequestDto dto);

    /**
     * Deletes a tag by its ID.
     * Requires TAG_WRITE role.
     *
     * @param id tag identifier
     * @return operation result map
     */
    @Operation(
            summary = "Delete a tag by ID",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TAG_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Tag deleted successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/deleteById/{id}")
    Map<String, String> deleteById(@PathVariable Integer id);

    /**
     * Updates an existing tag.
     * Requires TAG_WRITE role.
     *
     * @param dto updated tag details
     * @return operation result map
     */
    @Operation(
            summary = "Update an existing tag",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TAG_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Tag updated successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/update", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> update(@RequestBody TagRequestDto dto);
}
