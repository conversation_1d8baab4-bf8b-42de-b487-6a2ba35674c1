package com.enttribe.promptanalyzer.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Entity representing a message within a conversation.
 * Stores content, role, and association with a prompt.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Entity
@Table(name = "MESSAGES")
@Getter
@Setter
@NoArgsConstructor
public class Message {

    /**
     * Unique ID of the message.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    @JsonIgnore
    private Long id;

    /**
     * Content of the message.
     */
    @Column(name = "CONTENT", columnDefinition = "LONGTEXT")
    private String content;

    /**
     * Role of the sender (e.g., user, system).
     */
    @Column(name = "ROLE")
    private String role;

    /**
     * Associated prompt for the message.
     */
    @ManyToOne
    @JoinColumn(name = "PROMPT_ID")
    @JsonIgnore
    private Prompt prompt;


}

