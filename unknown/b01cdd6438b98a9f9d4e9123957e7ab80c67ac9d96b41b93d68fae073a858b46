package com.enttribe.promptanalyzer.dto.prompt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * A Data Transfer Object (DTO) that manages provider and model associations.
 * Handles the relationship between providers and their available models.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProviderModelDto {

    private String provider;
    private List<ProviderModel> models;

    public ProviderModelDto(String provider, List<ProviderModel> models) {
        this.provider = provider;
        this.models = models;
    }

    public record ProviderModel(String provider, String model) {}
}
