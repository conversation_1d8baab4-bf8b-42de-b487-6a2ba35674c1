package com.enttribe.commons.ai.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to indicate that a record component should be validated as a JSON body.
 * Allows specifying a JSON schema for validation purposes.
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Target(ElementType.RECORD_COMPONENT)
@Retention(RetentionPolicy.RUNTIME)
public @interface Body {
    String value() default "{}"; // JSON schema for validation
}
