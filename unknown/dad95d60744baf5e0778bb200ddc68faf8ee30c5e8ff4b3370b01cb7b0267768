package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.QueryDao;
import com.enttribe.promptanalyzer.dto.query.QueryRequestDto;
import com.enttribe.promptanalyzer.dto.query.QueryResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Query;
import com.enttribe.promptanalyzer.service.QueryService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.QueryConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Implementation of the {@link QueryService} interface.
 * This class provides the actual business logic for managing Query.
 * for a specific application. It interacts with the data access layer to fetch and modify Query data.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class QueryServiceImpl implements QueryService {

    private final CustomFilter customFilter;
    private final QueryDao queryDao;

    /**
     * Searches for queries based on the given filter and pagination parameters.
     *
     * @param filter the search filter criteria to apply to the queries
     * @param offset the offset for pagination
     * @param size the size (limit) for pagination
     * @param orderBy the field by which to order the results
     * @param orderType the type of ordering (ascending or descending)
     * @return a list of Query objects representing the found queries
     * @throws BusinessException if an error occurs while searching for queries
     */
    @Override
    public List<QueryResponseDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        try {
            log.debug("Searching queries with filter: {}, offset: {}, size: {}, orderBy: {}, orderType: {}", filter, offset, size, orderBy, orderType);
            List<Query> queries = customFilter.searchByFilter(Query.class, filter, orderBy, orderType, offset, size);
            log.debug("Found {} queries", queries.size());
            return QueryConverter.getQueryDtoList(queries);
        } catch (Exception e) {
            log.error("Error while searching for queries: {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }

    /**
     * Counts the number of queries that match the given filter.
     *
     * @param filter the search filter criteria to apply to the queries
     * @return the total number of queries matching the filter
     */
    @Override
    public Long count(String filter) {
        return customFilter.countByFilter(Query.class, filter);
    }

    /**
     * Creates a new query entry in the database based on the provided DTO.
     *
     * @param dto the DTO containing the details of the query to create
     * @return a map indicating the result of the creation process ("status": "success" or "failed")
     * @throws BusinessException if an error occurs while saving the query
     */
    @Override
    public Map<String, String> createQuery(QueryRequestDto dto) {
        log.debug("Inside createQuery method with question: {}", dto.getQuestion());
        Map<String, String> response = new HashMap<>();
        try {
            Query query = new Query();
            query.setQuestion(dto.getQuestion());
            query.setUserId(dto.getUserId());
            query.setType(dto.getType());
            query.setCreatedTime(new Date());
            queryDao.save(query);
            response.put("status", "success");
            log.info("Query created successfully with ID: {}", query.getId());
        } catch (Exception e) {
            log.error("Unable to save Query: {}", e.getMessage(), e);
            throw new BusinessException("Unable to save Query: " + e.getMessage());
        }
        return response;
    }

    /**
     * Marks a query as deleted (soft delete) by setting its `deleted` flag to true.
     *
     * @param id the ID of the query to softly delete
     * @return a map indicating the result of the soft delete operation ("result": "success" or "failed")
     */
    @Override
    public Map<String, String> softDelete(Integer id) {
        log.debug("Attempting to soft delete query with ID: {}", id);
        Map<String, String> result = new HashMap<>();
        Optional<Query> query = queryDao.findById(id);
        if (query.isPresent()) {
            Query query1 = query.get();
            query1.setDeleted(true);
            queryDao.save(query1);
            log.info("Query with ID {} marked as deleted", id);
            result.put("result", "success");
        } else {
            log.warn("Query with ID {} not found for deletion", id);
            result.put("result", "failed");
        }
        return result;
    }


}
