package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.ProviderModelDto;
import com.enttribe.promptanalyzer.model.LlmModel;

import java.util.List;
import java.util.Map;

/**
 * Manages Language Learning Model (LLM) configurations and operations.
 * This service handles model management across different providers,
 * including model creation, retrieval, and SDK-specific operations.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface LlmModelService {

    ProviderModelDto getModelsByProvider(String provider) ;

    List<ProviderModelDto> getAllProvidersWithModels();

    Map<String, String> create(LlmModel llmmodel);

    List<LlmModelSdkDto> getUniqueInferencesByType(String type);

    List<LlmModelSdkDto> getLlmModelsForSDK(String appName, String type);

    List<LlmModelSdkDto> getLlmModelsByTypeForSDK(String type);

}
