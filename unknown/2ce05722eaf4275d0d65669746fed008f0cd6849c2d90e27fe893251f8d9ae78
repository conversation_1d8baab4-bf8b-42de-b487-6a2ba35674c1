package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.KnowledgeBaseDao;
import com.enttribe.promptanalyzer.dto.knowledge_base.VectorResponseDto;
import com.enttribe.promptanalyzer.dto.crawl.CrawlResponse;
import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.service.CrawlerService;
import com.enttribe.promptanalyzer.service.KnowledgeBaseService;
import com.enttribe.promptanalyzer.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class CrawlerServiceImpl implements CrawlerService {

    private static final Logger log = LoggerFactory.getLogger(CrawlerServiceImpl.class);

    private final ApiService apiService;
    private final KnowledgeBaseDao knowledgeBaseDao;
    private final KnowledgeBaseService knowledgeBaseService;

    @Override
    public void processWebSiteKB() {
        log.info("inside @method processWebSiteKB");
        List<KnowledgeBase> knowledgeBases = knowledgeBaseDao.getWebsiteTypeUnprocessedKB();
        log.debug("total {} available for processing", knowledgeBases.size());
        for (KnowledgeBase knowledgeBase : knowledgeBases) {
            try {
                String taskId = knowledgeBase.getWebsiteTaskId();
                log.debug("getting status for taskId : {}", taskId);

                CrawlResponse crawlResponse = apiService.getTaskStatus(taskId);
                if (crawlResponse.getError() != null) {
                    log.warn("Error received for task {}: {}", taskId, crawlResponse.getError());
                    knowledgeBase.setWebsiteTaskStatus("PROCESSING");
                    knowledgeBase.setWebsiteTaskError(crawlResponse.getError());
                    String newTaskId = apiService.triggerCrawl(knowledgeBase.getWebSiteUrl());
                    log.info("Triggered new crawl for website {}: new task ID {}", knowledgeBase.getWebSiteUrl(), newTaskId);
                    knowledgeBase.setWebsiteTaskId(newTaskId);
                    knowledgeBaseDao.save(knowledgeBase);
                } else if (crawlResponse.getStatus().equals("completed")) {
                    log.info("Task {} completed for website {}", taskId, knowledgeBase.getWebSiteUrl());
                    String markdown = crawlResponse.getResult().markdown();
                    VectorResponseDto vectorResponse = knowledgeBaseService.saveInVector(List.of(markdown));
                    log.debug("Document saved in vector with filter: {}", vectorResponse.getFilter());
                    knowledgeBase.setFilter(vectorResponse.getFilter());
                    knowledgeBase.setDocMetaData(JsonUtils.convertToJSON(vectorResponse.getMetadata()));
                    knowledgeBase.setDocId(JsonUtils.convertToJSON(vectorResponse.getDocIds()));
                    knowledgeBase.setWebsiteTaskStatus("COMPLETED");
                    knowledgeBaseDao.save(knowledgeBase);
                } else {
                    log.debug("task status is {} for website {}", crawlResponse.getStatus(), knowledgeBase.getWebSiteUrl());
                }
            } catch (Exception e) {
                log.error("error while processing document knowledge base : {}", e.getMessage(), e);
            }
        }
    }
}