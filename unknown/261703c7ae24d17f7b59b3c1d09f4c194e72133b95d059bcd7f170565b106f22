package com.enttribe.promptanalyzer.dto.tool;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * A Data Transfer Object (DTO) that handles tool conversion operations and configurations.
 * Provides a comprehensive structure for tool data transformation and management,
 * including bytecode handling and tool metadata.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ToolConvertorDto {

    private Integer id;
    private String applicationName;
    private String category;
    private String name;
    private String displayName;
    private String version;
    private String toolId;
    private String language;
    private String type;
    private String sourceCode;
    private String description;
    private String tags;
    private String className;
    private String requestType;
    private String status;
    private String toolImage;
    private Map<String, byte[]> byteCodeMap;
    private Long agentId;
    private String apiTool;
    private Boolean returnDirect;

}
