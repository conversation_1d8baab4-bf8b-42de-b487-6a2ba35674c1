package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.Tag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for Tag entity operations.
 * Extends JpaRepository to provide standard CRUD operations and pagination support for Tag entities.
 * 
 * @see Tag
 * @see JpaRepository
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface TagDao extends JpaRepository<Tag, Integer> {
}
