package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import com.enttribe.promptanalyzer.model.Trigger;

import java.util.List;

/**
 * Utility class for converting Trigger entities to TriggerResponseDto objects.
 * Provides methods to convert individual entities and lists of entities.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public final class TriggerConverter {

    // Private constructor to prevent instantiation
    private TriggerConverter() {
    }

    /**
     * Converts a list of Trigger entities to a list of TriggerResponseDto objects.
     *
     * @param triggers the list of Trigger entities to convert
     * @return a list of converted TriggerResponseDto objects
     */
    public static List<TriggerResponseDto> getTriggerDtoList(List<Trigger> triggers) {
        return triggers.stream()
                .map(TriggerConverter::convertToTriggerDto)
                .toList();
    }

    /**
     * Converts a single Trigger entity to a TriggerResponseDto object.
     *
     * @param trigger the Trigger entity to convert
     * @return the converted TriggerResponseDto object
     */
    public static TriggerResponseDto convertToTriggerDto(Trigger trigger) {
        return TriggerResponseDto.builder()
                .id(trigger.getId())
                .name(trigger.getName())
                .displayName(trigger.getDisplayName())
                .description(trigger.getDescription())
                .metadata(trigger.getMetadata())
                .application(trigger.getApplication())
                .entity(trigger.getEntity())
                .type(trigger.getType())
                .build();
    }
}

