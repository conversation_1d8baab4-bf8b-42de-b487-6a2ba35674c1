package com.enttribe.promptanalyzer.model;

import jakarta.persistence.*;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

/**
 * Entity class representing an Agent in the system.
 * Extends BaseAgent to inherit common agent properties.
 * Manages the many-to-many relationship between agents and their tools.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Setter
@Getter
@Entity
@Table(name = "AGENT")
public class Agent extends BaseAgent {

    /**
     * Unique identifier for the agent.
     * Auto-generated using identity strategy.
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition = "INT")
    private Long id;

    /**
     * Set of tools associated with this agent.
     * Represents a many-to-many relationship with Tool entities.
     * <PERSON><PERSON> fetched to optimize performance.
     * Managed through the AGENT_TOOL join table.
     */
    @ManyToMany(targetEntity = Tool.class, fetch = FetchType.LAZY)
    @JoinTable(name = "AGENT_TOOL",
            joinColumns = @JoinColumn(name = "AGENT_TOOL_ID"),
            inverseJoinColumns = @JoinColumn(name = "TOOL_ID"))
    private Set<Tool> tools = new HashSet<>();



}
