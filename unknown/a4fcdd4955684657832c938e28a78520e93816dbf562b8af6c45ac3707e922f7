package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.TestCaseRequestDto;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.TestCase;

import java.util.Date;
import java.util.List;

/**
 * Utility class for converting TestCase entities to TestCaseRequestDto objects and vice versa.
 * Provides methods to convert individual entities and lists of entities.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public final class TestCaseUtils {

    // Private constructor to prevent instantiation
    private TestCaseUtils() {
    }

    /**
     * Converts a TestCase entity to a TestCaseRequestDto object.
     *
     * @param testcase the TestCase entity to convert
     * @return the converted TestCaseRequestDto object
     */
    public static TestCaseRequestDto getTestCaseDto(TestCase testcase) {
        return TestCaseRequestDto.builder()
                .id(testcase.getId())
                .testcaseId(testcase.getTestcaseId())
                .assertions(testcase.getAssertions())
                .inputJson(testcase.getInputJson())
                .remark(testcase.getRemark())
                .prompt(PromptConvertor.getPromptDto(testcase.getPrompt()))
                .build();
    }

    /**
     * Converts a TestCaseRequestDto object to a TestCase entity.
     *
     * @param dto the TestCaseRequestDto object to convert
     * @return the converted TestCase entity
     */
    public static TestCase getTestCase(TestCaseRequestDto dto) {
        PromptConvertorDto promptDto = dto.getPrompt();
        Prompt prompt = null;
        if (promptDto != null && promptDto.getId() != null) {
            prompt = new Prompt();
            prompt.setId(promptDto.getId());
        }
        return TestCase.builder()
                .id(dto.getId())
                .assertions(dto.getAssertions())
                .inputJson(dto.getInputJson())
                .remark(dto.getRemark())
                .prompt(prompt)
                .modifiedTime(new Date())
                .build();
    }

    /**
     * Converts a list of TestCase entities to a list of TestCaseRequestDto objects.
     *
     * @param testcases the list of TestCase entities to convert
     * @return a list of converted TestCaseRequestDto objects
     */
    public static List<TestCaseRequestDto> getTestCaseDtoList(List<TestCase> testcases) {
        return testcases.stream().map(TestCaseUtils::getTestCaseDto).toList();
    }

}
