package com.enttribe.promptanalyzer.ai.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * This class represents a data transfer object for chat completion requests, 
 * containing parameters for generating chat responses.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChatCompletionRequestDto {

    /**
     * The maximum number of tokens to generate in the response.
     */
    private Integer maxTokens;

    /**
     * A list of messages to provide context for the chat completion.
     */
    private List<MessageDto> messages;

    /**
     * The model to use for generating the chat response.
     */
    private String model;

    /**
     * Controls the randomness of the output. Lower values make the output more deterministic.
     */
    private Double temperature;

    /**
     * Controls the diversity of the output using nucleus sampling.
     */
    private Double topP;

    /**
     * Indicates whether the response should be in JSON format.
     */
    private Boolean jsonMode;

    /**
     * The provider of the chat completion service.
     */
    private String provider;

}