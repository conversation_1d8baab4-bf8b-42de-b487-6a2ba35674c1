package com.enttribe.promptanalyzer.exception;

/**
 * Exception thrown to indicate a business logic error.
 * Extends {@link RuntimeException} to provide unchecked exception handling for business-related errors.
 * <AUTHOR>
 * @version 1.0
 */
public class BusinessException extends RuntimeException {

    /**
     * Constructs a new BusinessException with the specified detail message.
     *
     * @param message the detail message
     */
    public BusinessException(String message) {
        super(message);
    }

    /**
     * Constructs a new BusinessException with the specified detail message and cause.
     *
     * @param message the detail message
     * @param cause the cause of the exception
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

}