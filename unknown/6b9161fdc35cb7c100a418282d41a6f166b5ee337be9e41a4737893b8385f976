package com.enttribe.promptanalyzer.dto.tool;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * A Data Transfer Object (DTO) that represents API tool configuration and details.
 * Handles API endpoint information, method specifications, and request configurations.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApiToolDto {

    private String url;
    private String method;
    private String headers; //stringified json map
    private String requestBody; //stringfied

}
