package com.enttribe.promptanalyzer.dto.prompt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.Map;

/**
 * A Data Transfer Object (DTO) that captures audit information for prompt executions.
 * Tracks performance metrics, token usage, and execution details for analysis and monitoring.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PromptAuditDto {

    private String id;
    private String model;
    private String responseText;
    private Long totalToken;
    private Long promptToken;
    private Double responseTime;
    private Long generationTokens;
    private String applicationName;
    private String category;
    private String promptId;
    private Map<String, String> metaTags;
    private Map<String, Object> chatOptions;
    private String functionArgs;  //This is for Agentic audit
    private String auditId;
    private String promptName;
    private Integer httpStatus;
    private String requestText;
    private String status;
    private String errorMessage;
    private String provider;
    private Date creationTime;
    private String agentName;
    private double totalCost;

}
