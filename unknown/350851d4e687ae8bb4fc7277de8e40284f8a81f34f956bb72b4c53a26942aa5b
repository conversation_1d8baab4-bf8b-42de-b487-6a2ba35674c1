package com.enttribe.promptanalyzer.model;


import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Entity representing a test case.
 * Stores details about test cases, including input data, assertions, and associations with test plans and prompts.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "TESTCASE")
public class TestCase {

    /**
     * Unique ID of the test case.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    /**
     * Unique identifier for the test case.
     */
    @Column(name = "TESTCASE_ID")
    private String testcaseId;

    /**
     * Indicates if the test case is marked as deleted.
     */
    @Column(name = "DELETED")
    private Boolean deleted = false;

    /**
     * JSON input data for the test case.
     */
    @Column(name = "INPUT_JSON", columnDefinition = "TEXT")
    private String inputJson;

    /**
     * Remarks or comments about the test case.
     */
    @Column(name = "REMARK", columnDefinition = "TEXT")
    private String remark;

    /**
     * Assertions for validating the test case.
     */
    @Column(name = "ASSERTIONS", columnDefinition = "TEXT")
    private String assertions;

    /**
     * Test plans associated with the test case.
     */
    @ManyToMany
    @JoinTable(name = "TEST_PLAN_TEST", joinColumns = @JoinColumn(name = "TEST_ID"),
            inverseJoinColumns = @JoinColumn(name = "TEST_PLAN_ID"))
    private Set<TestPlan> testPlans = new HashSet<>();

    /**
     * Associated prompt for the test case.
     */
    @ManyToOne
    @JoinColumn(name = "PROMPT_ID")
    private Prompt prompt;

    /**
     * Timestamp when the test case was created.
     */
    @Column(name = "CREATED_TIME")
    private Date createdTime;

    /**
     * Timestamp when the test case was last modified.
     */
    @Column(name = "MODIFIED_TIME")
    private Date modifiedTime;

}
