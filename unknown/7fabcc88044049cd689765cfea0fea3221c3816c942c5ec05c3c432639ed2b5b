package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.McpServer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for McpServer entity operations.
 * Provides methods to interact with the MCP_SERVER table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 * @see McpServer
 * @see JpaRepository
 */
@Repository
public interface McpServerDao extends JpaRepository<McpServer, Integer> {

}
