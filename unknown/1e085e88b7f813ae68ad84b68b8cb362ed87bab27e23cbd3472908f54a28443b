package com.enttribe.promptanalyzer.rest.impl;


import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import com.enttribe.promptanalyzer.rest.ProcessorRest;
import com.enttribe.promptanalyzer.service.ProcessorService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;


/**
 * REST controller implementation for processor-related operations.
 * Handles HTTP requests for saving, updating, searching, and counting processors.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/processor")
@RequiredArgsConstructor
public class ProcessorRestImpl implements ProcessorRest {

    private final ProcessorService processorService;

    /**
     * Saves a new processor.
     *
     * @param requestDto The processor data to be saved
     * @return A map containing the result of the save operation
     */
    @Override
    public Map<String, Object> save(ProcessorRequestDto requestDto) {
        log.info("Saving processor");
        return processorService.save(requestDto);
    }

    /**
     * Updates an existing processor by ID.
     *
     * @param id The ID of the processor to update
     * @param requestDto The updated processor data
     * @return A map containing the result of the update operation
     */
    @Override
    public Map<String, Object> update(Integer id, ProcessorRequestDto requestDto) {
        log.info("Updating processor with ID: {}", id);
        return processorService.update(id, requestDto);
    }

    /**
     * Searches for processors based on specified criteria.
     *
     * @param filter Optional search filter
     * @param offset Required pagination offset
     * @param size Required pagination size
     * @param orderBy Optional field to order results by
     * @param orderType Optional order direction (e.g., "ASC" or "DESC")
     * @return List of processors matching the search criteria
     */
    @Override
    public List<ProcessorResponseDto> search(
            String filter,
            Integer offset,
            Integer size,
            String orderBy,
            String orderType) {
        log.info("Searching processors");
        return processorService.search(filter, offset, size, orderBy, orderType);
    }

    /**
     * Counts the total number of processors matching the filter criteria.
     *
     * @param filter Optional search filter
     * @return The total count of matching processors
     */
    @Override
    public Long count(String filter) {
        return processorService.count(filter);
    }


}
