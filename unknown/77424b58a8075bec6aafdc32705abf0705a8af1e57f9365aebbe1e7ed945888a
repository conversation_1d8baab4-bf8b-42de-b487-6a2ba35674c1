package com.enttribe.promptanalyzer.dto.agent;

import lombok.*;

/**
 * A Data Transfer Object (DTO) class that represents agent conversion data.
 * Used for transferring agent-related information between different layers of the application.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AgentConvertDto {
    private Long id;
    private String name;


}
