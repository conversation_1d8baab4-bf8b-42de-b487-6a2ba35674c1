{"1": "{\"name\": \"OnNewEmail\",\"description\": \"Triggered when an email is received.\",\"package\": \"com.enttribe.custom.trigger.OnNewEmail\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"email id of user to look for new mail\",\"required\":true}]}", "2": "{\"name\": \"OnTicketAssign\",\"description\": \"Triggered when a ticket is assigned to someone.\",\"package\": \"com.enttribe.custom.trigger.OnTicketAssign\",\"properties\":[{\"parameterName\":\"Assignee\",\"parameterDescription\":\"Assignee of the ticket\",\"required\":true}]}", "3": "{\"name\": \"Recurring\",\"description\": \"Triggered every time at fixed time interval\",\"package\": \"com.enttribe.custom.trigger.Recurring\",\"properties\": [    {\"parameterName\":\"Triggering frequency\",\"parameterDescription\":\"The time unit for the interval (e.g., SECONDS, MINUTES, HOURS, DAYS)\",\"required\":true},    {\"parameterName\":\"Triggering interval\",\"parameterDescription\":\"The interval value in the specified time unit\",\"required\":true}]}", "4": "{\"name\": \"OnProjectDelay\",\"description\": \"Triggered when a project delivery exceeds the specified end date.\",\"package\": \"com.enttribe.custom.trigger.OnProjectDelay\",\"properties\" : [{\"parameterName\":\"ACCESS TOKEN\",\"parameterDescription\":\"The API access token\",\"required\":true}]}", "5": "{ \"name\": \"OnTicketDelay\", \"description\":\"Triggered when a ticket remains open for more than 3 days.\", \"package\":\"com.enttribe.custom.trigger.OnTicketDelay\" }", "6": "{\"name\": \"OnTaskDelay\",\"description\":\"Triggered when a task remains incomplete beyond its due date.\",\"package\":\"com.enttribe.custom.trigger.OnTaskDelay\"}", "7": "{\"name\": \"OnTicketResolve\",\"description\":\"Triggered when a ticket is marked as resolved.\",\"package\":\"com.enttribe.custom.trigger.OnTicketResolve\"}", "8": "{\"name\": \"OnBudgetExceed\",\"description\":\"Triggered when project expenses exceed 90% of the allocated budget.\",\"package\":\"com.enttribe.custom.trigger.OnBudgetExceed\"}", "11": "{\"name\": \"TicketCreate\",\"description\": \"Creates a ticket in system based on provided details\",\"package\":\"com.enttribe.custom.action.TicketCreate\"}", "12": "{\"name\": \"LeadCreate\",\"description\": \"Creates a crm lead based on provided details\",\"description\": \"Create a Lead\",\"package\":\"com.enttribe.custom.action.LeadCreate\"}", "13": "{\"name\": \"InvokeVoiceAgent\",\"description\": \"Invokes a phone call to user mobile number\",\"package\":\"com.enttribe.custom.action.InvokeVoiceAgent\",\"properties\" : [{\"parameterName\":\"Phone Number\",\"parameterDescription\":\"The phone number to call\",\"required\":true}]}", "14": "{\"name\": \"InvokeAgent\",\"description\": \"Invokes a agent based on its name. This AI Agent is capable of scheduling event/meeting, send email etc\",\"package\":\"com.enttribe.custom.action.InvokeAgent\",\"properties\" : [{\"parameterName\":\"Agent Name\",\"parameterDescription\":\"The name of the agent to invoke\",\"required\":true}]}", "15": "{\"name\": \"OnTaskCreate\",\"description\": \"The OnTaskCreate processor listens for new tasks being created in a system. It works like a server that waits for task creation events, processes details like task name, project, and priority, and then passes this information for further actions. It supports both HTTP and HTTPS (with security features like SSL) and helps in automating workflows based on new tasks.\",\"package\":\"com.enttribe.custom.trigger.OnTaskCreate\"}", "16": "{\"name\": \"OnTaskAssign\",\"description\": \"The OnTaskAssign processor is triggered whenever a task is assigned to a trainee. It helps automate actions like notifications, tracking, and workflow updates based on task assignments.\",\"package\":\"com.enttribe.custom.trigger.OnTaskAssign\",\"properties\" : [{\"parameterName\":\"Assignee\",\"parameterDescription\":\"Assignee of the task\",\"required\":true}]}", "51": "{\"name\": \"OnTicketCreate\",\"package\":\"com.enttribe.custom.trigger.OnTicketCreate\",\"description\": \"The OnTicketCreate processor is triggered whenever a new ticket is created. It helps automate actions like sending notifications, updating records, or assigning tasks whenever a ticket is generated.\"}", "21": "{\"name\": \"ListTask\",\"package\":\"com.enttribe.custom.trigger.ListTask\",\"description\": \"Fetches a list of tasks, sorts them by modified time, and creates FlowFiles for the top 10 tasks.\",\"properties\" : [{\"parameterName\":\"API Endpoint\",\"parameterDescription\":\"The API URL to fetch tasks\",\"required\":true},{\"parameterName\":\"Task Status Filter\",\"parameterDescription\":\"Filter tasks based on their status.\",\"required\":true}]}", "34": "{\"name\": \"TicketAssign\",\"package\":\"com.enttribe.custom.action.TicketAssign\",\"description\": \"Assign a ticket to a user\"}", "35": "{\"name\": \"OutlookSendMail\",\"package\":\"com.enttribe.custom.action.OutlookSendMail\",\"description\": \"Sends emails using Microsoft Graph API for Outlook\",\"properties\" : [{\"parameterName\":\"From Address\",\"parameterDescription\":\"Email address to send from (must have appropriate permissions\",\"required\":true},{\"parameterName\":\"To Addresses\",\"parameterDescription\":\"Comma-separated list of recipient email addresses\",\"required\":true},{\"parameterName\":\"Email Subject\",\"parameterDescription\":\"Subject of the email\",\"required\":true},{\"parameterName\":\"Email Body\",\"parameterDescription\":\"Content of the email body\",\"required\":true},{\"parameterName\":\"Enable Attachment\",\"parameterDescription\":\"Enable/disable attachment processing from FlowFile\",\"required\":true}]}", "37": "{\"name\": \"CreateGoogleDriveFolder\",\"package\":\"com.enttribe.custom.processor.CreateGoogleDriveFolder\",\"description\": \"Creates a folder in Google Drive using Google Drive API\",\"properties\" : [{\"parameterName\":\"Folder Name\",\"parameterDescription\":\"Name of the folder to create in Google Drive\",\"required\":true}]}", "38": "{\"name\": \"DeleteGoogleDriveFolder\",\"package\":\"com.enttribe.custom.processor.DeleteGoogleDriveFolder\",\"description\": \"Deletes a folder in Google Drive using Google Drive API\",\"properties\" : [{\"parameterName\":\"Folder ID\",\"parameterDescription\":\"ID of the folder to delete in Google Drive\",\"required\":true}]}", "39": "{\"name\": \"ShareGoogleDriveFolder\",\"package\":\"com.enttribe.custom.processor.ShareGoogleDriveFolder\",\"description\": \"Shares a folder in Google Drive with a specified user or makes it public\",\"properties\" : [{\"parameterName\":\"Folder ID\",\"parameterDescription\":\"ID of the folder to share in Google Drive\",\"required\":true},{\"parameterName\":\"Role\",\"parameterDescription\":\"Permission role: reader, commenter, or writer\",\"required\":true},{\"parameterName\":\"Public Access\",\"parameterDescription\":\"If set to 'Yes', the folder will be publicly accessible\",\"required\":true}]}", "40": "{\"name\": \"CopyGoogleDriveFile\",\"package\":\"com.enttribe.custom.processor.CopyGoogleDriveFile\",\"description\": \"Copies a file in Google Drive\",\"properties\" : [{\"parameterName\":\"File ID\",\"parameterDescription\":\"ID of the file to copy in Google Drive\",\"required\":true}]}", "92": "{\"name\":\"CohereEmbeddings\",\"package\":\"com.enttribe.custom.processor.CohereEmbeddings\",\"description\":\"Generates text embeddings using Cohere's API for semantic analysis and similarity tasks.\",\"properties\":[{\"parameterName\":\"Cohere API Key\",\"parameterDescription\":\"API Key for accessing Cohere services\",\"required\":true},{\"parameterName\":\"Model Name\",\"parameterDescription\":\"The Cohere embedding model to use\",\"required\":true}]}", "90": "{\"name\":\"DeleteFileFromOpenAI\",\"package\":\"com.enttribe.custom.processor.DeleteFileFromOpenAI\",\"description\":\"Deletes specified files from OpenAI's storage using their File API via DELETE requests with a File ID.\",\"properties\":[{\"parameterName\":\"OPEN_AI_FILE_ID\",\"parameterDescription\":\"ID of the file to delete from OpenAI storage.\",\"required\":true},{\"parameterName\":\"OpenAI API Key\",\"parameterDescription\":\"The OpenAI API Key to use for the OpenAI model\",\"required\":true}]}", "74": "{\"name\":\"MistralAIEmbeddings\",\"package\":\"com.enttribe.custom.processor.MistralAIEmbeddings\",\"description\":\"This Processor generates text embeddings using MistralAI.\",\"properties\":[{\"parameterName\":\"Mistral API Key\",\"parameterDescription\":\"API Key for accessing MistralAI services\",\"required\":true},{\"parameterName\":\"Model\",\"parameterDescription\":\"The MistralAI embedding model to use\",\"required\":true}]}", "52": "{\"name\":\"OnTicketUpdate\",\"package\":\"com.enttribe.custom.trigger.OnTicketUpdate\",\"description\":\"The OnTicketUpdate NiFi processor listens for HTTP requests when tickets are updated, filtering updates based on configurable properties like priority, severity, and assignee. It converts incoming requests into NiFi FlowFiles, supports SSL/TLS, provides a health check endpoint, and ensures proper session management, running only on the primary node in a clustered environment.\",\"properties\":[{\"parameterName\":\"Priority\",\"parameterDescription\":\"Priority of the flow\",\"required\":true},{\"parameterName\":\"Severity\",\"parameterDescription\":\"Severity of the flow\",\"required\":true},{\"parameterName\":\"Title\",\"parameterDescription\":\"Title of the flow\",\"required\":true},{\"parameterName\":\"Category\",\"parameterDescription\":\"Category of the flow\",\"required\":true}]}", "53": "{\"name\":\"GoogleSearch\",\"package\":\"com.enttribe.custom.processor.GoogleSearch\",\"description\":\"The GoogleSearch NiFi processor performs a Google search using the Google Search API. It extracts a summary from the first search result and adds it as a FlowFile attribute.\",\"properties\":[{\"parameterName\":\"User question\",\"parameterDescription\":\"The search term for Google\",\"required\":true}]}", "54": "{\"name\":\"OpenAiPrompt\",\"package\":\"com.enttribe.custom.processor.OpenAIPrompt\",\"description\":\"The OpenAiPrompt processor integrates with OpenAI's API to generate text responses based on user-defined prompts. It sends system and user prompts to the OpenAI LLM and processes the model's response.\",\"properties\":[{\"parameterName\":\"Model\",\"parameterDescription\":\"The name of the OpenAI model to use\",\"required\":true},{\"parameterName\":\"API Key\",\"parameterDescription\":\"The OpenAI API Key to use for the OpenAI model\",\"required\":true},{\"parameterName\":\"User Prompt\",\"parameterDescription\":\"The user prompt/question to be sent to the LLM\",\"required\":true}]}", "55": "{\"name\":\"GroqAiPrompt\",\"package\":\"com.enttribe.custom.processor.GroqAIPrompt\",\"description\":\"The GroqAiPrompt NiFi processor is designed to generate text using GroqAI's large language models (LLMs). It allows users to send prompts to Groq's API and retrieve AI-generated responses.\",\"properties\":[{\"parameterName\":\"Model\",\"parameterDescription\":\"The name of the model to use\",\"required\":true},{\"parameterName\":\"API Key\",\"parameterDescription\":\"API Key for the Groq API\",\"required\":true},{\"parameterName\":\"User Prompt\",\"parameterDescription\":\"The user prompt/question to be sent to the LLM\",\"required\":true},{\"parameterName\":\"JSON Mode\",\"parameterDescription\":\"If true, forces the model to output valid JSON\",\"required\":true}]}", "96": "{\"name\":\"AIMLPromptAI\",\"package\":\"com.enttribe.custom.processor.AIMLPromptAI\",\"description\":\"Interacts with AIML APIs to generate AI-driven responses based on user prompts\",\"properties\":[{\"parameterName\":\"AIML API Key\",\"parameterDescription\":\"API Key for AIML API authentication.\",\"required\":true},{\"parameterName\":\"User Prompt\",\"parameterDescription\":\"The user prompt/question to be sent to the LLM\",\"required\":true},{\"parameterName\":\"Model Name\",\"parameterDescription\":\"The AIML model to use\",\"required\":true},{\"parameterName\":\"JSON Mode\",\"parameterDescription\":\"If true, forces the model to output valid JSON\",\"required\":true}]}", "95": "{\"name\":\"AI<PERSON><PERSON>mbedding\",\"package\":\"com.enttribe.custom.processor.AIMLEmbedding\",\"description\":\"Fetches text embeddings from an AI/ML API for machine learning-driven text processing tasks.\",\"properties\":[{\"parameterName\":\"AI/ML API Key\",\"parameterDescription\":\"API Key for authentication with the AI/ML API\",\"required\":true},{\"parameterName\":\"Model Name\",\"parameterDescription\":\"Choose the Al model for embedding generation\",\"required\":true}]}", "94": "{\"name\":\"ExecuteJavaCode\",\"package\":\"com.enttribe.custom.processor.ExecuteJavaCode\",\"description\":\"Executes custom Java code snippets at runtime with support for external JARs, database connections, and error handling.\",\"properties\":[{\"parameterName\":\"Class Name\",\"parameterDescription\":\"Class Name\",\"required\":true}]}", "56": "{\"name\":\"SplitDocument\",\"package\":\"com.enttribe.custom.processor.SplitDocument\",\"description\":\"The SplitDocument NiFi processor is designed to split a document into smaller chunks based on a specified mode and chunk size.\",\"properties\":[{\"parameterName\":\"Split Mode\",\"parameterDescription\":\"The mode to use when splitting by tokens (NEXT_LINE, PARAGRAPH, or RECURSIVE)\",\"required\":true}]}", "57": "{\"name\":\"OpenAIImageEdit\",\"package\":\"com.enttribe.custom.processor.OpenAIImageEdit\",\"description\":\"The OpenAI Image Edit Processor is a custom Apache NiFi processor designed to modify images using OpenAI's API. This processor takes an input image from a FlowFile, applies modifications based on a user-defined text prompt, and returns the edited image.\",\"properties\":[{\"parameterName\":\"Edit Prompt \",\"parameterDescription\":\"The prompt describing the desired image modifications\",\"required\":true},{\"parameterName\":\"Image Size\",\"parameterDescription\":\"The size of the output image\",\"required\":true},{\"parameterName\":\"Model\",\"parameterDescription\":\"OpenAI image edit model to use\",\"required\":true},{\"parameterName\":\"OpenAI API Key \",\"parameterDescription\":\"The prompt describing the desired image modifications\",\"required\":true}]}", "58": "{\"name\":\"YahooFinance\",\"package\":\"com.enttribe.custom.processor.YahooFinance\",\"description\":\"The YahooFinance NiFi processor fetches real-time stock market data from Yahoo Finance API  extracts key financial details and stores the structured data in FlowFile attributes for further processing.\",\"properties\":[{\"parameterName\":\"Yahoo Finance API Key \",\"parameterDescription\":\"Your API key for Yahoo Finance (RapidAPI)\",\"required\":true},{\"parameterName\":\"Stock Tickers\",\"parameterDescription\":\"Comma-separated stock tickers (e.g., AAPL,MSFT,GOOG)\",\"required\":true}]}", "59": "{\"name\":\"UploadFileToOpenAI\",\"package\":\"com.enttribe.custom.processor.UploadFileToOpenAI\",\"description\":\"The UploadFileToOpenAI NiFi processor uploads files to OpenAI’s API for use in assistants and other operations. It reads a file from a FlowFile, sends it as a multipart-form request to OpenAI’s file upload endpoint, and assigns a specified purpose (e.g., assistants). If the upload is successful, the processor extracts the file ID from OpenAI's response and adds it as a FlowFile attribute (openai.file.id), then routes the FlowFile to the success relationship. If the upload fails, it routes the FlowFile to failure. This processor facilitates seamless file integration with OpenAI's services.\",\"properties\":[{\"parameterName\":\"Purpose\",\"parameterDescription\":\"The intended purpose of the uploaded file (e.g., 'assistants')\",\"required\":true},{\"parameterName\":\"OpenAI API Key\",\"parameterDescription\":\"The OpenAI API Key to use for the OpenAI model\",\"required\":true}]}", "60": "{\"name\":\"UpdateOpenAIAssistant\",\"package\":\"com.enttribe.custom.processor.UpdateOpenAIAssistant\",\"description\":\"The UpdateOpenAIAssistant NiFi processor updates an existing OpenAI assistant with new details. It takes an Assistant ID and a JSON update payload, sends a PATCH request to OpenAI, and routes the FlowFile to success if the update is successful or failure otherwise.\",\"properties\":[{\"parameterName\":\"Assistant ID\",\"parameterDescription\":\"The ID of the assistant to update.\",\"required\":true},{\"parameterName\":\"Update Payload\",\"parameterDescription\":\"JSON payload specifying the updated assistant details.\",\"required\":true},{\"parameterName\":\"OpenAI API Key\",\"parameterDescription\":\"The API key for the OpenAI service.\",\"required\":true}]}", "61": "{\"name\":\"TemplateProcessor\",\"package\":\"com.enttribe.custom.processor.Template\",\"description\":\"The Template NiFi processor processes Jinja templates using flow file attributes as variables. It takes a Jinja template and variables from flow file attributes, renders the template using Java's Freemarker  , and outputs the result either to the FlowFile content or as an attribute (template.output).\",\"properties\":[{\"parameterName\":\"Template Content\",\"parameterDescription\":\"The Jinja template to process\",\"required\":true},{\"parameterName\":\"Output Destination\",\"parameterDescription\":\"Whether to write output to FlowFile content or attribute\",\"required\":true}]}", "62": "{\"name\":\"StartEvent\",\"package\":\"com.enttribe.custom.processor.StartEvent\",\"description\":\"The StartEvent processor generates FlowFiles with either random or custom-defined content, supporting batching, text/binary formats, and dynamic attributes. It is a source processor that triggers workflows, creates test data, or injects events. The processor supports unique or repeated content, sets MIME types, and logs provenance events.\",\"properties\":[{\"parameterName\":\"Custom Text\",\"parameterDescription\":\"Custom Text\",\"required\":true}]}", "63": "{\"name\":\"QuestionClassifier\",\"package\":\"com.enttribe.custom.processor.QuestionClassifier\",\"description\":\"The QuestionClassifier NiFi processor is designed to classify user-provided questions based on user defined categories and generate structured responses using an LLM (Large Language Model). It interacts with the AI API to analyze input text, classify it into a specified category, and return an appropriate response.\",\"properties\":[{\"parameterName\":\"Groq Model\",\"parameterDescription\":\"The LLM model to use (e.g., GPT-4, Claude, etc.)\",\"required\":true},{\"parameterName\":\"Groq API Key \",\"parameterDescription\":\"The API Key for authenticating requests to Groq AI API\",\"required\":true},{\"parameterName\":\"Input Variable\",\"parameterDescription\":\"The input variable to be sent to the LLM\",\"required\":true}]}", "64": "{\"name\":\"PutMilvus\",\"package\":\"com.enttribe.custom.processor.PutMilvus\",\"description\":\"The PutMilvus NiFi processor is designed to insert document data into a Milvus vector database. It converts text content into embedding vectors using the API and stores them in Milvus, along with metadata. This processor is useful for applications requiring semantic search, similarity matching, and AI-driven document retrieval.\",\"properties\":[{\"parameterName\":\"Milvus Host\",\"parameterDescription\":\"The hostname of the Milvus server\",\"required\":true},{\"parameterName\":\"Milvus Port\",\"parameterDescription\":\"The port of the Milvus server\",\"required\":true},{\"parameterName\":\"Model\",\"parameterDescription\":\"The model to use for embeddings (e.g., nomic-embed-text-v1_5-preview1)\",\"required\":true},{\"parameterName\":\"Collection Name\",\"parameterDescription\":\"The name of the Milvus collection to store documents\",\"required\":true}]}", "66": "{\"name\":\"ParameterExctractor\",\"package\":\"com.enttribe.custom.processor.ParameterExctractor\",\"description\":\"ParameterExtractor is a NiFi processor that extracts structured parameters from text using the Groq AI API, supporting JSON output, temperature control.\",\"properties\":[{\"parameterName\":\"Model\",\"parameterDescription\":\"The LLM model to use (e.g., GPT-4, Claude, etc.)\",\"required\":true},{\"parameterName\":\"Extract Parameters\",\"parameterDescription\":\"Comma-separated list of parameters in format: paramName:type (e.g., person:String, age:Integer, skills:Array)\",\"required\":true}]}", "71": "{\"name\":\"OpenAIAssistantDeletion\",\"package\":\"com.enttribe.custom.processor.OpenAIAssistantDeletion\",\"description\":\"The OpenAIAssistantDeletion NiFi processor is designed to delete an assistant using OpenAI's API. It takes an assistant ID as input, sends a DELETE request to OpenAI’s API, and removes the specified assistant. The processor ensures seamless integration with NiFi workflows for managing AI assistants.\",\"properties\":[{\"parameterName\":\"Assistant ID\",\"parameterDescription\":\"The ID of the assistant to delete\",\"required\":true},{\"parameterName\":\"OpenAI API Key\",\"parameterDescription\":\"The OpenAI API Key to use for the OpenAI model\",\"required\":true}]}", "72": "{\"name\":\"OpenAIAssistantCreation\",\"package\":\"com.enttribe.custom.processor.OpenAIAssistantCreation\",\"description\":\"The OpenAIAssistantCreation NiFi processor allows users to create an assistant using OpenAI's API. It sends a POST request to OpenAI's v1/assistants endpoint with user-defined parameters like name, description, model, and instructions. Upon successful creation, it returns the assistant's ID and other details.\",\"properties\":[{\"parameterName\":\"Assistant Name\",\"parameterDescription\":\"Name of the assistant\",\"required\":true},{\"parameterName\":\"Model\",\"parameterDescription\":\"OpenAI model to use for the assistant\",\"required\":true},{\"parameterName\":\"Instructions\",\"parameterDescription\":\"System instructions for the assistant\",\"required\":true},{\"parameterName\":\"OpenAI API Key\",\"parameterDescription\":\"The OpenAI API Key to use for the OpenAI model\",\"required\":true}]}", "67": "{\"name\":\"OpenAIMessageAssistant\",\"package\":\"com.enttribe.custom.processor.OpenAIMessageAssistant\",\"description\":\"The OpenAIMessageAssistant processor is a custom Apache NiFi processor that interacts with OpenAI's Assistant API. It allows users to send a message (prompt) to an OpenAI assistant and receive a response.\",\"properties\":[{\"parameterName\":\"Assistant ID\",\"parameterDescription\":\"The ID of Assistant ot message\",\"required\":true},{\"parameterName\":\"Prompt\",\"parameterDescription\":\"The message prompt to send to teh assistant\",\"required\":true},{\"parameterName\":\"OpenAI API Key\",\"parameterDescription\":\"The OpenAI API Key to use for Open AI Model\",\"required\":true}]}", "68": "{\"name\":\"OpenAIImageValidation\",\"package\":\"com.enttribe.custom.processor.OpenAIImageValidation\",\"description\":\"This NiFi processor validates passport photos using OpenAI's vision model (gpt-4-vision-preview). It accepts an image as input, converts it to a base64-encoded format, and sends it to OpenAI's API along with a user-defined validation prompt. The processor then analyzes the response and determines whether the image meets passport photo requirements.\",\"properties\":[{\"parameterName\":\"Validation Prompt\",\"parameterDescription\":\"Prompt for validating the passport photo\",\"required\":true},{\"parameterName\":\"OpenAI Model\",\"parameterDescription\":\"The Open AI model to use for image validation\",\"required\":true},{\"parameterName\":\"OpenAI API Key\",\"parameterDescription\":\"The OpenAI API Key to use for Open AI Model\",\"required\":true}]}", "69": "{\"name\":\"OpenAIImageGeneration\",\"package\":\"com.enttribe.custom.processor.OpenAIImageGeneration\",\"description\":\"The OpenAIImageGeneration NiFi processor is designed to generate images using OpenAI’s DALL·E models. It accepts a text prompt from the user, sends a request to OpenAI’s image generation API, retrieves the generated image, and outputs it as a FlowFile.\",\"properties\":[{\"parameterName\":\"Prompt\",\"parameterDescription\":\"format of the generated image(png or jpeg)\",\"required\":true},{\"parameterName\":\"Image Format \",\"parameterDescription\":\"The Open AI model to use for image validation\",\"required\":true},{\"parameterName\":\"Image Size\",\"parameterDescription\":\"Size of the generated image\",\"required\":true},{\"parameterName\":\"Model\",\"parameterDescription\":\"OpenAI image generation model to use\",\"required\":true},{\"parameterName\":\"OpenAI API Key\",\"parameterDescription\":\"The OpenAI key to use for the Open AI model\",\"required\":true}]}", "70": "{\"name\":\"OpenAIEmbeddings\",\"package\":\"com.enttribe.custom.processor.OpenAIEmbeddings\",\"description\":\"This Processor can generates text embeddings using OpenAI .\",\"properties\":[{\"parameterName\":\"OpenAI API Key\",\"parameterDescription\":\"API Key for Accesing Open AI services\",\"required\":true},{\"parameterName\":\"Model\",\"parameterDescription\":\"The Open AI Embedding model to use\",\"required\":true},{\"parameterName\":\"CONTENT\",\"parameterDescription\":\"The Content to use for embeddings\",\"required\":true}]}", "77": "{\"name\":\"KnowledgeRetrieval\",\"package\":\"com.enttribe.custom.processor.KnowledgeRetrieval\",\"description\":\"Retrieves relevant text content from the Dify Knowledge Base using the Dify API based on user-provided queries.\",\"properties\":[{\"parameterName\":\"Knowledge Base Name\",\"parameterDescription\":\"Name for you knowledge base\",\"required\":true},{\"parameterName\":\"User Question\",\"parameterDescription\":\"Question for user\",\"required\":true}]}", "78": "{\"name\":\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"package\":\"com.enttribe.custom.processor.JsonReplace\",\"description\":\"Modifies JSON content by replacing specific string values based on JSONPath queries.\",\"properties\":[{\"parameterName\":\"JSON Content\",\"parameterDescription\":\"JSON Content to be modified\",\"required\":true},{\"parameterName\":\"String to Replace\",\"parameterDescription\":\"The string value to be replaced in matched JSON fields\",\"required\":true},{\"parameterName\":\"Replacement Value \",\"parameterDescription\":\"The new value that will replaced the target string\",\"required\":true}]}", "79": "{\"name\":\"JsonInsert\",\"package\":\"com.enttribe.custom.processor.JsonInsert\",\"description\":\"Merges additional JSON content into an existing JSON object, updating or adding key-value pairs.\",\"properties\":[{\"parameterName\":\"JSON Content\",\"parameterDescription\":\"JSON Content to be processed\",\"required\":true},{\"parameterName\":\"Content to add\",\"parameterDescription\":\"JSON content to be add\",\"required\":true}]}", "80": "{\"name\":\"<PERSON>sonDele<PERSON>\",\"package\":\"com.enttribe.custom.processor.JsonDelete\",\"description\":\"This NiFi processor removes specified elements from JSON content using a JSONPath expression.\",\"properties\":[{\"parameterName\":\"JSON Content\",\"parameterDescription\":\"JSON Content to be processed\",\"required\":true},{\"parameterName\":\"Content to delete\",\"parameterDescription\":\"JSON content to be delete\",\"required\":true}]}", "81": "{\"name\":\"IFELSE\",\"package\":\"com.enttribe.custom.processor.IFELSE\",\"description\":\"Routes FlowFiles based on attribute conditions using NiFi Expression Language.\",\"properties\":[{\"parameterName\":\"First Input \",\"parameterDescription\":\"First value to be compare\",\"required\":true},{\"parameterName\":\"Second Input\",\"parameterDescription\":\"Second value to be compare\",\"required\":true},{\"parameterName\":\"Comparison Operator\",\"parameterDescription\":\"Operator to use for comparison\",\"required\":true}]}", "82": "{\"name\":\"HuggingFaceEmbedding\",\"package\":\"com.enttribe.custom.processor.HuggingFaceEmbedding\",\"description\":\"Creates text embeddings using Hugging Face's BAAI/bge-large-en-v1.5 model via their Inference API\",\"properties\":[{\"parameterName\":\"Hugging Face API Key\",\"parameterDescription\":\"First value to be compare\",\"required\":true},{\"parameterName\":\"Model Name\",\"parameterDescription\":\"Second value to be compare\",\"required\":true},{\"parameterName\":\"Content\",\"parameterDescription\":\"Content to use for embedding\",\"required\":true}]}", "83": "{\"name\":\"HuggingFaceAIPrompt\",\"package\":\"com.enttribe.custom.processor.HuggingFaceAIPrompt\",\"description\":\"Sends user input to Hugging Face AI API using an authentication token and retrieves generated output.\",\"properties\":[{\"parameterName\":\"API key\",\"parameterDescription\":\"Hugging face API token\",\"required\":true},{\"parameterName\":\"Model ID\",\"parameterDescription\":\"Hugging face model ID to use for text generation\",\"required\":true},{\"parameterName\":\"User Prompt\",\"parameterDescription\":\"The user prompt/question to be send to LLM\",\"required\":true},{\"parameterName\":\"Max New Tokens\",\"parameterDescription\":\"Maximum number of token to generate\",\"required\":true}]}", "84": "{\"name\":\"GroqImageValidation\",\"package\":\"com.enttribe.custom.processor.GroqImageValidation\",\"description\":\"Validates images (e.g., passport photos) using Groq AI API to check compliance with criteria like neutral expression, proper lighting, and background.\",\"properties\":[{\"parameterName\":\"Validation Prompt\",\"parameterDescription\":\"prompt for validating as passport photo\",\"required\":true},{\"parameterName\":\"Model Name\",\"parameterDescription\":\"Groq model to be use for imagE validation\",\"required\":true},{\"parameterName\":\"Groq API Key\",\"parameterDescription\":\"The Groq AI api key to use for Groq AI model\",\"required\":true}]}", "85": "{\"name\":\"FetchMilvus\",\"package\":\"com.enttribe.custom.processor.FetchMilvus\",\"description\":\"The FetchMilvus processor fetches data from a Milvus vector database based on search parameters. It performs vector-based searches using a provided query and retrieves similar records, returning results as JSON\",\"properties\":[{\"parameterName\":\"Milvus Host\",\"parameterDescription\":\"HOST address of milvus server\",\"required\":true},{\"parameterName\":\"Milvus Port\",\"parameterDescription\":\"Port number of milvus server\",\"required\":true},{\"parameterName\":\"Collection Name\",\"parameterDescription\":\"Name of milvus connection to search\",\"required\":true},{\"parameterName\":\"Top K Results\",\"parameterDescription\":\"Number of nearest neighbors to return\",\"required\":true},{\"parameterName\":\"Query\",\"parameterDescription\":\"Query to search for\",\"required\":true},{\"parameterName\":\"Model\",\"parameterDescription\":\"Model to use for embedding\",\"required\":true}]}", "86": "{\"name\":\"ExecutePythonScript\",\"package\":\"com.enttribe.custom.processor.ExecutePythonScript\",\"description\":\"Executes Python scripts from inline code, local files, or AWS S3 buckets. Supports dynamic script management in NiFi workflows with runtime execution.\",\"properties\":[{\"parameterName\":\"Python Command\",\"parameterDescription\":\"Choose which python interpreter\",\"required\":true}]}", "87": "{\"name\":\"EntitySummariser\",\"package\":\"com.enttribe.custom.processor.EntitySummariser\",\"description\":\"Summarizes entity information (emails/tickets/CRM leads) using LLM capabilities by processing entity names and objects\"}", "88": "{\"name\":\"EndEvent\",\"package\":\"com.enttribe.custom.processor.EndEvent\",\"description\":\"Isolates output variables from FlowFile attributes (output.*), creates a new FlowFile with these variables, and routes it to SUCCESS while removing the original.\"}", "89": "{\"name\":\"DocExtractor\",\"package\":\"com.enttribe.custom.processor.DocExtractor\",\"description\":\"Extracts text content from PDF, CSV, TXT, Excel (XLSX), and Word (DOCX) documents.\",\"properties\":[{\"parameterName\":\"File Type\",\"parameterDescription\":\"The file type of file to process(PDF,CSV,TEXT,EXCEL,DOCX, Or Other\",\"required\":true},{\"parameterName\":\"Output Attribute\",\"parameterDescription\":\"FlowFile attribute to store the extracted content\",\"required\":true}]}", "75": "{\"name\":\"ListFileFromOpenAI\",\"package\":\"com.enttribe.custom.processor.ListFileFromOpenAI\",\"description\":\"The ListFileFromOpenAI processor retrieves a list of files stored in OpenAI's file storage system for the user's organization.\",\"properties\":[{\"parameterName\":\"File Purpose\",\"parameterDescription\":\"Filters files for the purpose(eg.Assistants)\",\"required\":true},{\"parameterName\":\"OpenAI API Key\",\"parameterDescription\":\"Api key used for OpenAI model\",\"required\":true}]}", "73": "{\"name\":\"NovitaAIPrompt\",\"package\":\"com.enttribe.custom.processor.NovitaAIPrompt\",\"description\":\"Interacts with Novita AI models for text generation. Fetches available models and processes requests.\",\"properties\":[{\"parameterName\":\"Model\",\"parameterDescription\":\"Name of model to use\",\"required\":true},{\"parameterName\":\"API Key\",\"parameterDescription\":\"API key for novita\",\"required\":true},{\"parameterName\":\"User Prompt\",\"parameterDescription\":\"The user prompt/question to be sent to LLM\",\"required\":true}]}", "93": "{\"name\":\"CoherePromptAI\",\"package\":\"com.enttribe.custom.processor.CoherePromptAI\",\"description\":\"Generates AI-powered text responses using Cohere AI's language models via API integration.\",\"properties\":[{\"parameterName\":\"Cohere API Key\",\"parameterDescription\":\"The Cohere API Key to use for the Cohere model\",\"required\":true},{\"parameterName\":\"Max Output Tokens\",\"parameterDescription\":\"The maximum number of tokens to generate (must be a positive integer)\",\"required\":true},{\"parameterName\":\"User prompt\",\"parameterDescription\":\"User input/question that need to ask\",\"required\":true}]}", "42": "{\"name\":\"GetGoogleDriveFile\",\"package\":\"com.enttribe.custom.processor.GetGoogleDriveFile\",\"description\":\"The GetGoogleDriveFile processor is designed to retrieve file metadata from Google Drive using a given File ID. It connects to the Google Drive API and extracts key metadata information about the specified file, which is then stored as FlowFile attributes for further processing in NiFi.\",\"properties\":[{\"parameterName\":\"Google Credentials\",\"parameterDescription\":\"Path to Google OAuth credentials JSON\",\"required\":true},{\"parameterName\":\"File ID\",\"parameterDescription\":\"ID of the file to retrieve metadata from Google Drive\",\"required\":true}]}", "29": "{\"name\":\"OutlookFlagMail\",\"package\":\"com.enttribe.custom.action.OutlookFlagMail\",\"description\":\"Flags or unflags email messages based on specified email address and message ID\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"Email of the user whose calendar the event should be created in\",\"required\":true}]}", "27": "{\"name\":\"OutlooDraftMailReply\",\"package\":\"com.enttribe.custom.action.OutlookDraftMailReply\",\"description\":\"Creates a draft reply email for an existing message with specified content\",\"properties\":[{\"parameterName\":\"Subject\",\"parameterDescription\":\"Email subject for creating a draft email\",\"required\":true}]}", "24": "{\"name\":\"OutlookAcceptMeeting\",\"package\":\"com.enttribe.custom.action.OutlookAcceptMeeting\",\"description\":\"Accepts a calendar meeting invitation for a specified email address and event ID\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"Email address of user whose event should be created \",\"required\":true}]}", "25": "{\"name\":\"OutlookDeclineMeeting\",\"package\":\"com.enttribe.custom.action.OutlookDeclineMeeting\",\"description\":\"Declines a calendar meeting invitation for a specified email address and event ID\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"Email address of user whose event should be created \",\"required\":true}]}", "26": "{\"name\":\"OutlookDraftMail\",\"package\":\"com.enttribe.custom.action.OutlookDraftMail\",\"description\":\"Creates a draft email with specified recipient and content\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"Email address of user whose event should be created \",\"required\":true}]}", "97": "{\"name\":\"OutlookReadEvent\",\"package\":\"com.enttribe.custom.trigger.OutlookReadEvent\",\"description\":\"The OutlookReadEvent processor reads event from Microsoft Outlook using Graph API with optional subject and body from a specified Outlook user’s calendar.\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"Email address of user whose event should be created \",\"required\":true}]}", "41": "{\"name\":\"DeleteGoogleDriveFile\",\"package\":\"com.enttribe.custom.processor.DeleteGoogleDriveFolder\",\"description\":\"Deletes a file from Google Drive using the file ID.\",\"properties\":[{\"parameterName\":\"Google Credentials\",\"parameterDescription\":\"Path to Google OAuth credentials JSON\",\"required\":true},{\"parameterName\":\"File ID\",\"parameterDescription\":\"ID of the file to delete from Google Drive\",\"required\":true}]}", "43": "{\"name\":\"ListGoogleDriveFiles\",\"package\":\"com.enttribe.custom.processor.CoherePromptAI\",\"description\":\"Searches for all files within a Google Drive folder and retrieves their details.\",\"properties\":[{\"parameterName\":\"Google Credentials\",\"parameterDescription\":\"Path to Google OAuth credentials JSON\",\"required\":true},{\"parameterName\":\"File ID\",\"parameterDescription\":\"ID of the file to delete from Google Drive\",\"required\":true}]}", "44": "{\"name\":\"WikipediaSearch\",\"package\":\"com.enttribe.custom.processor.WikipediaSearch\",\"description\":\"Fetches Wikipedia content based on a search query.\",\"properties\":[{\"parameterName\":\"Query\",\"parameterDescription\":\"The search term for Wikipedia\",\"required\":true},{\"parameterName\":\"Language\",\"parameterDescription\":\"Select the Wikipedia language (e.g., English, Hindi, French)\",\"required\":true}]}", "28": "{\"name\":\"OutlookScheduleEvent\",\"package\":\"com.enttribe.custom.action.OutlookScheduleEvent\",\"description\":\"Creates and processes meeting scheduling events. Handles meeting details including organizer, start/end times, and required attendees to facilitate calendar scheduling operations.\",\"properties\":[{\"parameterName\":\"User Email \",\"parameterDescription\":\"Azure application client ID\",\"required\":true},{\"parameterName\":\"Event Subject\",\"parameterDescription\":\"Subject/title of the event\",\"required\":true},{\"parameterName\":\"Event Start Time \",\"parameterDescription\":\"Start time in ISO 8601 format (yyyy-MM-ddTHH:mm:ss)\",\"required\":true},{\"parameterName\":\"Event End Time\",\"parameterDescription\":\"End time in ISO 8601 format (yyyy-MM-ddTHH:mm:ss)\",\"required\":true},]}", "76": "{\"name\":\"ListAssistants\",\"package\":\"com.enttribe.custom.processor.ListAssistants\",\"description\":\"The ListAssistants NiFi processor retrieves a list of assistants from OpenAI using the /v1/assistants API.\",\"properties\":[{\"parameterName\":\"OpenAI API Key\",\"parameterDescription\":\"OpenAI API Key use for Open AI model\",\"required\":true}]}", "91": "{\"name\":\"DeepseekPrompt\",\"package\":\"com.enttribe.custom.processor.DeepseekPrompt\",\"description\":\"Generates text responses using Deepseek language models by sending prompts to their API.\",\"properties\":[{\"parameterName\":\"API Key\",\"parameterDescription\":\"Deepseek API Key use for Deepseek AI model\",\"required\":true},{\"parameterName\":\"User Prompt\",\"parameterDescription\":\"User prompt/question that need to send to LLM\",\"required\":true},{\"parameterName\":\"Model Name\",\"parameterDescription\":\"Name of deepseek model to use\",\"required\":true}]}", "45": "{\"name\":\"YahooSearch\",\"package\":\"com.enttribe.custom.processor.YahooSearch\",\"description\":\"Fetches search results from Yahoo using SerpAPI and extracts relevant fields.\",\"properties\":[{\"parameterName\":\"SerpAPI Key\",\"parameterDescription\":\"Your API key for SerpAPI\",\"required\":true},{\"parameterName\":\"Search Query\",\"parameterDescription\":\"The query string to search on Yahoo (e.g., 'coffee mug')\",\"required\":true}]}"}