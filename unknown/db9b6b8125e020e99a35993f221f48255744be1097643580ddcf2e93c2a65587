package com.enttribe.promptanalyzer.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents the response to a query request.
 * Extends QueryRequestDto with additional response-specific fields.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class QueryResponseDto {

    private Integer id;
    private String userId;
    private String type;
    private String question;
}
