package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.KnowledgeBase;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * Data Access Object interface for KnowledgeBase entity operations.
 * Extends JpaRepository to inherit standard database operations.
 *
 * <AUTHOR>
 * @version 1.0
 * @see KnowledgeBase
 * @see JpaRepository
 */
public interface KnowledgeBaseDao extends JpaRepository<KnowledgeBase, Integer> {

    /**
     * Retrieves a list of KnowledgeBase entities of type 'WEBSITE' 
     * that are currently in the 'PROCESSING' status.
     *
     * @return a list of unprocessed KnowledgeBase entities of type 'WEBSITE'
     */
    @Query("SELECT kb FROM KnowledgeBase kb WHERE kb.type = 'WEBSITE' AND kb.websiteTaskStatus = 'PROCESSING'")
    List<KnowledgeBase> getWebsiteTypeUnprocessedKB();

    /**
     * Checks if a KnowledgeBase entity with the specified website URL exists 
     * and is not marked as deleted.
     *
     * @param webSiteUrl the website URL to check
     * @return a list of KnowledgeBase entities matching the specified URL
     */
    @Query("SELECT kb FROM KnowledgeBase kb WHERE kb.webSiteUrl = :webSiteUrl AND kb.deleted = false")
    List<KnowledgeBase> existsWebsiteUrl(String webSiteUrl);

    @Query("SELECT kb FROM KnowledgeBase kb WHERE kb.name = :knowledgeBaseName")
    KnowledgeBase findByName(String knowledgeBaseName);
}
