package com.enttribe.promptanalyzer.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * An entity class that represents the historical interaction records of an agent.
 * Manages the persistence and retrieval of agent conversation history, including
 * all associated messages and interactions.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@Entity
@Table(name = "AGENT_HISTORY")
public class AgentHistory {

    /**
     * Unique identifier for the agent history record.
     * Auto-generated primary key using identity strategy.
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition = "INT")
    private Integer id;

    /**
     * Name identifier of the agent associated with this history.
     * Mapped to "processGroupId" in JSON serialization for external compatibility.
     */
    @Column(name = "AGENT_NAME", columnDefinition = "VARCHAR(255)")
    @JsonProperty("processGroupId")
    private String agentName;

    //this is accepted plan
    @Column(name = "CURRENT_PLAN", columnDefinition = "TEXT")
    private String currentPlan = "";

    //this is the last plan developed in this conversation
    @Column(name = "LAST_FLOW", columnDefinition = "TEXT")
    private String lastFlow = "";

    /**
     * Collection of messages associated with this agent's history.
     * Maintains a bidirectional relationship with AgentMessage entities.
     * Initialized as an empty ArrayList to prevent null pointer exceptions.
     */
    @OneToMany(
            targetEntity = AgentMessage.class,
            mappedBy = "agentHistory",
            cascade = CascadeType.ALL,
            orphanRemoval = true,
            fetch = FetchType.EAGER
    )
    private List<AgentMessage> agentMessages = new ArrayList<>();

}
