package com.enttribe.promptanalyzer.dto.prompt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that handles version information for prompts.
 * Provides version tracking and identification capabilities.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PromptVersionDetailsDto {
    private Integer id;
    private String version;

    public  PromptVersionDetailsDto(Integer id, String version) {
        this.id = id;
        this.version = version;
    }
}

