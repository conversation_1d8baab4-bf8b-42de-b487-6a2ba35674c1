package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.Agent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for Agent entity operations.
 * Provides methods to interact with the Agent table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 * @see Agent
 * @see JpaRepository
 */
@Repository
public interface AgentDao extends JpaRepository<Agent, Long> {

    /**
     * Finds an agent by its name.
     *
     * @param agentName The name of the agent to search for
     * @return The Agent entity if found, null otherwise
     */
    @Query("SELECT a FROM Agent a WHERE a.name = :agentName")
    Agent findByName(String agentName);
}
