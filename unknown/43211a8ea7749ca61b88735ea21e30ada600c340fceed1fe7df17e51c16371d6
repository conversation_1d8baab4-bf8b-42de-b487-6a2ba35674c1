package com.enttribe.promptanalyzer.dto.processor;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that encapsulates processor configuration requests.
 * Handles processor metadata, categorization, and structure definition.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProcessorRequestDto {

    private String key;
    private String displayName;
    private String icon;
    private String styleType;
    private String category;
    private String subCategory;
    private String jsonStructure;

}
