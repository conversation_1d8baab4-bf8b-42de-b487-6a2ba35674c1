package com.enttribe.promptanalyzer.dto.tag;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for tag creation and update requests.
 * This class is used to transfer tag data from client to server.
 * Unknown JSON properties are ignored during deserialization.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class TagRequestDto {

   private Integer id;
   private String name;
   private String type;
}
