package com.enttribe.promptanalyzer.model;

import jakarta.persistence.*;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

/**
 * Entity representing a voice agent configuration.
 * Extends the base agent to include voice-specific settings and associations.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Setter
@Getter
@Entity
@Table(name = "VOICE_AGENT")
public class VoiceAgent extends  BaseAgent{

    /**
     * Unique ID of the voice agent.
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition = "INT")
    private Integer id;

    /**
     * Associated text-to-speech configuration.
     */
    @ManyToOne(targetEntity = TextToSpeech.class,fetch = FetchType.LAZY)
    @JoinColumn(name = "TTS_ID", referencedColumnName = "ID")
    private TextToSpeech textToSpeech;

    /**
     * Associated speech-to-text configuration.
     */
    @ManyToOne(targetEntity = SpeechToText.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "STT_ID", referencedColumnName = "ID")
    private SpeechToText speechToText;

    /**
     * Tools associated with the voice agent.
     */
    @ManyToMany(targetEntity = Tool.class, fetch = FetchType.LAZY)
    @JoinTable(name = "VOICE_AGENT_TOOL",
            joinColumns = @JoinColumn(name = "VOICE_AGENT_TOOL"),
            inverseJoinColumns = @JoinColumn(name = "TOOL_ID"))
    private Set<Tool> tools = new HashSet<>();


}
