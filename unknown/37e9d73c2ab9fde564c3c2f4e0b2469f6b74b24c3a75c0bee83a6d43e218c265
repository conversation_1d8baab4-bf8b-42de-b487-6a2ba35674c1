package com.enttribe.promptanalyzer.dto.processor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents processor configuration responses.
 * Extends ProcessorRequestDto with additional response-specific fields.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class ProcessorResponseDto {

    private Integer id;
    private String key;
    private String displayName;
    private String icon;
    private String styleType;
    private String category;
    private String subCategory;
    private String jsonStructure;

}
