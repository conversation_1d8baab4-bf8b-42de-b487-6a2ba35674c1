package com.enttribe.promptanalyzer.model;

import com.enttribe.promptanalyzer.util.ByteCodeMapConverter;
import jakarta.persistence.Column;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Convert;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Base entity class for Agent entities.
 * Provides common properties and relationships for all agent types.
 * Uses JPA auditing for creation and modification timestamps.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Setter
@Getter
@EntityListeners(AuditingEntityListener.class)
@MappedSuperclass
public class BaseAgent {

    /**
     * Internal name of the agent.
     */
    @Column(name = "NAME")
    private String name;

    /**
     * Description of the agent's purpose or function.
     */
    @Column(name = "PURPOSE")
    private String purpose;

    /**
     * JSON string containing agent variables configuration.
     */
    @Column(name = "VARIABLES", columnDefinition = "TEXT")
    private String variables;

    /**
     * User-friendly display name for the agent.
     */
    @Column(name = "DISPLAY_NAME")
    private String displayName;

    /**
     * Current status of the agent.
     * Possible values: ACTIVE, DEACTIVE, DRAFT, PUBLISH
     */
    @Column(name = "STATUS", columnDefinition = "ENUM('ACTIVE','DEACTIVE','DRAFT','PUBLISH')")
    private String status;

    /**
     * Icon representation for the agent, stored as text (e.g., base64 or URL).
     */
    @Column(name = "ICON", columnDefinition = "TEXT")
    private String icon;

    /**
     * Associated LLM model for this agent.
     * One-to-one relationship with LlmModel entity.
     */
    @ManyToOne
    @JoinColumn(name = "LLM_MODEL_ID", referencedColumnName = "ID") // The foreign key in 'Prompt' table
    private LlmModel llmModel;

    /**
     * Map of variable byte code implementations.
     * Stored as LONGTEXT in the database and converted using ByteCodeMapConverter.
     */
    @Lob
    @Convert(converter = ByteCodeMapConverter.class)
    @Column(name = "VAR_BYTE_CODE_MAP", columnDefinition = "LONGTEXT")
    private Map<String, byte[]> variablesByteCode = new HashMap<>();

    /**
     * Associated prompt for this agent.
     * One-to-one relationship with Prompt entity.
     */
    @ManyToOne
    @JoinColumn(name = "PROMPT_ID", referencedColumnName = "ID") // The foreign key in 'Prompt' table
    private Prompt prompt;

    /**
     * Timestamp when the agent was created.
     */
    @Column(name = "CREATED_TIME")
    private Date createdTime;

    /**
     * Timestamp when the agent was last modified.
     */
    @Column(name = "MODIFIED_TIME")
    private Date modifiedTime;

}

