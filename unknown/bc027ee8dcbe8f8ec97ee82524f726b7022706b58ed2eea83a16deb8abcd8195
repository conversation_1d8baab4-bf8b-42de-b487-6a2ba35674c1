package com.enttribe.promptanalyzer.dto.tool;

import com.enttribe.promptanalyzer.model.Prompt;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * A comprehensive Data Transfer Object (DTO) that represents a complete tool configuration.
 * Serves as the primary tool representation including all tool-related information,
 * configurations, and associations.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToolDto {

    private Integer id;
    private String applicationName;
    private String category;
    private String name;
    private String displayName;
    private String version;
    private Boolean deleted = false;
    private String toolId;
    private String language;
    private String type;
    private String sourceCode;
    private List<SwaggerDto> swaggerApis;
    private ApiToolDto apiTool;
    private String description;
    private String className;
    private String requestType;
    private Prompt prompt;
    private String tags;
    private String toolJson;
    private String status;
    private ToolAuthDto toolAuthentication;
    private String toolImage;
    private Long agentId;
    private String httpMethod;
    private String parameters;
    private String url;
    private String connectorName;
    private String operationConfig;
    private Boolean returnDirect;


}
