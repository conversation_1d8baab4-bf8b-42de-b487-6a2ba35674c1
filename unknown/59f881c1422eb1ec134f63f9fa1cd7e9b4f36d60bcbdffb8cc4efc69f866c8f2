package com.enttribe.promptanalyzer.model;

import jakarta.persistence.Id;
import jakarta.persistence.Entity;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Table;
import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Enumerated;
import jakarta.persistence.EnumType;

import lombok.Getter;
import lombok.Setter;

/**
 * Entity representing a speech-to-text configuration.
 * Stores details about the service and language used for speech-to-text conversion.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Setter
@Getter
@Entity
@Table(name = "SPEECH_TO_TEXT")
public class SpeechToText {

    /**
     * Unique ID of the speech-to-text configuration.
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition = "INT")
    private Integer id;

    /**
     * Service used for speech-to-text conversion.
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "SERVICE")
    private Service service;

    /**
     * Language used for speech-to-text conversion.
     */
    @Column(name = "LANGUAGE")
    private String language;
}
