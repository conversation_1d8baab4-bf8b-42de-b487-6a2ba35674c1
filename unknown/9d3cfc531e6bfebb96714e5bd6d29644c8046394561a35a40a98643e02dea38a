package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.rest.ToolCallbackProviderRest;
import com.enttribe.promptanalyzer.service.ToolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;


@Slf4j
@RestController
@RequiredArgsConstructor
public class ToolCallbackProviderRestImpl implements ToolCallbackProviderRest {

    private final ToolService toolService;

    @Override
    public List<HashMap<String, String>> getToolCallbackProvider() {
        return toolService.getToolCallbackProvider();
    }
}
