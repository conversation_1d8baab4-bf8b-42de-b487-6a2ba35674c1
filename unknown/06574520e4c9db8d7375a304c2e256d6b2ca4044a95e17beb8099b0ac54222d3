package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.AgentDao;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dto.agent.AgentConvertDto;
import com.enttribe.promptanalyzer.dto.agent.AgentDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Agent;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.service.AgentService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.AgentConvertUtils;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Implementation of the {@link AgentService} interface.
 * This class provides the business logic for managing agents.
 * It handles agent creation, searching, and data mapping operations while
 * interacting with the data access layer.
 * The service manages:
 * - Agent creation and validation
 * - Search functionality with filtering and pagination
 * - Data mapping between DTOs and entities
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AgentServiceImpl implements AgentService {

    private final AgentDao agentDao;
    private final CustomFilter customFilter;
    private final PromptDao promptDao;


    /**
     * Creates a new agent.
     * This method handles the creation of a new agent based on the provided AgentDto.
     *
     * @param agentDto The AgentDto containing the agent details to be created
     * @return Map containing the operation result with keys:
     *         - "status": "success".
     * @throws BusinessException if agent creation fails or validation errors occur
     */
    @Override
    public Map<String, String> createAgent(AgentDto agentDto) {
        log.debug("@class AgentServiceImpl @method createAgent going to create Agent");
        Map<String, String> result = new HashMap<>();
        try {
            Agent agent = mapDtoToEntity(agentDto);
            Prompt prompt = promptDao.findById(agentDto.getPrompt().getId())
                    .orElseThrow(() -> new EntityNotFoundException("Prompt not found for ID: " + agentDto.getPrompt().getId()));
            log.debug("Found Prompt : {}", agentDto.getPrompt().getName());
            agent.setLlmModel(prompt.getLlmModel());
            agent.setPrompt(prompt);
            agent.setCreatedTime(new Date());
            agentDao.save(agent);
            log.debug("Successfully saved agent");
            result.put("result", "success");
            return result;
        } catch (Exception e) {
            log.error("Exception occurred while saving agent: {}", e.getMessage());
            throw new BusinessException("unable to save agent : " + e.getMessage());
        }
    }

    /**
     * Maps an AgentDto to an Agent entity.
     * This method creates a new Agent entity and populates it with data from the provided DTO,
     *
     * @param agent The AgentDto containing the source data
     * @return A new Agent entity populated with the DTO data
     */
    private Agent mapDtoToEntity(AgentDto agent) {
        Agent agent1 = new Agent();
        agent1.setVariables(agent.getVariables());
        agent1.setDisplayName(agent.getDisplayName());
        agent1.setIcon(agent.getIcon());
        agent1.setName(agent.getName());
        agent1.setPurpose(agent.getPurpose());
        agent1.setStatus(agent.getStatus());
        log.debug("Mapped Successfully AgentDto to Agent entity: {}", agent);
        return agent1;
    }

    /**
     * Searches for agents based on specified criteria and pagination parameters.
     *
     * @param filter The search filter criteria to apply
     * @param offset The starting position in the result set
     * @param size The maximum number of results to return
     * @param orderBy The field to sort results by
     * @param orderType The sort direction (ascending/descending)
     * @return List of Agent objects matching the search criteria
     * @throws BusinessException if the search operation fails
     */
    @Override
    public List<AgentConvertDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        List<Agent> agents = customFilter.searchByFilter(Agent.class, filter, orderBy, orderType, offset, size);
        return AgentConvertUtils.getAgentDtoList(agents);
    }


}

