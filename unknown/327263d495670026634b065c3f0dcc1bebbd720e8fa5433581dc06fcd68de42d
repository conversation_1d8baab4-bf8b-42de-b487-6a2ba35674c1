package com.enttribe.promptanalyzer.exception;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * This class represents the details of an error response, 
 * including the timestamp, error message, and additional details.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
public class ErrorDetails {

    private Date timestamp;
    private String errorMsg;
    private String details;
    private String detailedMessage;

    /**
     * Constructs an ErrorDetails object with the specified timestamp, 
     * error message, and details.
     *
     * @param timestamp the time when the error occurred
     * @param errMessage the error message
     * @param details additional details about the error
     */
    public ErrorDetails(Date timestamp, String errMessage, String details) {
        this.timestamp = timestamp;
        this.errorMsg = errMessage;
        this.details = details;
    }

    /**
     * Constructs an ErrorDetails object with the specified timestamp, 
     * error message, details, and a detailed message.
     *
     * @param timestamp the time when the error occurred
     * @param errMessage the error message
     * @param details additional details about the error
     * @param detailedMessage a more detailed message about the error
     */
    public ErrorDetails(Date timestamp, String errMessage, String details, String detailedMessage) {
        this.timestamp = timestamp;
        this.errorMsg = errMessage;
        this.details = details;
        this.detailedMessage = detailedMessage;
    }

}
