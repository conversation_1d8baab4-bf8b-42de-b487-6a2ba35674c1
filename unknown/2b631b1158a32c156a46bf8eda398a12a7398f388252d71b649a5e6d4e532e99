package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.ProviderModelDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.service.LlmModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Implementation of the {@link LlmModelService} interface.
 * This class provides the actual business logic for managing LlmModel.
 * for a specific application. It interacts with the data access layer to fetch and modify LlmModel data.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class LlmModelServiceImpl implements LlmModelService {

    private final LlmModelDao llmModelDao;

    /**
     * Retrieves the models associated with a specific provider for the "chat" type.
     *
     * @param provider the name of the provider for which the models are to be fetched.
     * @return a {@link ProviderModelDto} containing the provider and its associated models.
     *         If no models are found, the returned list will be empty.
     */
    @Override
    public ProviderModelDto getModelsByProvider(String provider) {
        log.debug("Fetching models for provider: {}", provider);
        List<String> models = llmModelDao.getModelsByProvider(provider, "chat");
        log.debug("Number of models fetched: {}", models.size());
        ProviderModelDto dto = new ProviderModelDto();
        List<ProviderModelDto.ProviderModel> providerModels = models.stream().map(model -> new ProviderModelDto.ProviderModel(provider, model)).toList();

        dto.setProvider(provider);
        dto.setModels(providerModels);
        return dto;
    }

    /**
     * Retrieves all providers with their associated models for the "chat" type.
     *
     * @return a list of {@link ProviderModelDto} where each entry contains a provider and its associated models.
     *         If no models are found for any provider, the list will be empty.
     * @throws BusinessException if no records are found in the database for the query.
     */
    @Override
    public List<ProviderModelDto> getAllProvidersWithModels() {
        log.debug("Inside method getAllProvidersWithModels");
        try {
            List<Object[]> results = llmModelDao.getAllProvidersWithModels("chat");

            if (results.isEmpty()) {
                log.warn("No records found in LlmModel for the given query");
                throw new BusinessException("No records found in LlmModel for the given query");
            }
            Map<String, List<ProviderModelDto.ProviderModel>> providerModelMap = new HashMap<>();
            for (Object[] result : results) {
                String provider = (String) result[0];
                String model = (String) result[1];
                ProviderModelDto.ProviderModel providerModel = new ProviderModelDto.ProviderModel(provider, model);
                providerModelMap.computeIfAbsent(provider, k -> new ArrayList<>()).add(providerModel);
            }
            List<ProviderModelDto> providerModelDtos = new ArrayList<>();
            providerModelMap.forEach((provider, models) -> {
                ProviderModelDto dto = new ProviderModelDto();
                dto.setProvider(provider);
                dto.setModels(models);
                providerModelDtos.add(dto);
            });
            return providerModelDtos;
        } catch (Exception e) {
            log.error("Error fetching all providers with models: {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * Creates a new LlmModel record in the database.
     *
     * @param llmmodel the {@link LlmModel} object to be created.
     * @return a {@link Map} containing a result message ("success" or "failed").
     * @throws BusinessException if there is any error during the creation process.
     */
    @Override
    public Map<String, String> create(LlmModel llmmodel) {
        log.debug("Inside create LlmModel");
        Map<String, String> response = new HashMap<>();

        try {
            llmModelDao.save(llmmodel);
            log.debug("LlmModel saved successfully");
            response.put("result", "success");
        } catch (Exception e) {
            log.error("Failed to create LlmModel: {}", e.getMessage(), e);
            response.put("result", "failed");
            throw new BusinessException(e.getMessage());
        }

        return response;
    }

    /**
     * Retrieves a list of unique inferences based on the type.
     *
     * @param type the type of inference to be fetched.
     * @return a list of {@link LlmModelSdkDto} containing unique inferences for the given type.
     */
    @Override
    public List<LlmModelSdkDto> getUniqueInferencesByType(String type) {
        log.debug("Fetching unique inferences for type: {}", type);
        return llmModelDao.getUniqueInferencesByType(type);
    }

    /**
     * Retrieves LLM models for SDK for a given application and type.
     *
     * @param appName the name of the application for which the models are to be fetched.
     * @param type the type of the models to be fetched.
     * @return a list of {@link LlmModelSdkDto} containing the LLM models for the SDK.
     */
    @Override
    public List<LlmModelSdkDto> getLlmModelsForSDK(String appName, String type) {
        log.debug("Fetching LLM models for SDK for app: {}, type: {}", appName, type);
        return llmModelDao.getLlmModelsForSDK(appName, type);
    }

    /**
     * Retrieves LLM models by type for SDK.
     *
     * @param type the type of LLM models to be fetched.
     * @return a list of {@link LlmModelSdkDto} containing the models for the SDK.
     */
    @Override
    public List<LlmModelSdkDto> getLlmModelsByTypeForSDK(String type) {
        log.debug("Fetching LLM models by type for SDK: {}", type);
        return llmModelDao.getLlmModelsByTypeForSDK(type);
    }

}
