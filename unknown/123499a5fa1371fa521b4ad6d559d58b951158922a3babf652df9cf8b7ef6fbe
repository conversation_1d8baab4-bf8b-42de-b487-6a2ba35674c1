package com.enttribe.promptanalyzer.dto.tag;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for tag responses.
 * This class represents the tag data sent from server to client.
 * Uses the Builder pattern for object creation and ignores unknown JSON properties during deserialization.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class TagResponseDto {

    private Integer id;
    private String name;
    private String type;
}
