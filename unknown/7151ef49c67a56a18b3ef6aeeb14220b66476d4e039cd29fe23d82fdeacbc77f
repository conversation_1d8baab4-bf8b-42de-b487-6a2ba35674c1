package com.enttribe.promptanalyzer.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that manages assertion template configurations
 * for prompts. Handles the association between prompts and their assertion
 * templates used for validation and testing purposes.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssertionTemplateDto {

    private Integer promptId;
    private String assertionTemplate;

}

