package com.enttribe.promptanalyzer.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that defines the formatting and style parameters
 * for conversation prompts. Manages the conversational aspects and communication
 * guidelines for AI interactions.
 * Author: VisionWaves
 * Version: 1.0
 */

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConversationPromptFormat {

    @JsonPropertyDescription("Role or persona for the system prompt")
    private String role;
    @JsonPropertyDescription("General tone or approach of the communication")
    private String communicationStyle;
    @JsonPropertyDescription("How the responses are structured, including the form and style of communication")
    private String responseGuidelines;
    @JsonPropertyDescription("The complexity, sophistication and formality of the language")
    private String languageStyle;

}
