package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.Processor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for Processor entity operations.
 * Extends JpaRepository to provide standard CRUD operations and pagination support for Processor entities.
 * 
 * @see Processor
 * @see JpaRepository
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface ProcessorDao extends JpaRepository<Processor, Integer> {

}
