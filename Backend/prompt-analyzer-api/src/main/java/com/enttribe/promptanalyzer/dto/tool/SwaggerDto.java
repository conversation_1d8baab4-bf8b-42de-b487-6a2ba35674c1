package com.enttribe.promptanalyzer.dto.tool;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents Swagger/OpenAPI specification details
 * and configurations. Handles API documentation, endpoint information, and related
 * metadata for Swagger-documented services.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SwaggerDto {

    private String name;
    private String description;
    private String url;
    private String httpMethod;
    private String parameters;
    private String hostname;
    private String swaggerJson;
    private String applicationName;
    private String category;
    private String status;
    private String tags;
    private ToolAuthDto toolAuthentication;
    private String toolImage;
    private Boolean returnDirect;

}
