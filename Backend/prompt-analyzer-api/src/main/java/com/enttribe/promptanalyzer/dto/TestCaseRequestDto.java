package com.enttribe.promptanalyzer.dto;

import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents test case configurations for prompts.
 * Manages test case data, assertions, and associated prompt information.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@Builder
public class TestCaseRequestDto {

    private Integer id;
    private String testcaseId;
    private Boolean deleted;
    private String inputJson;
    private String remark;
    private String assertions;
    private PromptConvertorDto prompt;

}
