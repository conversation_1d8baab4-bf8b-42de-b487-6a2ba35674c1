package com.enttribe.promptanalyzer.dto.tool;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * A Data Transfer Object (DTO) specifically designed for SDK interactions with tools.
 * Provides a simplified interface for external SDK implementations while maintaining
 * essential tool functionality and bytecode management.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToolDtoSdk {

    private Integer id;
    private String toolName;
    private Map<String, byte[]> byteCodeMap;
    private String className;
    private String toolId;
    private String requestType;
    private String description;
    private Boolean returnDirect;

}
