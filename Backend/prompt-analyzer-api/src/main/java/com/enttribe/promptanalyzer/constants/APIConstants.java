package com.enttribe.promptanalyzer.constants;

/**
 * Constants used throughout the API for authentication, authorization, and responses.
 * Contains role definitions, status codes, and access scopes for different API endpoints.
 * Author: VisionWaves
 * Version: 1.0
 */
public class APIConstants {
    
    private APIConstants() {
    }

    public static final String RESULT = "result";
    public static final String SUCCESS= "success";
    public static final String FAILED = "failed";
    public static final String SUCCESS_CODE = "200";
    public static final String DEFAULT_SCHEME = "default";
    public static final String ERROR= "Internal server error";
    public static final String ERROR_CODE = "500";

    // Tool scopes
    public static final String ROLE_API_TOOL_WRITE = "ROLE_API_TOOL_WRITE";
    public static final String ROLE_API_TOOL_READ = "ROLE_API_TOOL_READ";

    // LlmModel scopes
    public static final String ROLE_API_LLMMODEL_WRITE = "ROLE_API_LLMMODEL_WRITE";
    public static final String ROLE_API_LLMMODEL_READ = "ROLE_API_LLMMODEL_READ";

    // Test case scopes
    public static final String ROLE_API_TESTCASE_WRITE = "ROLE_API_TESTCASE_WRITE";
    public static final String ROLE_API_TESTCASE_GENERATE = "ROLE_API_TESTCASE_GENERATE";
    public static final String ROLE_API_TESTCASE_READ = "ROLE_API_TESTCASE_READ";

    // Exception scopes
    public static final String ROLE_API_EXCEPTION_SAVE= "ROLE_API_EXCEPTION_SAVE";
    public static final String ROLE_API_EXCEPTION_READ = "ROLE_API_EXCEPTION_READ";

    // Audit API scopes - Action-based
    public static final String ROLE_API_AUDIT_EXCEPTION_SAVE = "ROLE_API_AUDIT_EXCEPTION_SAVE";
    public static final String ROLE_API_AUDIT_PROMPT_SAVE = "ROLE_API_AUDIT_PROMPT_SAVE";
    public static final String ROLE_API_AUDIT_TOOL_SAVE = "ROLE_API_AUDIT_TOOL_SAVE";
    public static final String ROLE_API_AUDIT_PROMPT_GET_BY_AUDIT_ID = "ROLE_API_AUDIT_PROMPT_GET_BY_AUDIT_ID";
    public static final String ROLE_API_AUDIT_PROMPT_GET_BY_PROMPT_ID = "ROLE_API_AUDIT_PROMPT_GET_BY_PROMPT_ID";
    public static final String ROLE_API_AUDIT_PROMPT_SEARCH = "ROLE_API_AUDIT_PROMPT_SEARCH";
    public static final String ROLE_API_AUDIT_PROMPT_COUNT = "ROLE_API_AUDIT_PROMPT_COUNT";
    public static final String ROLE_API_AUDIT_TOOL_GET_BY_AUDIT_ID = "ROLE_API_AUDIT_TOOL_GET_BY_AUDIT_ID";

    // Llm API scopes
    public static final String ROLE_API_CHAT_COMPLETE= "ROLE_API_CHAT_COMPLETE";
    public static final String ROLE_API_EXECUTE_PROMPT = "ROLE_API_EXECUTE_PROMPT";
    public static final String ROLE_API_EXECUTE_PROMPT_V1 = "ROLE_API_EXECUTE_PROMPT_V1";


    // trigger API scopes
    public static final String ROLE_API_TRIGGER_READ = "ROLE_API_TRIGGER_READ";
    public static final String ROLE_API_TRIGGER_WRITE = "ROLE_API_TRIGGER_WRITE";

    // Agent API scopes
    public static final String ROLE_API_AGENT_READ = "ROLE_API_AGENT_READ";
    public static final String ROLE_API_AGENT_WRITE = "ROLE_API_AGENT_WRITE";
    public static final String ROLE_API_AGENT_CREATE = "ROLE_API_AGENT_CREATE";
    public static final String ROLE_API_AGENT_SEARCH = "ROLE_API_AGENT_SEARCH";
    public static final String ROLE_API_AGENT_GET_HISTORY = "ROLE_API_AGENT_GET_HISTORY";
    public static final String ROLE_API_AGENT_PLAN_CREATE = "ROLE_API_AGENT_PLAN_CREATE";
    public static final String ROLE_API_AGENT_TRIGGER_CREATE = "ROLE_API_AGENT_TRIGGER_CREATE";
    public static final String ROLE_API_AGENT_TRIGGER_NAME_CREATE = "ROLE_API_AGENT_TRIGGER_NAME_CREATE";

    // Processor API scopes
    public static final String ROLE_API_PROCESSOR_READ = "ROLE_API_PROCESSOR_READ";
    public static final String ROLE_API_PROCESSOR_WRITE = "ROLE_API_PROCESSOR_WRITE";

    // Query API scopes
    public static final String ROLE_API_QUERY_READ = "ROLE_API_QUERY_READ";
    public static final String ROLE_API_QUERY_WRITE = "ROLE_API_QUERY_WRITE";

    // Tag API scopes
    public static final String ROLE_API_TAG_READ = "ROLE_API_TAG_READ";
    public static final String ROLE_API_TAG_WRITE = "ROLE_API_TAG_WRITE";

    // Prompt API scopes
    public static final String ROLE_API_PROMPT_READ = "ROLE_API_PROMPT_READ";
    public static final String ROLE_API_PROMPT_WRITE = "ROLE_API_PROMPT_WRITE";

    // Knowledge Base scopes
    public static final String ROLE_API_KNOWLEDGE_BASE_WRITE = "ROLE_API_KNOWLEDGE_BASE_WRITE";
    public static final String ROLE_API_KNOWLEDGE_BASE_READ = "ROLE_API_KNOWLEDGE_BASE_READ";

    // MCP Server scopes
    public static final String ROLE_API_MCP_SERVER_READ = "ROLE_API_MCP_SERVER_READ";
    public static final String ROLE_API_MCP_SERVER_WRITE = "ROLE_API_MCP_SERVER_WRITE";

    // Hint scopes
    public static final String ROLE_API_HINT_READ = "ROLE_API_HINT_READ";
    public static final String ROLE_API_HINT_WRITE = "ROLE_API_HINT_WRITE";

    // Time Parser scopes
    public static final String ROLE_API_TIME_PARSER_READ = "ROLE_API_TIME_PARSER_READ";

    // Tool Callback Provider scopes
    public static final String ROLE_API_TOOL_CALLBACK_READ = "ROLE_API_TOOL_CALLBACK_READ";
}

