package com.enttribe.promptanalyzer.model;


import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Entity representing a user query.
 * Stores details about queries made by users for processing and analysis.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Entity
@Table(name = "QUERY")
@Getter
@Setter
public class Query {

    /**
     * Unique ID of the query.
     */
    @GeneratedValue(strategy = jakarta.persistence.GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition = "INT")
    private Integer id;

    /**
     * ID of the user who made the query.
     */
    @Column(name = "USER_ID")
    private String userId;

    /**
     * Type of the query.
     */
    @Column(name = "TYPE", length = 50)
    private String type;


    /**
     * The question or content of the query.
     */
    @Column(name = "QUESTION",columnDefinition = "LONGTEXT")
    private String question;

    /**
     * Timestamp when the query was created.
     */
    @Column(name = "CREATED_TIME")
    private Date createdTime;

    /**
     * Timestamp when the query was last modified.
     */
    @Column(name = "MODIFIED_TIME")
    private Date modifiedTime;

    /**
     * Indicates if the query is marked as deleted.
     */
    @Column(name = "DELETED")
    private Boolean deleted = false;



}
