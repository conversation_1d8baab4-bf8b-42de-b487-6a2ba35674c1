package com.enttribe.promptanalyzer.dto.prompt;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * A Data Transfer Object (DTO) that manages prompt variables and content
 * for different roles in a conversation context.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
public class PromptVariableDto {

    private String promptName;
    private List<String> userPrompt;
    private List<String> systemPrompt;
    private List<String> assistantPrompt;

}
