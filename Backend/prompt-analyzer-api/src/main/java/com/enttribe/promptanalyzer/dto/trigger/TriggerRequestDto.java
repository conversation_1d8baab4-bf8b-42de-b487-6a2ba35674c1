package com.enttribe.promptanalyzer.dto.trigger;

import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents trigger creation and update requests.
 * Handles the essential information needed to create or modify trigger configurations
 * in the system.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
public class TriggerRequestDto {

    private Integer id;
    private String description;
    private String name;
    private String displayName;
    private String metadata;
}
