package com.enttribe.promptanalyzer.dto.mcpserver;

import com.enttribe.promptanalyzer.model.McpServer.ServerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * A Data Transfer Object (DTO) that represents MCP server configuration
 * and details. Handles server information, type classification, and custom server settings.
 * Author: VisionWaves
 * Version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class McpServerDto {
    private Integer id;
    private String name;
    private String description;
    private ServerType type;
    private String icon;
    private String url;
    private Boolean isCustomServer;
    private String commandToRun;
}