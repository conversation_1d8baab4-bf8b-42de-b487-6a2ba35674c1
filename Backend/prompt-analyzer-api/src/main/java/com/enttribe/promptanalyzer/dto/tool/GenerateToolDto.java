package com.enttribe.promptanalyzer.dto.tool;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.json.JSONObject;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerateToolDto {
    private String endpoint;
    private String method;
    private JSONObject methodDetails;
    private String hostName;
    private String authorizationType;
    private String authorizationKey;
    private String authValue;
    private Map<String, JSONObject> definitionsMap;
    private String applicationName;
    private String category;
}

