package com.enttribe.promptanalyzer.dto.knowledge_base;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * A Data Transfer Object (DTO) that represents vector search responses from the knowledge base.
 * Contains filter criteria, metadata, and document identifiers for vector search results.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@AllArgsConstructor
public class VectorResponseDto {

    private String filter;
    private Map<String, Object> metadata;
    private List<String> docIds;

}
