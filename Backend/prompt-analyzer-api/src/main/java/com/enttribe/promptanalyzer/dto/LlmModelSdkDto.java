package com.enttribe.promptanalyzer.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) for managing LLM (Large Language Model) configurations
 * in SDK context. Handles model connection details and authentication.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
public class LlmModelSdkDto {

    private String provider;
    private String baseUrl;
    private String apiKey;
    private String model;

    public LlmModelSdkDto(String provider, String baseUrl, String apiKey) {
        this.provider = provider;
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }

    public LlmModelSdkDto(String provider, String baseUrl, String apiKey, String model) {
        this.provider = provider;
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
        this.model = model;
    }
}
