package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.ProviderModelDto;
import com.enttribe.promptanalyzer.model.LlmModel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing LLM (Language Learning Model) operations.
 * Provides endpoints for creating and retrieving LLM models, with security requirements
 * for each operation. Supports SDK integration and provider-specific queries.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(name = "LlmModelRest", url = "${prompt-analyzer-service.url}", path = "/llm-model", primary = false)
public interface LlmModelRest {

    /**
     * Creates a new LLM model.
     * Requires ROLE_API_LLMMODEL_WRITE security role.
     *
     * @param llmmodel The LLM model to create
     * @return Map containing the result of the create operation
     * @apiNote Response Codes:
     * 200 - LlmModel created successfully
     * 500 - Error occurred during creation
     */
    @Operation(
            summary = "Create new LLM model", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_LLMMODEL_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "LlmModel created successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/create", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> create(@RequestBody LlmModel llmmodel);

    /**
     * Retrieves models for a specific provider.
     * Requires ROLE_API_LLMMODEL_READ security role.
     *
     * @param provider The provider name to get models for
     * @return ProviderModelDto containing the provider's models
     * @apiNote Response Codes:
     * 200 - Successfully retrieves all LLM models for the provider
     * 500 - Error occurred during retrieval
     */
    @Operation(summary = "Get models by provider",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_LLMMODEL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Successfully retrieves all LLM models associated with a specific provider"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping("/getModelsByProvider")
    ProviderModelDto getModelsByProvider(String provider);

    /**
     * Retrieves all providers and their associated models.
     * Requires ROLE_API_LLMMODEL_READ security role.
     *
     * @return List of ProviderModelDto containing all providers and their models
     * @apiNote Response Codes:
     * 200 - Successfully retrieved all providers and models
     * 500 - Error occurred during retrieval
     */
    @Operation(summary = "Get all providers and models",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_LLMMODEL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Successfully retrieved all providers and models"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping("/all")
    List<ProviderModelDto> getAllProvidersWithModels();

    /**
     * Retrieves LLM models for SDK integration by application name.
     * Requires ROLE_API_LLMMODEL_READ security role.
     *
     * @param appName The application name to get models for
     * @return List of LlmModelSdkDto for the specified application
     * @apiNote Response Codes:
     * 200 - Successfully retrieved models for SDK
     * 500 - Error occurred during retrieval
     * @deprecated Use {@link #getLlmModelsByTypeForSDK(String)} instead
     */
   @Deprecated(forRemoval=false)
    @SuppressWarnings("java:S1133")
    @Operation(summary = "Get LLM models for SDK by app name",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_LLMMODEL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Retrieves LLM models for SDK integration filtered by application name"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping("/getLlmModelsForSDK/{appName}")
    List<LlmModelSdkDto> getLlmModelsForSDK(@PathVariable String appName);

    /**
     * Retrieves LLM models by type for SDK integration.
     * Requires ROLE_API_LLMMODEL_READ security role.
     *
     * @param type The type of models to retrieve
     * @return List of LlmModelSdkDto matching the specified type
     * @apiNote Response Codes:
     * 200 - Successfully retrieved models by type
     * 500 - Error occurred during retrieval
     */
    @Operation(summary = "Get LLM models by type",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_LLMMODEL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Successfully retrieved models by using type for SDK"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping("/getLlmModelsByType/type/{type}")
    List<LlmModelSdkDto> getLlmModelsByTypeForSDK(@PathVariable String type);

}
