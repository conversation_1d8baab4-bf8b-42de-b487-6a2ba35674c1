package com.enttribe.promptanalyzer.dto.audit;


import lombok.Getter;
import lombok.Setter;

import java.util.Date;


/**
 * A Data Transfer Object (DTO) class that represents Tool Audit data. Used for transferring TOOL
 * Author: VisionWaves
 * Version: 1.0
 */

@Setter
@Getter
public class ToolAuditDto {
    private Integer id;
    private String requestText;
    private String chatOptions;
    private String auditId;
    private String toolCallRequest;
    private String toolDefinitions;
    private String toolResponse;
    private String agentName;
    private String promptId;
    private String promptName;
    private Integer totalToken;
    private Integer promptToken;
    private Integer generationTokens;
    private Long responseTime;
    private Date creationTime;
    private String model;
    private String provider;
    private String totalCost;
    private String applicationName;
    private String status;

}


