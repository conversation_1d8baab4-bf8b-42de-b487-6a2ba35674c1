package com.enttribe.promptanalyzer.dto.query;

import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents an incoming query request.
 * Handles user queries and their type classification for processing.
 * Author: VisionWaves
 * Version: 1.0
 */

@Setter
@Getter
public class QueryRequestDto {

    private String userId;
    private String type;
    private String question;
}
