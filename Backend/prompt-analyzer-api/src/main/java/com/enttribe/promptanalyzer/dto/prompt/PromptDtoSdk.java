package com.enttribe.promptanalyzer.dto.prompt;

import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * A Data Transfer Object (DTO) specifically designed for SDK interactions with the prompt system.
 * Provides a comprehensive configuration structure for prompt execution through the SDK.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PromptDtoSdk {

    private String id;
    private String promptName;
    private String inference;
    private String provider;
    private String model;
    private String category;
    private String promptId;
    private List<MessageConvertorDto> messages;
    private List<ToolDto> tools;
    private String defaultFormat;
    private Double temperature;
    private Double topP;
    private Integer maxTokens;
    private Boolean stream;
    private Boolean jsonMode;
    private Boolean llmGuard;
    private String applicationName;

}
