package com.enttribe.promptanalyzer.ai.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for AI message interactions.
 * Represents a message with a role and content, used in AI conversations.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class MessageDto {

    private String role;
    private String content;

    public MessageDto(String role, String content) {
        this.role = role;
        this.content = content;
    }
}