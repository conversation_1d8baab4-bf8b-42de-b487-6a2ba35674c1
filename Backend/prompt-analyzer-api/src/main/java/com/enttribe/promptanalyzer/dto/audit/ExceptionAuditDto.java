package com.enttribe.promptanalyzer.dto.audit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.Map;

/**
 * A Data Transfer Object (DTO) that captures and transfers exception-related audit information.
 * This class provides a structured format for logging and tracking exceptions across the system,
 * including contextual information about the exception occurrence.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExceptionAuditDto {

    private Date timestamp;
    private String customMessage;
    private String exceptionMessage;
    private String exceptionTrace;
    private String methodName;
    private Map<String, Object> methodParameters;
    private Map<String, Object> identifier;
    private String applicationName;
    private String promptId;
    private String category;
    private String auditId;


}
