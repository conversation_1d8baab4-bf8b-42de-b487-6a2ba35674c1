package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.TestCaseRequestDto;
import com.enttribe.promptanalyzer.model.TestCase;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing test case operations.
 * Provides endpoints for creating, updating, deleting, and searching test cases.
 * All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(name = "TestCaseRest", url = "${prompt-analyzer-service.url}", path = "/test-case", primary = false)
public interface TestCaseRest {

    /**
     * Creates a new test case.
     * Requires ROLE_API_TESTCASE_WRITE security role.
     *
     * @param requestDto The test case details to create
     * @return Map containing the result of the create operation
     * @apiNote Response Codes:
     * 200 - Create TestCase successfully
     * 500 - Error occurred during creation
     */
    @Operation(summary = "Create TestCase", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TESTCASE_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Create TestCase successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})

    @PostMapping(path = "/create", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> create(@RequestBody TestCaseRequestDto requestDto);

    /**
     * Creates a test case in batch.
     * Requires ROLE_API_TESTCASE_WRITE security role.
     *
     * @param requestDtos The test case details to create
     * @return Map containing the result of the create operation
     * @apiNote Response Codes:
     * 200 - Create TestCase successfully
     * 500 - Error occurred during creation
     */
    @Operation(summary = "Create TestCase", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TESTCASE_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Create TestCase successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})

    @PostMapping(path = "/create-batch", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> createBatch(@RequestBody List<TestCaseRequestDto> requestDtos);

    /**
     * Updates an existing test case.
     * Requires ROLE_API_TESTCASE_WRITE security role.
     *
     * @param updatedTestcase The updated test case data
     * @return Map containing the result of the update operation
     * @apiNote Response Codes:
     * 200 - Update TestCase successfully
     * 500 - Error occurred during update
     */
    @Operation(summary = "update TestCase", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TESTCASE_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Update TestCase successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})

    @PostMapping(path = "/update", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> update(@Valid @RequestBody TestCase updatedTestcase);

    /**
     * Deletes a test case by ID.
     * Requires ROLE_API_TESTCASE_WRITE security role.
     *
     * @param requestBody Map containing the ID of the test case to delete
     * @return Map containing the result of the delete operation
     * @apiNote Response Codes:
     * 200 - Delete TestCase successfully
     * 500 - Error occurred during deletion
     */
    @Operation(summary = "Delete TestCase", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TESTCASE_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Delete TestCase successfully by using TestCase id"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})

    @PostMapping(path = "/delete")
    Map<String, String> deleteTestcase(@RequestBody Map<String, Integer> requestBody);

    /**
     * Searches for test cases with pagination and sorting options.
     * Requires ROLE_API_TESTCASE_READ security role.
     *
     * @param filter    Optional filter criteria for searching test cases
     * @param offset    Required pagination offset
     * @param size      Required pagination size
     * @param orderBy   Optional field to order results by
     * @param orderType Optional order direction (asc/desc)
     * @return List of matching test cases converted to DTOs
     * @apiNote Response Codes:
     * 200 - Get All Testcase successfully
     * 500 - Error occurred during search
     */
    @Operation(summary = "search TestCase", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TESTCASE_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Get All Testcase successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})

    @GetMapping(path = "/search")
    List<TestCaseRequestDto> search(
            @RequestParam(required = false) String filter,
            @RequestParam(required = true) Integer offset,
            @RequestParam(required = true) Integer size,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderType);

    /**
     * Counts the number of test cases matching the optional filter.
     * Requires ROLE_API_TESTCASE_READ security role.
     *
     * @param filter Optional filter criteria
     * @return Total count of matching test cases
     * @apiNote Response Codes:
     * 200 - Count All TestCase successfully
     * 500 - Error occurred during count operation
     */
    @Operation(summary = "count TestCase", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TESTCASE_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "count All TestCase successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping(path = "/count")
    Long count(@RequestParam(required = false) String filter);

}
