package com.enttribe.promptanalyzer.function;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * An annotation that marks parameters for validation during runtime processing.
 * Used to indicate that a parameter should undergo validation checks before being
 * used in method execution.
 * Author: VisionWaves
 * Version: 1.0
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidateParameters {

}
