package com.enttribe.promptanalyzer.dto.knowledge_base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * A Data Transfer Object (DTO) that represents the response from knowledge base operations.
 * Contains comprehensive information about knowledge base entries including vectors and metadata.
 * Author: VisionWaves
 * Version: 1.0
 */
@Builder
@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class KnowledgeBaseResponseDto {

    private Integer id;
    private String name;
    private String description;
    private Map<String, byte[]> byteCodeMap;
    private String docId;
    private String docMetaData;
    private String vectorMetaData;
    private byte[] documentVector;
    private Boolean isContext;
    private Integer topK;
    private Double similarityThreshold;
    private String type;
    private String className;
    private String fileName;
    private String filter;
    private String websiteUrl;
    private String collectionName;
    private String tags;
    private String integration;
    private List<String> tables;
    private Boolean returnDirect;
    private String s3FileNames;

}
