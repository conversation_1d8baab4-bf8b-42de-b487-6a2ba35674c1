package com.enttribe.promptanalyzer.dto.agent;

import com.enttribe.promptanalyzer.model.Prompt;
import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that encapsulates agent information for creation and
 * update operations. This class represents the core attributes of an agent including
 * its display properties, configuration, and associated prompt.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
public class AgentDto {

    private String displayName;
    private String icon;
    private String name;
    private String purpose;
    private String status;
    private String variables;
    private Prompt prompt;

}
