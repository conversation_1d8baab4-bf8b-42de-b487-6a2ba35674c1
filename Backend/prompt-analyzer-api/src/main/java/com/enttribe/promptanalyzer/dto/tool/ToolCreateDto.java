package com.enttribe.promptanalyzer.dto.tool;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.json.JSONObject;

import java.util.Map;

@Setter
@Getter
@RequiredArgsConstructor
@AllArgsConstructor
public class ToolCreateDto {

    private String endpoint;
    private String method;
    private JSONObject methodObj;
    private String hostname;
    private ToolAuthDto toolAuthentication;
    private Map<String, JSONObject> definitionsMap;
    private SwaggerDto swaggerDto;
    private String toolImage;
}
