package com.enttribe.promptanalyzer.dto.knowledge_base;

import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * A Data Transfer Object (DTO) that encapsulates document upload and processing requests
 * for the knowledge base system. Handles various document types and configurations.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
public class DocumentRequestDto {

    private String name;
    private String type;
    private String description;
    private String websiteUrl;
    private String collectionName;
    private String docType;
    private Integer topK;
    private Double similarityThreshold;
    private List<MultipartFile> file;
    private Boolean deleted;
    private String tags;
    private String integration;
    private List<String> tables;
    private String content;
    private Boolean returnDirect;

}
