package com.enttribe.promptanalyzer.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Entity class representing a Tag in the system.
 * Tags are used to categorize and label various items in the application.
 * This entity is mapped to the "TAG" table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Setter
@Getter
@Entity
@Table(name = "TAG")
public class Tag {

    /**
     * Unique identifier for the tag.
     * Auto-generated using identity strategy.
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition = "INT")
    private Integer id;

    /**
     * The type or category of the tag.
     * Used for grouping related tags together.
     */
    @Column(name = "TYPE")
    private String type;

    /**
     * The display name of the tag.
     * Used for user-friendly representation of the tag.
     */
    @Column(name = "NAME")
    private String name;

    /**
     * Flag indicating if the tag has been soft deleted.
     * Default value is false.
     */
    @Column(name = "DELETED")
    private Boolean deleted = false;

    /**
     * Timestamp when the tag was created.
     * Automatically set during tag creation.
     */
    @Column(name = "CREATED_TIME")
    private Date createdTime;

    /**
     * Timestamp when the tag was last modified.
     * Updated automatically when the tag is modified.
     */
    @Column(name = "MODIFIED_TIME")
    private Date modifiedTime;


}
