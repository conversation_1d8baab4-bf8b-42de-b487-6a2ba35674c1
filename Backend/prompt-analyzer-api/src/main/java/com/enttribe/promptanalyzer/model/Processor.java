package com.enttribe.promptanalyzer.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Entity class representing a Processor in the system.
 * Processors are core components that handle specific processing tasks.
 * This entity is mapped to the "PROCESSOR" table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Entity
@Getter
@Setter
@Table(name = "PROCESSOR")
public class Processor {

    /**
     * Unique identifier for the processor.
     * Auto-generated using identity strategy.
     */
    @GeneratedValue(strategy = jakarta.persistence.GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition = "INT")
    private Integer id;

    /**
     * Unique key identifier for the processor.
     * Maximum length of 500 characters.
     */
    @Column(name = "UNIQUE_KEY", length = 500, unique = true)
    private String key;

    /**
     * User-friendly display name for the processor.
     */
    @Column(name = "DISPLAY_NAME")
    private String displayName;

    /**
     * Icon representation for the processor.
     * Stored as TEXT to accommodate various icon formats.
     */
    @Column(name = "ICON", columnDefinition = "TEXT")
    private String icon;

    /**
     * Style type for UI representation.
     * Limited to 50 characters.
     */
    @Column(name = "STYLE_TYPE", length = 50)
    private String styleType;

    /**
     * Flag indicating if the processor has been soft deleted.
     * Default value is false.
     */
    @Column(name = "DELETED")
    private Boolean deleted = false;

    /**
     * Main category of the processor.
     * Limited to 50 characters.
     */
    @Column(name = "CATEGORY", length = 50)
    private String category;

    /**
     * Sub-category for more specific categorization.
     * Limited to 50 characters.
     */
    @Column(name = "SUB_CATEGORY", length = 50)
    private String subCategory;

    @Column(name = "JSON_STRUCTURE", columnDefinition = "TEXT")
    private String jsonStructure;

    /**
     * Timestamp when the processor was created.
     * Automatically set during processor creation.
     */
    @Column(name = "CREATED_TIME")
    private Date createdTime;

    /**
     * Timestamp when the processor was last modified.
     * Updated automatically when the processor is modified.
     */
    @Column(name = "MODIFIED_TIME")
    private Date modifiedTime;

}


