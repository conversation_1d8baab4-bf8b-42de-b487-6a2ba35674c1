package com.enttribe.promptanalyzer.dto.tool;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * A Data Transfer Object (DTO) that represents workflow-specific tool configurations
 * and parameters. Manages tool workflow metadata, authentication, and required
 * parameters for workflow integration.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
public class ToolWorkflowDto {

    private String applicationName;
    private String category;
    private String name;
    private String description;
    private String displayName;
    private String hostName;
    private ToolAuthDto toolAuthentication;
    private List<Map<String, String>> requiredParameters;
    private String status;
    private String tags;
    private String toolImage;
    private Boolean returnDirect;
}
