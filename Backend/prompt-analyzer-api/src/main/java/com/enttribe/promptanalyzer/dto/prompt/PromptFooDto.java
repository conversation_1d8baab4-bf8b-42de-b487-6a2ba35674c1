package com.enttribe.promptanalyzer.dto.prompt;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents a simplified prompt configuration.
 * Used for basic prompt operations and quick configurations.
 * Author: VisionWaves
 * Version: 1.0
 */

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class PromptFooDto {

    private Integer id;
    private String application;
    private String name;
    private String category;
    private Double temperature;
    private String model;
    private Integer maxToken;
    private String version;
    private Double topP;
    private String prompt;

}
