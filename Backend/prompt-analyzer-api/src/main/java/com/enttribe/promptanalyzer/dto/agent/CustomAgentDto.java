package com.enttribe.promptanalyzer.dto.agent;

import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents custom agent interactions and queries.
 * This class handles user-specific agent requests and maintains conversation context
 * for process group operations.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
public class CustomAgentDto {

    private String conversationId;
    private String userQuery;
    private String processGroupId;
    private Long timeStamp;

}
