package com.enttribe.promptanalyzer.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

/**
 * Entity representing a Model Control Panel (MCP) server configuration.
 * Manages server details, type, and execution settings.
 * Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
@Entity
@Table(name = "MCP_SERVER")
public class McpServer {

    /**
     * Unique identifier for the McpServer.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", columnDefinition = "INT")
    private Integer id;

    /**
     * McpServer name identifier.
     */
    @Column(name = "NAME", columnDefinition = "VARCHAR(100)", nullable = false)
    private String name;

    /**
     * McpServer description.
     */
    @Column(name = "DESCRIPTION", columnDefinition = "VARCHAR(300)")
    private String description;

    /**
     * McpServer type (API or JSON).
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "TYPE")
    private ServerType type;

    /**
     * McpServer icon representation.
     */
    @Column(name = "ICON", columnDefinition = "TEXT")
    private String icon;

    /**
     * McpServer URL endpoint.
     */
    @Column(name = "URL", columnDefinition = "TEXT")
    private String url;

    /**
     * Custom McpServer flag.
     */
    @Column(name = "IS_CUSTOM_SERVER", columnDefinition = "BOOLEAN")
    private Boolean isCustomServer;

    /**
     * Command for McpServer execution.
     */
    @Column(name = "COMMAND_TO_RUN", columnDefinition = "TEXT")
    private String commandToRun;

    /**
     * Deletion status flag.
     */
    @Column(name = "DELETED")
    private Boolean deleted = false;

    /**
     * Timestamp when the server was created.
     */
    @Column(name = "CREATED_TIME")
    private Date createdTime;

    /**
     * Timestamp when the server was last modified.
     */
    @Column(name = "MODIFIED_TIME")
    private Date modifiedTime;

    public enum ServerType {
        API, JSON
    }
}