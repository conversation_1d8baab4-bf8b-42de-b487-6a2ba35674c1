package com.enttribe.promptanalyzer.dto.prompt;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents a message in a conversation format,
 * typically used for AI model interactions. Contains role and content information
 * for message conversion.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MessageConvertorDto {

    private String role;
    private String content;

}
