package com.enttribe.promptanalyzer.rest;


import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.tool.ToolWorkflowDto;
import com.enttribe.promptanalyzer.dto.tool.SwaggerDto;
import com.enttribe.promptanalyzer.dto.tool.ToolConvertorDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDtoSdk;
import com.enttribe.promptanalyzer.model.Tool;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing tool operations.
 * Provides endpoints for creating, updating, searching, and managing tools,
 * including support for Swagger integration, workflow generation, and import/export functionality.
 * All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(name = "ToolRest", url = "${prompt-analyzer-service.url}", path = "/tool", primary = false)
public interface ToolRest {



    /**
     * Creates a new tool.
     * Requires ROLE_API_TOOL_WRITE security role.
     *
     * @param toolDto The tool data to create
     * @return Map containing the result of the create operation
     * @apiNote Response Codes:
     * 200 - Create Tool successfully
     * 500 - Error occurred during creation
     */
    @Operation(summary = "Create new Tool", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Create Tool successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path = "/create", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> save(@RequestBody ToolDto toolDto);

    /**
     * Generates tools from Swagger JSON specification.
     * Requires ROLE_API_TOOL_WRITE security role.
     *
     * @param swaggerDto The Swagger specification data
     * @return ResponseEntity containing the result of tool generation
     * @apiNote Response Codes:
     * 200 - Tools created successfully with success/failure count
     * 500 - Error occurred during generation
     */
    @PostMapping("/toolFromSwagger")
    @Operation(summary = "Generate Tool from Swagger JSON", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Create Tool successfully by processing the provided Swagger JSON"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    ResponseEntity<Map<String, String>> createToolFromSwagger(@RequestBody SwaggerDto swaggerDto);

    /**
     * Searches for tools with pagination and sorting options.
     * Requires ROLE_API_TOOL_READ security role.
     *
     * @param filter    Optional filter criteria for searching tools
     * @param offset    Required pagination offset
     * @param size      Required pagination size
     * @param orderBy   Optional field to order results by
     * @param orderType Optional order direction (asc/desc)
     * @return List of matching tools as ToolConvertorDto objects
     */
    @GetMapping(path = "/search")
    @Operation(summary = "Search Tools", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Search Tool successfully based on the provided parameters like filtering, and sorting"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    List<ToolConvertorDto> search(
            @RequestParam(required = false) String filter,
            @RequestParam(required = true) Integer offset,
            @RequestParam(required = true) Integer size,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderType);

    /**
     * Counts the total number of tools matching the optional filter.
     * Requires ROLE_API_TOOL_READ security role.
     *
     * @param filter Optional filter criteria
     * @return Total count of matching tools
     * @apiNote Response Codes:
     * 200 - Count All Tools successfully
     * 500 - Error occurred during count operation
     */
    @GetMapping(path = "/count")
    @Operation(summary = "Count All Tools", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Count All Tools successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    Long count(
            @RequestParam(required = false) String filter);

    /**
     * Soft deletes a tool by its ID.
     * Requires ROLE_API_TOOL_WRITE security role.
     *
     * @param map Map containing the tool ID to delete
     * @return Map containing the result of the delete operation
     * @apiNote Response Codes:
     * 200 - Delete Tool successfully
     * 500 - Error occurred during deletion
     */
    @PostMapping(path = "/deleteById")
    @Operation(summary = "Delete Tool by Id", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Delete Tool successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    Map<String, String> deleteById(@RequestBody Map<String, Integer> map);

    /**
     * Retrieves a tool by its ID.
     * Requires ROLE_API_TOOL_READ security role.
     *
     * @param toolId The ID of the tool to retrieve
     * @return ToolConvertorDto containing the tool details
     * @apiNote Response Codes:
     * 200 - Find Tool successfully
     * 500 - Error occurred during retrieval
     */
    @GetMapping(path = "/findById/{toolId}")
    @Operation(summary = "Get Tool by Id", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Find Tool successfully from Id"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    ToolConvertorDto getToolById(@PathVariable Integer toolId);

    /**
     * Changes the status of a tool.
     * Requires ROLE_API_TOOL_WRITE security role.
     *
     * @param requestMap Map containing tool ID and new status
     * @return Map containing the result of the status change operation
     * @apiNote Response Codes:
     * 200 - Change Tool status successfully
     * 500 - Error occurred during status change
     */
    @PostMapping(path = "/changeToolStatus")
    @Operation(summary = "Change Tool status by Id", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Change Tool status successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    Map<String, String> changeToolStatus(@RequestBody Map<String, Object> requestMap);

    /**
     * Updates an existing tool.
     * Requires ROLE_API_TOOL_WRITE security role.
     *
     * @param tool The updated tool data
     * @return Map containing the result of the update operation
     * @apiNote Response Codes:
     * 200 - Update Tool successfully
     * 500 - Error occurred during update
     */
    @PostMapping(path = "/update", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Update Tool", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Update Tool status successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    Map<String, String> update(@RequestBody ToolDto tool);

    /**
     * Retrieves tools for a specific application.
     * Requires ROLE_API_TOOL_READ security role.
     *
     * @param appName The application name to get tools for
     * @return List of tools as ToolDtoSdk objects
     * @apiNote Response Codes:
     * 200 - Get Tool successfully
     * 500 - Error occurred during retrieval
     */
    @GetMapping(path = "/getToolsByApplication/{appName}")
    @Operation(summary = "Get Tools By using application name", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Get Tool successfully by using application name"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    List<ToolDtoSdk> getToolsByApplication(@PathVariable String appName);

    /**
     * Creates tools from workflow data.
     * Requires ROLE_API_TOOL_WRITE security role.
     *
     * @param toolWorkflowDto The workflow data to create tools from
     * @return ResponseEntity containing the result of tool creation
     * @apiNote Response Codes:
     * 200 - Create Tool successfully from workflow
     * 500 - Error occurred during creation
     */
    @PostMapping("/toolFromWorkflow")
    @Operation(summary = "Create Tools from workflow", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Create Tool successfully from workflow data"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    ResponseEntity<Map<String, String>> createToolFromWorkflow(@RequestBody ToolWorkflowDto toolWorkflowDto);

    /**
     * Retrieves tools associated with a specific agent.
     * Requires ROLE_API_TOOL_READ security role.
     *
     * @param agentId The ID of the agent
     * @return List of Tool objects associated with the agent
     * @apiNote Response Codes:
     * 200 - Get Tool successfully
     * 500 - Error occurred during retrieval
     */
    @GetMapping("/getToolByAgentId/{agentId}")
    @Operation(summary = "Get Tools By using Agent Id", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Get Tool successfully by using agent id"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    List<Tool> getToolByAgentId(@PathVariable Long agentId);

    /**
     * Retrieves tools by their IDs for SDK integration.
     * Requires ROLE_API_TOOL_READ security role.
     *
     * @param ids List of tool IDs to retrieve
     * @return List of tools as ToolDtoSdk objects
     * @apiNote Response Codes:
     * 200 - Get Tools successfully
     * 500 - Error occurred during retrieval
     */
    @PostMapping(path = "/getToolsByIds")
    @Operation(summary = "Get Tools by Ids", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Get Tools successfully by using list of ids for AI JAVA SDK"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    List<ToolDtoSdk> getToolsByIds(@RequestBody List<Integer> ids);

    /**
     * Retrieves tools by their IDs (version 1).
     * Requires ROLE_API_TOOL_READ security role.
     *
     * @param ids List of tool IDs to retrieve
     * @return List of tools as ToolConvertorDto objects
     * @apiNote Response Codes:
     * 200 - Get Tools successfully
     * 500 - Error occurred during retrieval
     */
    @PostMapping(path = "/v1/getToolsByIds")
    @Operation(summary = "Get Tools by Ids (v1)", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Get Tools successfully by using list of ids"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    List<ToolConvertorDto> getToolsByIdsV1(@RequestBody List<Integer> ids);

    /**
     * Checks if provided source code compiles successfully.
     * Requires ROLE_API_TOOL_READ security role.
     *
     * @param requestMap Map containing sourceCode and className
     * @return Map containing compilation result (true/false)
     * @apiNote Response Codes:
     * 200 - Compilation check completed
     * 500 - Error occurred during check
     */
    @PostMapping(path = "/checkCompilation")
    @Operation(summary = "Check whether source code compiles or not", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "returns true or false based on compilation of source code"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    Map<String, Boolean> checkCompilation(@RequestBody Map<String, String> requestMap);

    /**
     * Registers an agent as a tool using its ID.
     *
     * @param agentId The ID of the agent to register as a tool.
     * @return A map containing the result of the registration operation.
     */
    @Operation(
            summary = "Register an agent as a tool",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TOOL_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Agent registered as tool successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping("/registerAgentAsTool/{id}")
    Map<String, String> registerAgentAsTool(@PathVariable(name = "id") Long agentId);

    /**
     * Exports tools for an application as a downloadable resource.
     *
     * @param appName The application name to export tools for.
     * @return ResponseEntity containing the exported resource.
     */
    @Operation(
            summary = "Export tools for an application",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TOOL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Tools exported successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(value = "/exportTool/{appName}", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    ResponseEntity<Resource> exportTool(@PathVariable String appName);

    /**
     * Imports tools from an uploaded file.
     *
     * @param file The multipart file containing tool data.
     * @return ResponseEntity containing the import result.
     */
    @Operation(
            summary = "Import tools from a file",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TOOL_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Tools imported successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(value = "/importTool", produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseEntity<Resource> importTool(@RequestParam("file") MultipartFile file);

    /**
     * Checks if a tool exists based on the provided criteria.
     *
     * @param requestMap A map containing the criteria to check for tool existence.
     * @return A map containing the result of the existence check.
     */
    @Operation(
            summary = "Check if a tool exists",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TOOL_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Existence check completed successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/existsTool", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, Boolean> existsTool(@RequestBody Map<String, String> requestMap);

    /**
     * Updates a tag by its ID.
     *
     * @param id   The ID of the tag to update.
     * @param tags A map containing the updated tag data.
     * @return A map containing the result of the update operation.
     */
    @Operation(
            summary = "Update tag by ID",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TOOL_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Tag updated successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/updateTagById/{id}")
    Map<String, String> updateTagById(@PathVariable Integer id, @RequestBody Map<String, String> tags);

    @Operation(summary = "Get Tool by Name", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_READ})})
    @GetMapping(path = "/findByName/{toolName}")
    ToolDto getToolByName(@PathVariable String toolName);

    @GetMapping("/findToolByAgentId/{agentId}")
    List<ToolDto> getToolDtoByAgentId(@PathVariable Long agentId);

    @PostMapping(path = "/findToolsByIds")
    List<ToolDto> findToolsByIds(@RequestBody List<Integer> ids);


}




