package com.enttribe.promptanalyzer.dto.nififlow;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessorCreateDto {
    private String parentGroupId;
    private String processorType;
    private String name;
    private int x;
    private int y;
    private List<String> autoTerminatedRelationships;
    private Map<String, Object> properties;
    private String authorization;
}
