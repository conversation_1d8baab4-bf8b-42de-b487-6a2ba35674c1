package com.enttribe.promptanalyzer.model;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Column;
import jakarta.persistence.Enumerated;
import jakarta.persistence.EnumType;

import lombok.Getter;
import lombok.Setter;

/**
 * Entity representing a text-to-speech configuration.
 * Stores details about the service, voice, and gender used for text-to-speech conversion.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Setter
@Getter
@Entity
@Table(name = "TEXT_TO_SPEECH")
public class TextToSpeech {

    /**
     * Unique ID of the text-to-speech configuration.
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition = "INT")
    private Integer id;

    /**
     * Gender of the voice used for text-to-speech.
     */
    @Column(name = "GENDER", columnDefinition = "ENUM('MALE','FEMALE')")
    private String gender;


    /**
     * Name of the voice used for text-to-speech.
     */
    @Column(name = "VOICE_NAME",nullable = true)
    private String voiceName;

    /**
     * Service used for text-to-speech conversion.
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "NAME", nullable = false)
    private Service service;

    }
