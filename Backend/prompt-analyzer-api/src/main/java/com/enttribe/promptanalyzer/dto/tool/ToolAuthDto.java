package com.enttribe.promptanalyzer.dto.tool;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that manages tool authentication configurations.
 * Handles various authentication methods and credentials for tool access.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ToolAuthDto {

    private String authorizationType;
    private String authType;
    private String key;
    private String value;

}
