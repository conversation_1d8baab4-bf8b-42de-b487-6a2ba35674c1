package com.enttribe.conversationalai.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class HttpUtils {

    private static final String BEARER_PREFIX = "Bearer ";
    private static final int READ_TIMEOUT_MS = 600000; // 10 minutes

    // Private constructor to prevent instantiation
    private HttpUtils() {
        throw new IllegalStateException("Utility class");
    }


    private static HttpURLConnection createConnection(String urlString, String method, String token) throws IOException {
        // Use URI to parse and validate, then convert to URL
        URI uri = URI.create(urlString);
        URL url = uri.toURL();

        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod(method);
        if (token != null) {
            connection.setRequestProperty("Authorization", BEARER_PREFIX + token);
        }
        connection.setRequestProperty("Content-Type", "application/json");
        return connection;
    }

    private static String readResponse(HttpURLConnection connection) throws IOException {
        int responseCode = connection.getResponseCode();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                responseCode >= 200 && responseCode < 300
                        ? connection.getInputStream()
                        : connection.getErrorStream()))) {

            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        }
    }

    public static String sendGet(String urlString, Map<String, String> headers) throws IOException {
        HttpURLConnection connection = createConnection(urlString, "GET", null);
        if (headers != null) {
            for (Map.Entry<String, String> header : headers.entrySet()) {
                connection.setRequestProperty(header.getKey(), header.getValue());
            }
        }
        return readResponse(connection);
    }

    public static String sendGet(String urlString, Map<String, String> headers, String token) throws IOException {
        HttpURLConnection connection = createConnection(urlString, "GET", token);
        if (headers != null) {
            for (Map.Entry<String, String> header : headers.entrySet()) {
                connection.setRequestProperty(header.getKey(), header.getValue());
            }
        }
        return readResponse(connection);
    }


    public static String sendPost(String urlString, String body, String token) throws IOException {
        HttpURLConnection connection = createConnection(urlString, "POST", token);
        connection.setDoOutput(true);
        connection.setReadTimeout(READ_TIMEOUT_MS);
        if (body != null && !body.isEmpty()) {
            try (OutputStream os = connection.getOutputStream()) {
                os.write(body.getBytes(StandardCharsets.UTF_8));
                os.flush();
            }
        }
        return readResponse(connection);
    }


    public static String sendPost(String urlString, String body, Map<String, String> headers) throws IOException {

        HttpURLConnection connection = createConnection(urlString, "POST", null);
        connection.setDoOutput(true);

        if (headers != null) {
            for (Map.Entry<String, String> header : headers.entrySet()) {
                connection.setRequestProperty(header.getKey(), header.getValue());
            }
        }

        if (body != null && !body.isEmpty()) {
            try (OutputStream os = connection.getOutputStream()) {
                os.write(body.getBytes(StandardCharsets.UTF_8));
                os.flush();
            }
        }
        return readResponse(connection);
    }


}