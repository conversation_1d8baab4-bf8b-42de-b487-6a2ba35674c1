package com.enttribe.promptanalyzer.util;

import ai.djl.huggingface.tokenizers.HuggingFaceTokenizer;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

@Component
public class ChunkCounter {

    @Value("${tokenizer.path}")
    private Resource tokenizerResource;

    private HuggingFaceTokenizer tokenizer;

    @PostConstruct
    public void init() throws IOException {
        Path tempPath = Files.createTempFile("tokenizer", ".json");
        Files.copy(tokenizerResource.getInputStream(), tempPath, StandardCopyOption.REPLACE_EXISTING);
        tokenizer = HuggingFaceTokenizer.newInstance(tempPath);
    }

    public int countChunks(String text) {
        return tokenizer.encode(text).getTokens().length;
    }
}
