package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.nififlow.NifiFlowDto;

import java.util.List;
import java.util.Map;

/**
 * Manages NiFi flow creation and configuration operations.
 * This service handles the generation and management of NiFi flows,
 * providing functionality to create and configure data processing pipelines.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface NifiFlowService {

    Map<String,String> createTriggerNifi(NifiFlowDto flowDto);

    List<String> getValidationErrorsProcessGroup(String processGroupId);

}
