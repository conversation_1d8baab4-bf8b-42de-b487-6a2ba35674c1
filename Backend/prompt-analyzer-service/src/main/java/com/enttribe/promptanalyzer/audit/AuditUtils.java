package com.enttribe.promptanalyzer.audit;

import com.enttribe.promptanalyzer.dto.prompt.PromptDtoSdk;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public final class AuditUtils {

    private AuditUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    private static final String APPLICATION_NAME = "PROMPT_ANALYZER_APP_NAME";

    /**
     * Generates an audit ID by combining the application name with either a provided audit ID or a new UUID.
     *
     * @param auditId The existing audit ID to use, or null to generate a new UUID
     * @return A formatted string containing the application name and audit ID
     */
    public static String generateAuditId(String auditId) {
        String resolvedAuditId = auditId == null ? UUID.randomUUID().toString() : auditId;
        return String.format("%s_%s", APPLICATION_NAME, resolvedAuditId);
    }

    /**
     * Generates a specific audit ID for JSON rectification operations by combining the application name,
     * a JSON parse identifier, and either a provided audit ID or a new UUID.
     *
     * @param auditId The existing audit ID to use, or null to generate a new UUID
     * @return A formatted string containing the application name, JSON parse identifier, and audit ID
     */
    public static String generateRectifyJsonAuditId(String auditId) {
        String resolvedAuditId = auditId == null ? UUID.randomUUID().toString() : auditId;
        return String.format("%s_json_parse_%s", APPLICATION_NAME, resolvedAuditId);
    }

    /**
     * Generates a new random UUID string for audit purposes.
     *
     * @return A new random UUID as a string
     */
    public static String getAuditId() {
        return UUID.randomUUID().toString();
    }

    /**
     * Creates a map of chat options from a PromptModel object.
     * The map includes model configuration parameters such as the model name,
     * maximum tokens, temperature, and top P values.
     *
     * @param promptModel The PromptModel containing the chat configuration parameters
     * @return A Map containing the chat options
     */
    public static Map<String, Object> getChatOptionsMap(PromptDtoSdk promptModel) {
        Map<String, Object> chatOptionsMap = new HashMap<>();
        chatOptionsMap.put("model", promptModel.getModel());
        chatOptionsMap.put("maxTokens", promptModel.getMaxTokens());
        chatOptionsMap.put("temperature", promptModel.getTemperature());
        chatOptionsMap.put("topP", promptModel.getTopP());

        return chatOptionsMap;
    }

}