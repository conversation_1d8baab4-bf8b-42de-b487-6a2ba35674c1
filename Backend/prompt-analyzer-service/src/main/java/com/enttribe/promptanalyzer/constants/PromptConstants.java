package com.enttribe.promptanalyzer.constants;


/**
 * This class contains constant values used throughout the application 
 * for handling prompt-related operations and responses.
 * Author: VisionWaves
 * Version: 1.0
 */
public class PromptConstants {

    public static final String RESULT = "result";
    public static final String SUCCESS = "success";
    public static final String FAILED = "failed";
     public static final String AUTHORIZATION = "Authorization";
    public static final String VERSION = "version";
    public static final String CONDITION = "condition";
    public static final String PACKAGE = "package";
    public static final String REVISION = "revision";
    public static final String COMPONENT = "component";
    public static final String DISCONNECTED_NODE_ACKNOWLEDGED = "&disconnectedNodeAcknowledged=false";
    public static final String CLIENT_ID = "clientId";
    public static final String APPLICATION_JSON = "application/json" ;
    public static final String COLLECTION_NAME = "collectionName";
    public static final String VECTOR_DATABASE = "vectorDatabase";
    public static final String MILVUS = "milvus";
    public static final String DATABASE_NAME = "databaseName";
    public static final String EMBEDDING_MODEL = "nomic-embed-text-v1_5-preview1";
    public static final String EMBEDDING_MODEL_KEY = "embeddingModel";
    public static final String CHAT_MODEL_KEY = "chatModel";
    public static final String CHAT_MODEL_VALUE = "llama-3.3-70b-versatile";
    public static final String PROVIDER = "provider";
    public static final String PARTIAL_SUCCESS = "partial-success";
    public static final String AGENT_ID = "Agent ID";
    public static final String PROMPT_ID = "Prompt ID";
    public static final String SCHEMA = "schema";
    public static final String DESCRIPTION = "description";
    public static final String CONNECTOR = "connector";
    public static final String RETURN_DIRECT = "Return Direct";
    public static final String COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_APITOOL="com.enttribe.promptanalyzer.function.ApiTool";
    public static final String TEMPLATE_NOT_VALID = "The template string is not valid." ;
    public static final String CONTENT_TYPE = "Content-Type" ;
    public static final String COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_CONNECTORTOOL = "com.enttribe.promptanalyzer.function.ConnectorTool";
    public static final String FAILED_TO_READ_RESOURCE = "Failed to read resource" ;
    public static final String ENDING = "\";\n";
    public static final String SEMICOLON_NEWLINES = "\";\n\n";
    public static final String STATIC_METHOD_DECLARATION = "private static final String METHOD = \"";
    public static final String STATIC_HOSTNAME_DECLARATION = "private static final String HOSTNAME = \"";
    public static final String AUTH_TYPE_DECLARATION = "private static final String AUTHORIZATION_TYPE = \"";
    public static final String AUTH_VALUE_DECLARATION = "private static final String AUTHORIZATION_VALUE = \"";
    public static final String JANUARY = "january";
    public static final String FEBRUARY = "february";
    public static final String MARCH = "march";
    public static final String APRIL = "april";
    public static final String MAY = "may";
    public static final String JUNE = "june";
    public static final String JULY = "july";
    public static final String AUGUST = "august";
    public static final String SEPTEMBER = "september";
    public static final String OCTOBER = "october";
    public static final String NOVEMBER = "november";
    public static final String DECEMBER = "december";
    public static final String USER_QUERY = "userQuery";
    public static final String TIME_ZONE = "timeZone";
    public static final String LOCALE = "locale";
    public static final String NO_TIME_INFORMATION_FOUND = "No time information found";
    public static final String SECOND = "second";
    public static final String MONTH = "month";
    public static final String GRAIN="grain";



  private  PromptConstants() {
        // public constructor to prevent instantiation
    }
}

