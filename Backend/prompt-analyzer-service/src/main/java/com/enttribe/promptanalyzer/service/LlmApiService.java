package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.ai.dto.ChatCompletionRequestDto;

import java.util.Map;

/**
 * Handles interactions with Language Learning Model (LLM) APIs.
 * This service manages chat completions and prompt executions,
 * providing a clean interface for communicating with LLM services.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface LlmApiService {

    Object chatCompletion(ChatCompletionRequestDto requestDto);

    Object executePrompt(String promptId, Map<String, Object> variableMap, Boolean format);

    String executePromptV1(String promptId, Map<String, Object> variableMap);

    String generateFreshSystemPrompt(String userInput, String type);

    String improveSystemPrompt(String userInput, String oldPrompt, String type);

    Map<String, Object> generateTestCase(Integer promptId, Map<String, String> request);

}
