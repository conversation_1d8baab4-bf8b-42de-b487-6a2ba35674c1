package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.ProcessorDao;
import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Processor;
import com.enttribe.promptanalyzer.service.ProcessorService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.ProcessorUtils;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Implementation of the {@link ProcessorService} interface.
 * This service class provides the business logic for managing processors,
 * including operations for searching, creating, updating, and deleting processors.
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class ProcessorServiceImpl implements ProcessorService {

    private static final Logger log = LoggerFactory.getLogger(ProcessorServiceImpl.class);
    private final ProcessorDao processorDao;
    private final CustomFilter customFilter;

    /**
     * Creates a new processor in the system.
     *
     * @param requestDto The processor data transfer object containing the details to be saved
     * @return A Map containing the result status of the operation
     * @throws BusinessException If there's a duplicate key or any other business rule violation
     */
    @Override
    public Map<String, Object> save(ProcessorRequestDto requestDto) {
        try {
            Processor processor = new Processor();
            ProcessorUtils.mapDtoToEntity(requestDto, processor);
            processor.setCreatedTime(new Date());
            processor.setModifiedTime(new Date());
            processorDao.save(processor);
            return Map.of("result", "success");
        } catch (Exception e) {
            if (e.getMessage().contains("unique_key")) {
                throw new BusinessException("duplicate entry for " + requestDto.getKey());
            }
            throw new BusinessException("unable to save processor : " + e.getMessage(), e);
        }
    }

    /**
     * Updates an existing processor identified by its ID.
     *
     * @param id The ID of the processor to update
     * @param requestDto The processor data transfer object containing the updated details
     * @return A Map containing the result status of the operation
     * @throws ResourceNotFoundException If the processor with given ID is not found
     * @throws BusinessException If there's a duplicate key or any other business rule violation
     */
    @Override
    public Map<String, Object> update(Integer id, ProcessorRequestDto requestDto) {
        try {
            Processor processor = processorDao.findById(id).orElseThrow(
                    () -> new ResourceNotFoundException("processor is not found with id : " + id));
            ProcessorUtils.mapDtoToEntity(requestDto, processor);
            processor.setModifiedTime(new Date());
            processorDao.save(processor);
            return Map.of("result", "success");
        } catch (Exception e) {
            if (e.getMessage().contains("unique_key")) {
                throw new BusinessException("duplicate entry for " + requestDto.getKey());
            }
            throw new BusinessException(e.getMessage(), e);
        }
    }

    /**
     * Searches for processors based on the provided filter criteria.
     *
     * @param filter The filter criteria for the search
     * @param offset The starting position of the result set
     * @param size The maximum number of results to return
     * @param orderBy The field to sort the results by
     * @param orderType The sort direction (ascending/descending)
     * @return A list of ProcessorResponseDto containing the matching processors
     * @throws BusinessException If there's an error during the search operation
     */
    @Override
    public List<ProcessorResponseDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        if (filter != null) {
            filter = filter.replace("(", "").replace(")", "");
        }
        try {
            List<Processor> processors = customFilter.searchByFilter(
                    Processor.class, filter, orderBy, orderType, offset, size
            );
            return ProcessorUtils.getProcessorList(processors);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage(), e);
        }
    }

    /**
     * Counts the number of processors matching the given filter criteria.
     *
     * @param filter The filter criteria for counting
     * @return The total number of processors matching the filter
     */
    @Override
    public Long count(String filter) {
        if (filter != null) {
            filter = filter.replace("(", "").replace(")", "");
        }
        return customFilter.countByFilter(Processor.class, filter);
    }
}
