package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.ai.dto.VectorMetaData;
import com.enttribe.promptanalyzer.config.milvusconfig.MilvusVectorStoreProperties;

import java.util.List;
import java.util.Map;

/**
 * Utility class for vector operations related to vector databases.
 * This class provides methods to validate vector database names and generate keys for Milvus vector stores.
 *
 * <AUTHOR>
 */
public final class VectorUtils {

    private static final List<String> SUPPORTED_VECTOR_DB_NAMES = List.of("milvus");
    private static final String FORMAT_PATTERN = "%s_%s_%s";

    private VectorUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    /**
     * Validates if the provided vector database name is supported.
     *
     * @param vectorDatabase the name of the vector database to validate
     * @throws IllegalArgumentException if the vector database name is not supported
     */
    public static void validateVectorDatabaseName(String vectorDatabase) {
        boolean contains = SUPPORTED_VECTOR_DB_NAMES.contains(vectorDatabase);
        if (!contains) throw new IllegalArgumentException("unsupported vector database name: " + vectorDatabase);
    }

    /**
     * Generates a key for the Milvus vector store using the provided configuration and properties.
     *
     * @param vectorStoreConfig           a map containing vector store configuration
     * @param milvusVectorStoreProperties properties of the Milvus vector store
     * @return a string key for the Milvus vector store
     */
    public static String generateMilvusVectorStoreKey(Map<String, Object> vectorStoreConfig,
                                                      MilvusVectorStoreProperties milvusVectorStoreProperties) {
        String vectorDatabase = (String) vectorStoreConfig.get("vectorDatabase");
        VectorUtils.validateVectorDatabaseName(vectorDatabase);
        String databaseName = (String) vectorStoreConfig.get("databaseName");
        databaseName = databaseName == null ? milvusVectorStoreProperties.getDatabaseName() : databaseName;
        String collectionName = (String) vectorStoreConfig.get("collectionName");
        collectionName = collectionName == null ? milvusVectorStoreProperties.getCollectionName() : collectionName;
        return String.format(FORMAT_PATTERN, vectorDatabase, databaseName, collectionName);
    }

    /**
     * Generates a key for the Milvus vector store using the provided metadata.
     *
     * @param metaData metadata containing vector database, database name, and collection name
     * @return a string key for the Milvus vector store
     */
    public static String generateMilvusVectorStoreKey(VectorMetaData metaData) {
        return String.format(FORMAT_PATTERN, metaData.getVectorDatabase(), metaData.getDatabaseName(), metaData.getCollectionName());
    }

    /**
     * Generates a key for the Milvus vector store using the provided metadata.
     *
     * @param vectorMetaData metadata containing vector database, database name, and collection name
     * @return a string key for the Milvus vector store
     */
    public static String generateMilvusVectorStoreKey(Map<String, Object> vectorMetaData) {
        return String.format(FORMAT_PATTERN, vectorMetaData.get("vectorDatabase"), vectorMetaData.get("databaseName"), vectorMetaData.get("collectionName"));
    }

}
