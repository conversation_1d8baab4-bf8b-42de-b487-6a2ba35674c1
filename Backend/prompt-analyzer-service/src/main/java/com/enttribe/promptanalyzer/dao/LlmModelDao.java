package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.model.LlmModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Data Access Object interface for LLM (Language Learning Model) operations.
 * Provides methods to interact with LLM models in the database, including
 * model retrieval, provider management, and SDK-specific queries.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface LlmModelDao extends JpaRepository<LlmModel, Integer> {

    /**
     * Retrieves all models for a specific provider and type.
     *
     * @param provider The provider name
     * @param type The model type
     * @return List of model names
     */
    @Query("SELECT lm.model FROM LlmModel lm WHERE lm.provider = :provider AND lm.type = :type")
    List<String> getModelsByProvider(String provider, String type);

    /**
     * Retrieves all providers and their associated models for a specific type.
     *
     * @param type The model type
     * @return List of Object arrays containing provider and model information
     */
    @Query("SELECT lm.provider, lm.model FROM LlmModel lm WHERE lm.type = :type ORDER BY lm.provider")
    List<Object[]> getAllProvidersWithModels(String type);

    /**
     * Finds a specific LLM model by its model name, provider, and type.
     *
     * @param model The model name
     * @param provider The provider name
     * @param type The model type
     * @return Matching LlmModel entity or null if not found
     */
    @Query("SELECT lm FROM LlmModel lm WHERE lm.model = :model AND lm.provider = :provider AND lm.type = :type")
    LlmModel findByModelAndProvider(String model, String provider, String type);

    /**
     * Retrieves LLM models for SDK use based on application name and type.
     * Returns unique provider configurations for models used in the specified application.
     *
     * @param appName The application name
     * @param type The model type
     * @return List of LlmModelSdkDto containing provider configurations
     */
    @Query("SELECT DISTINCT new com.enttribe.promptanalyzer.dto.LlmModelSdkDto(lm.provider, lm.baseUrl, lm.apiKey)" +
            " FROM LlmModel lm WHERE lm.provider IN (SELECT DISTINCT l.provider FROM LlmModel l WHERE l.type = :type)" +
            "AND lm.id IN (SELECT DISTINCT p.llmModel.id FROM Prompt p WHERE p.application = :appName)")
    List<LlmModelSdkDto> getLlmModelsForSDK(String appName, String type);

    /**
     * Retrieves all LLM models of a specific type for SDK use.
     * Includes complete model information including provider details.
     *
     * @param type The model type
     * @return List of LlmModelSdkDto containing model configurations
     */
    @Query("SELECT DISTINCT new com.enttribe.promptanalyzer.dto.LlmModelSdkDto(lm.provider, lm.baseUrl, lm.apiKey, lm.model)" +
            " FROM LlmModel lm WHERE lm.type = :type")
    List<LlmModelSdkDto> getLlmModelsByTypeForSDK(String type);

    /**
     * Retrieves unique inference configurations for a specific model type.
     * Returns provider-level configurations without model-specific details.
     *
     * @param type The model type
     * @return List of LlmModelSdkDto containing unique provider configurations
     */
    @Query("SELECT DISTINCT new com.enttribe.promptanalyzer.dto.LlmModelSdkDto(lm.provider, lm.baseUrl, lm.apiKey)" +
            " FROM LlmModel lm WHERE lm.type = :type")
    List<LlmModelSdkDto> getUniqueInferencesByType(String type);


    /**
     * Custom query to find model name and provider combination.
     *
     * @param model The model name to search for.
     * @param provider  The provider type to search for.
     * @return LlmModel if found, otherwise null.
     */
    @Query("SELECT m FROM LlmModel m WHERE m.model = :model AND m.provider = :provider")
    LlmModel findModelAndProvider(String model, String provider);
}