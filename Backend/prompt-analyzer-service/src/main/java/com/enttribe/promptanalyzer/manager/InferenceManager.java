package com.enttribe.promptanalyzer.manager;

import com.enttribe.commons.encoder.AESUtils;
import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.service.LlmModelService;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.model.SimpleApiKey;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Manager class responsible for handling inference operations.
 * Initializes and manages OpenAI API instances for different providers.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class InferenceManager {

    private static final Logger log = LoggerFactory.getLogger(InferenceManager.class);
    private static final Map<String, OpenAiApi> openAiApiMap = new HashMap<>();
    private final LlmModelService llmModelService;
    private final EmbeddingModel embeddingModel;

    /**
     * Constructs an InferenceManager with the specified LlmModelService.
     *
     * @param llmModelService the service to retrieve LLM model details
     */
    public InferenceManager(LlmModelService llmModelService, EmbeddingModel embeddingModel) {
        this.llmModelService = llmModelService;
        this.embeddingModel = embeddingModel;
    }

    /**
     * Retrieves the OpenAiApi instance for a given provider.
     *
     * @param provider the provider key
     * @return the OpenAiApi instance associated with the provider
     */
    public OpenAiApi getOpenAiApiByProvider(String provider) {
        log.debug("open api map keys -> {} and provider is : {}", openAiApiMap.keySet(), provider);
        OpenAiApi openAiApi = openAiApiMap.get(provider);
        Assert.notNull(openAiApi, "chat model is not found for the provider " + provider);
        return openAiApi;
    }

    public ChatModel getChatModelByProvider(String provider) {
        log.debug("open api map keys : {} and provider is : {}", openAiApiMap.keySet(), provider);
        return OpenAiChatModel.builder()
                .openAiApi(getOpenAiApiByProvider(provider))
                .build();
    }

    public EmbeddingModel getEmbeddingModel() {
        return embeddingModel;
    }

    /**
     * Initializes chat models by retrieving unique inferences and setting up OpenAI API instances.
     * This method is called after the bean's properties have been set.
     */
    @PostConstruct
    public void initializeChatModels() {
        List<LlmModelSdkDto> inferenceDetails = llmModelService.getUniqueInferencesByType("chat");

        for (LlmModelSdkDto inferenceDetail : inferenceDetails) {
            try {
                String decryptedApiKey = AESUtils.decrypt(inferenceDetail.getApiKey());
                inferenceDetail.setApiKey(decryptedApiKey);
                log.info("api key decrypted successfully");
            } catch (Exception e) {
                log.error("failed to decrypt api key for provider : {}", inferenceDetail.getProvider(), e);
            }
        }

        // Iterate through each inference
        for (LlmModelSdkDto inferenceDetail : inferenceDetails) {
            try {
                // Create OpenAiChatModel instance
                OpenAiApi openAiApi;
                if (inferenceDetail.getProvider().equals("azure")) {
                    openAiApi = OpenAiApi.builder()
                            .baseUrl(inferenceDetail.getBaseUrl())
                            .apiKey(new SimpleApiKey(inferenceDetail.getApiKey()))
                            .completionsPath("")
                            .build();
                } else {
                    openAiApi = OpenAiApi.builder()
                            .baseUrl(inferenceDetail.getBaseUrl())
                            .apiKey(new SimpleApiKey(inferenceDetail.getApiKey()))
                            .build();
                }
                // Populate the maps
                String providerKey = inferenceDetail.getProvider();
                openAiApiMap.put(providerKey, openAiApi);
                log.info("chat model initialized for : {}", providerKey);
            } catch (Exception e) {
                log.error("failed to initialize chat model for provider : {}", inferenceDetail.getProvider(), e);
            }
        }

        log.info("model initialization complete.");
    }


}
