package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDtoSdk;
import com.enttribe.promptanalyzer.dto.prompt.PromptFooDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptRequestDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVersionDetailsDto;
import com.enttribe.promptanalyzer.dto.AssertionTemplateDto;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.rest.PromptRest;
import com.enttribe.promptanalyzer.service.PromptService;
import com.enttribe.promptanalyzer.util.PromptFooUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing prompt operations.
 * Provides endpoints for creating, updating, searching, and managing prompts,
 * including import/export functionality and variable management.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/prompt")
@RequiredArgsConstructor
public class PromptRestImpl implements PromptRest {

    private final PromptService promptService;

    /**
     * Health check endpoint.
     *
     * @return Map containing success status
     */
    @Override
    public Map<String, String> success() {
        log.info("Health check endpoint called.");
        return Map.of("status", "success");
    }

    /**
     * Saves a new prompt.
     *
     * @param prompt The prompt data to save
     * @return Map containing the result of the save operation
     */
    @Override
    public Map<String, String> save(PromptDto prompt) {
        log.info("Saving prompt");
        return promptService.savePrompt(prompt);
    }

    /**
     * Retrieves versions of a prompt based on provided criteria.
     *
     * @param promptRequestDto Contains application, name, category, and status filters
     * @return List of prompt version details
     */
    @Override
    public List<PromptVersionDetailsDto> getVersionsOfPrompt(PromptRequestDto promptRequestDto) {
        log.info("Retrieving versions for prompt");
        return promptService.getVersionsOfPrompt(promptRequestDto.getApplication(), promptRequestDto.getName(), 
            promptRequestDto.getCategory(), promptRequestDto.getStatus());
    }

    /**
     * Updates an existing prompt.
     *
     * @param prompt The updated prompt data
     * @return Map containing the result of the update operation
     */
    @Override
    public Map<String, String> edit(PromptDto prompt) {
        log.info("Updating prompt");
        return promptService.updatePrompt(prompt);
    }

    /**
     * Deletes a prompt by ID (soft delete).
     * Requires ROLE_API_TOOL_WRITE security role.
     *
     * @param map Contains the ID of the prompt to delete
     * @return Map containing the result of the delete operation
     */
    @Override
    public Map<String, String> deleteById(Map<String, Integer> map) {
        log.info("Deleting prompt with ID: {}", map.get("id"));
        return promptService.softDelete(map.get("id"));
    }

    /**
     * Retrieves basic prompt details for a specific application.
     *
     * @param map Map containing the application name
     * @return List of maps containing basic prompt details
     */
    @Override
    public List<Map<String, String>> getPromptBasicDetailByApplication(Map<String, String> map) {
        log.info("Retrieving basic prompt details for application: {}", map.get("application"));
        return promptService.getPromptBasicDetailByApplication(map.get("application"));
    }

    /**
     * Retrieves detailed prompt information by ID.
     *
     * @param map Map containing the prompt ID
     * @return PromptConvertorDto containing the prompt details
     */
    @Override
    public PromptConvertorDto getPromptById(Map<String, Integer> map) {
        log.info("Retrieving prompt by ID: {}", map.get("id"));
        return promptService.getPromptById(map.get("id"));
    }

    /**
     * Retrieves SDK-formatted prompt information by ID.
     *
     * @param id The ID of the prompt to retrieve
     * @return PromptDtoSdk containing the prompt details
     */
    @Override
    public PromptDtoSdk getPromptById(Integer id) {
        log.info("Retrieving SDK-formatted prompt by ID: {}", id);
        return promptService.findPromptById(id);
    }

    /**
     * Checks if a prompt exists based on provided criteria.
     *
     * @param promptRequestDto The criteria to check for existence
     * @return Map containing existence check results
     */
    @Override
    public Map<String, Object> exists(PromptRequestDto promptRequestDto) {
        log.info("Checking existence for prompt");
        return promptService.exists(promptRequestDto);
    }

    /**
     * Retrieves distinct application names, optionally filtered.
     *
     * @param applicationName Optional application name filter
     * @return List of distinct application names
     */
    @Override
    public List<String> getDistinctApplications(String applicationName) {
        log.info("Retrieving distinct applications with filter: {}", applicationName);
        return promptService.getDistinctApplications(applicationName);
    }

    /**
     * Retrieves distinct categories for a specific application.
     *
     * @param applicationName The application name to get categories for
     * @return List of distinct categories
     */
    @Override
    public List<String> getDistinctCategoriesByApp(String applicationName) {
        log.info("Retrieving distinct categories for application: {}", applicationName);
        return promptService.getDistinctCategoriesByApp(applicationName);
    }

    /**
     * Filters prompts based on provided criteria.
     *
     * @param filterMap Map containing filter criteria
     * @return List of filtered prompts as PromptFooDto objects
     */
    @Override
    public List<PromptFooDto> filter(Map<String, Object> filterMap) {
        log.info("Filtering prompts with criteria: {}", filterMap);
        List<Prompt> promptList = promptService.filter(filterMap);
        return PromptFooUtils.getPromptFooDtoList(promptList);
    }

    /**
     * Updates the assertion template for a prompt.
     *
     * @param assertionTemplateDto The updated assertion template data
     * @return Map containing the result of the update operation
     */
    @Override
    public Map<String, String> updateAssertionTemplate(AssertionTemplateDto assertionTemplateDto) {
        log.info("Updating assertion template");
        return promptService.updateAssertionTemplate(assertionTemplateDto);
    }

    /**
     * Searches for prompts with pagination and sorting options.
     *
     * @param filter    Optional filter criteria for searching prompts
     * @param offset    Required pagination offset
     * @param size      Required pagination size
     * @param orderBy   Optional field to order results by
     * @param orderType Optional order direction (asc/desc)
     * @return List of matching prompts as PromptConvertorDto objects
     */
    @Override
    public List<PromptConvertorDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        log.info("Searching prompts");
        return promptService.search(filter, offset, size, orderBy, orderType);
    }

    /**
     * Version 1 of prompt search with pagination and sorting options.
     *
     * @param filter    Optional filter criteria for searching prompts
     * @param offset    Required pagination offset
     * @param size      Required pagination size
     * @param orderBy   Optional field to order results by
     * @param orderType Optional order direction (asc/desc)
     * @return List of matching prompts as PromptFooDto objects
     */
    @Override
    public List<PromptFooDto> searchV1(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        log.info("Searching prompts V1");
        List<Prompt> promptList = promptService.searchV1(filter, offset, size, orderBy, orderType);
        return PromptFooUtils.getPromptFooDtoList(promptList);
    }

    /**
     * Counts the number of prompts matching the optional filter.
     *
     * @param filter Optional filter criteria
     * @return Total count of matching prompts
     */
    @Override
    public Long count(String filter) {
        return promptService.count(filter);
    }

    /**
     * Retrieves prompts for a specific application in SDK format.
     *
     * @param appName The application name to get prompts for
     * @return List of prompts as PromptDtoSdk objects
     */
    @Override
    public List<PromptDtoSdk> getPromptByApplication(String appName) {
        log.info("Retrieving prompts for application: {}", appName);
        return promptService.getPromptByApplication(appName);
    }

    /**
     * Exports prompts for an application as CSV.
     *
     * @param appName The application name to export prompts for
     * @return ResponseEntity containing the CSV resource
     */
    @Override
    public ResponseEntity<Resource> exportCSV(String appName) {
        log.info("Exporting prompts for application: {}", appName);
        return promptService.exportPrompt(appName);
    }

    @Override
    public ResponseEntity<Resource> exportPromptsByIds(List<Integer> promptIds) {
        return promptService.exportPromptsByIds(promptIds);
    }

    /**
     * Imports prompts from a CSV file.
     *
     * @param file The CSV file containing prompt data
     * @return ResponseEntity containing the import result
     */
    @Override
    public ResponseEntity<Resource> importPrompt(MultipartFile file) {
        log.info("Importing prompts from file: {}", file.getOriginalFilename());
        return promptService.importPrompt(file);
    }

    /**
     * Updates tags for a prompt identified by its ID.
     *
     * @param id   the ID of the prompt to update
     * @param tags a map containing the tags to be updated
     * @return a map containing the result of the update operation
     */
    @Override
    public Map<String, String> updateTagById(Integer id, Map<String, String> tags) {
        log.info("Updating tags for prompt ID: {}", id);
        return promptService.updateTagById(id, tags);
    }

    @Override
    public PromptDto getPromptByName(String promptName) {
        return promptService.getPromptByName(promptName);
    }

    @Override
    public PromptDto findPromptById(Integer id) {
        return promptService.fetchPromptById(id);
    }

}


