package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.exception.BusinessException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.model.KnowledgeBase;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

/**
 * Utility class for converting KnowledgeBase entities to KnowledgeBaseResponseDto objects.
 * Provides methods to convert individual entities and lists of entities.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public final class KnowledgeConverter {

    // Private constructor to prevent instantiation
    private KnowledgeConverter() {
    }

    private static final int SPLIT_LIMIT = 2;

    /**
     * Converts a KnowledgeBase entity to a KnowledgeBaseResponseDto object.
     *
     * @param knowledgeBase the KnowledgeBase entity to convert
     * @return the converted KnowledgeBaseResponseDto object, or null if the input is null
     */
    public static KnowledgeBaseResponseDto convertKnowledgeDto(KnowledgeBase knowledgeBase) {
        if (knowledgeBase == null) {
            return null;
        }

        try {
            return KnowledgeBaseResponseDto.builder()
                    .id(knowledgeBase.getId())
                    .name(knowledgeBase.getName())
                    .description(knowledgeBase.getDescription())
                  //  .byteCodeMap(knowledgeBase.getByteCodeMap())
                    .docId(knowledgeBase.getDocId())
                    .docMetaData(knowledgeBase.getDocMetaData())
                    .vectorMetaData(knowledgeBase.getVectorMetaData())
                    .filter(knowledgeBase.getFilter())
                    .isContext(knowledgeBase.getIsContext())
                    .topK(knowledgeBase.getTopK())
                    .similarityThreshold(knowledgeBase.getSimilarityThreshold())
                    .type(knowledgeBase.getType())
                    .className(knowledgeBase.getClassName())
                    .fileName(knowledgeBase.getFileName())
                    .websiteUrl(knowledgeBase.getWebSiteUrl())
                    .collectionName(knowledgeBase.getCollectionName())
                    .tags(knowledgeBase.getTag())
                    .tables(JsonUtils.convertJsonToList(knowledgeBase.getTables(),String.class))
                    .integration(knowledgeBase.getIntegration())
                    .returnDirect(knowledgeBase.getReturnDirect())
                    .s3FileNames(s3FileConvertToJson(knowledgeBase.getS3FileNames(),knowledgeBase.getFileName()))
                    .build();
        } catch (JsonProcessingException e) {
            throw new BusinessException("Exception in Json Processing" + e);
        }
    }

    /**
     * Converts a list of KnowledgeBase entities to a list of KnowledgeBaseResponseDto objects.
     *
     * @param knowledgeBases the list of KnowledgeBase entities to convert
     * @return a list of converted KnowledgeBaseResponseDto objects, or an empty list if the input is null or empty
     */
    public static List<KnowledgeBaseResponseDto> getKnowledgeBasesDtoList(List<KnowledgeBase> knowledgeBases) {
        if (knowledgeBases == null || knowledgeBases.isEmpty()) {
            return List.of();
        }

        return knowledgeBases.stream()
                .map(KnowledgeConverter::convertKnowledgeDto)
                .toList();
    }

    private static String s3FileConvertToJson(String s3FileNames, String fileName) {

        if (s3FileNames == null || s3FileNames.isBlank() || fileName == null || fileName.isBlank()) {
            return s3FileNames;
        }
        try {
            // Check if s3FileNames is already a JSON string and deserialize it first
            List<String> s3List = JsonUtils.convertJsonToList(s3FileNames, String.class); // Deserialize if it's a string
            List<String> fileNameList = Arrays.asList(fileName.split("\\s*,\\s*")); // handles optional spaces

            // Merge label and value preserving label first and value second
            List<Map<String, String>> merged = IntStream.range(0, s3List.size())
                    .mapToObj(i -> Map.of(
                            "label", i < fileNameList.size() ? fileNameList.get(i) : extractFileName(s3List.get(i)),
                            "value", s3List.get(i) // Ensure value comes second
                    ))
                    .toList();

            // Convert the merged list to JSON and return it
            return JsonUtils.convertToJSON(merged); // This will serialize it correctly into JSON format
        } catch (JsonProcessingException e) {
            throw new BusinessException("Failed to convert s3FileNames to JSON: " + e.getMessage());
        }
    }

    private static String extractFileName(String s3Path) {
        String[] parts = s3Path.split("_", SPLIT_LIMIT);
        return parts.length > 1 ? parts[1] : s3Path;
    }
}


