package com.enttribe.promptanalyzer.config;

import com.enttribe.promptanalyzer.dao.ToolDao;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class TestService {

    private static final Logger log = LoggerFactory.getLogger(TestService.class);
    private final ToolDao toolDao;

    @Tool(description = "get list of tool name")
    public List<String> getListOfToolName() {
        log.info("inside @method getListOfToolName");
        return toolDao.getListOfToolName();
    }

    @Tool(description = "get list of tool name given application name")
    public List<String> getListOfToolNameByAppName(@ToolParam(description = "application name") String appName) {
        log.info("inside @method getListOfToolNameByAppName. application name : {}", appName);
        return toolDao.getListOfToolNameByAppName(appName);
    }

    @Tool(description = "Count tool by application name")
    public Long countToolByAppName(@ToolParam(description = "application name") String appName) {
        log.info("inside @method countToolByAppName. application name : {}", appName);
        return toolDao.countToolByAppName(appName);
    }

    @Tool(description = "send email")
    public String sendEmail(@ToolParam(description = "email id of recipient") String email) {
        log.info("inside @method sendEmail. email id of recipient : {}", email);

        return "email sent successfully";
    }

}