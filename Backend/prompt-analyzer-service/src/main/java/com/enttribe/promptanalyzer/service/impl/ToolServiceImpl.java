package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.config.TestService;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.AgentDao;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dao.ToolDao;
import com.enttribe.promptanalyzer.dto.InputTypeSchemaWrapper;
import com.enttribe.promptanalyzer.dto.tool.ApiToolDto;
import com.enttribe.promptanalyzer.dto.tool.GenerateToolDto;
import com.enttribe.promptanalyzer.dto.tool.ToolCreateDto;
import com.enttribe.promptanalyzer.dto.tool.ToolWorkflowDto;
import com.enttribe.promptanalyzer.dto.tool.SwaggerDto;
import com.enttribe.promptanalyzer.dto.tool.ToolAuthDto;
import com.enttribe.promptanalyzer.dto.tool.ToolConvertorDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDtoSdk;
import com.enttribe.promptanalyzer.dto.tool.ToolVersionDetailsDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Agent;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.Tool;
import com.enttribe.promptanalyzer.service.ToolService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.InMemoryClassFileManager;
import com.enttribe.promptanalyzer.util.InMemoryJavaFile;
import com.enttribe.promptanalyzer.util.SdkUtils;
import com.enttribe.promptanalyzer.util.TemplateUtils;
import com.enttribe.promptanalyzer.util.ToolConverter;
import com.enttribe.promptanalyzer.util.UtilsMethods;
import com.enttribe.promptanalyzer.util.CSVUtils;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.tools.JavaCompiler;
import javax.tools.JavaFileObject;
import javax.tools.ToolProvider;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;

/**
 * Implementation of the {@link ToolService} interface.
 * This class provides the actual business logic for managing Tool
 * for a specific application. It interacts with the data access layer to fetch and modify Tool data.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ToolServiceImpl implements ToolService {

    private final ToolDao toolDao;
    private final CustomFilter customFilter;
    private final AgentDao agentDao;
    private final PromptDao promptDao;
    private final TestService testService;
    
    private static final int MAX_IMPORT_RECORDS = 50;

    @Value("classpath:template/api_tool.st")
    private Resource apiTool;

    @Value("classpath:template/connector_tool.st")
    private Resource connectorTool;

    private static final List<String> csvColumnHeader = List.of("Application Name", "Tool Name", "Display Name", "Category",
            "Version", "Type", "Tool ID", "Description", "Class Name", "Request Type","Tags","Byte Code Map", "Source Code", "Tool JSON", "Tool Image", PromptConstants.PROMPT_ID,"Api Tool Info",PromptConstants.AGENT_ID, PromptConstants.RETURN_DIRECT);

  
    /**
     * Creates a new tool based on the provided Tool data.
     * It determines the next version of the tool, generates a unique tool ID,
     * compiles the source code, and saves the tool in the database.
     *
     * @param toolDto the {@link ToolDto} containing tool details such as name, category, status, and source code etc.
     * @return a {@link Map} containing the tool ID and the result status.
     * @throws BusinessException if an error occurs during the tool creation process.
     */
    @Override
    public Map<String, String> createTool(ToolDto toolDto) {
        log.debug("Inside @method createTool");
        Map<String, String> result = new HashMap<>();
        try {
            List<ToolVersionDetailsDto> versionsOfTool = getVersionsOfTool(toolDto.getApplicationName(), toolDto.getName(), toolDto.getCategory(), toolDto.getStatus());
            List<String> versionList = versionsOfTool.stream().map(ToolVersionDetailsDto::getVersion).toList();
            String nextVersion = getNextVersion(versionList, toolDto.getStatus());
            log.debug("createTool with next version : {}", nextVersion);
            toolDto.setVersion(nextVersion);
            Tool tool = getToolEntity(toolDto);
            tool.setToolName(toolDto.getName());
            String toolId = newToolId(toolDto.getApplicationName(), toolDto.getName(), toolDto.getCategory(), nextVersion);
            tool.setToolId(toolId);
            tool.setAgentId(toolDto.getAgentId());

            if (toolDto.getType().equals("API")) {
                ApiToolDto apiToolDto = toolDto.getApiTool();
                log.debug("ApiToolDto : {}", apiToolDto);
                Map<String, Object> variableMap = Map.of(PromptConstants.DESCRIPTION, toolDto.getDescription(),
                        "method", apiToolDto.getMethod(), "url", apiToolDto.getUrl(),
                        "requestBody", apiToolDto.getRequestBody().replace("\"", "\\\""),
                        "headers", apiToolDto.getHeaders().replace("\"", "\\\""));

                String sourceCode = TemplateUtils.getResolvedPrompt(apiTool, variableMap);
                Map<String, byte[]> byteCode = compileSourceCode(sourceCode, PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_APITOOL);
                tool.setByteCodeMap(byteCode);
                tool.setSourceCode(sourceCode);
                tool.setClassName(PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_APITOOL);
                tool.setType("API");
                tool.setApiToolInfo(JsonUtils.convertToJSON(apiToolDto));
            } else if (toolDto.getType().equals(PromptConstants.CONNECTOR)) {
                String operationSpecification = toolDto.getOperationConfig();

                String requestFields = extractVariableKeys(operationSpecification);
                log.debug("requestFields : {}", requestFields);
                Map<String, Object> variableMap = Map.of(PromptConstants.DESCRIPTION, toolDto.getDescription(),
                        "requestFields", requestFields, "name", toolDto.getConnectorName());

                String sourceCode = TemplateUtils.getResolvedPrompt(connectorTool, variableMap);
                Map<String, byte[]> byteCode = compileSourceCode(sourceCode, PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_CONNECTORTOOL);
                tool.setByteCodeMap(byteCode);
                tool.setSourceCode(sourceCode);
                tool.setClassName(PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_CONNECTORTOOL);
                tool.setType(PromptConstants.CONNECTOR);
            } else {
                Map<String, byte[]> byteCode = compileSourceCode(toolDto.getSourceCode(), toolDto.getClassName());
                tool.setByteCodeMap(byteCode);
                tool.setSourceCode(toolDto.getSourceCode());
            }
            tool.setTags(toolDto.getTags());
            tool.setToolImage(toolDto.getToolImage());
            tool.setReturnDirect(toolDto.getReturnDirect());
            log.debug("Going to save the tool");
            Tool savedTool = toolDao.save(tool);
            result.put("toolId", String.valueOf(savedTool.getId()));
            result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
            return result;
        } catch (Exception e) {
            log.error("error while creating Tool {}", e.getMessage(), e);
            if (e instanceof BusinessException ex) {
                throw new BusinessException(ex.getMessage(), e);
            }
            throw new BusinessException("unable to save tool : " + e.getMessage());
        }
    }

    private static String extractVariableKeys(String jsonString) {
        try {
            String unescapedJson = jsonString.replace("\\\"", "\"");

            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(unescapedJson);

            StringBuilder result = new StringBuilder();
            Iterator<String> fieldNames = rootNode.fieldNames();

            while (fieldNames.hasNext()) {
                String key = fieldNames.next();
                String value = rootNode.get(key).asText();

                // Check if value starts with '${'
                if (value.startsWith("${")) {
                    if (result.length() > 0) {
                        result.append(", ");
                    }
                    result.append("String " + key);
                }
            }

            return result.toString();
        } catch (Exception e) {
            throw new BusinessException("failed to extract operationConfig from connector", e);
        }
    }


    /**
     * Updates an existing tool with the provided details.
     * This method retrieves an existing tool by its ID, updates its fields based on the
     * provided {@link ToolDto}, compiles the source code if provided, and saves the updated tool.
     *
     * @param toolDto the {@link ToolDto} containing updated tool details
     * @return a {@link Map} with the update status, where result is set to "success" upon successful update
     * @throws BusinessException if the tool does not exist, required fields are missing,
     *         or an error occurs during the update process
     */
    @Override
    public Map<String, String> updateTool(ToolDto toolDto) {
        log.debug("Inside @method updateTool");
        Map<String, String> result = new HashMap<>();
        try {
            Optional<Tool> existingToolOpt = toolDao.findById(toolDto.getId());
            Tool existingTool = getExistingTool(toolDto, existingToolOpt);

            if (toolDto.getType().equals("API")) {
                ApiToolDto apiToolDto = toolDto.getApiTool();
                log.debug("apiToolDto : {}", apiToolDto);
                Map<String, Object> variableMap = Map.of(PromptConstants.DESCRIPTION, toolDto.getDescription(),
                        "method", apiToolDto.getMethod(), "url", apiToolDto.getUrl(),
                        "requestBody", apiToolDto.getRequestBody().replace("\"", "\\\""),
                        "headers", apiToolDto.getHeaders().replace("\"", "\\\""));

                String sourceCode = TemplateUtils.getResolvedPrompt(apiTool, variableMap);
                Map<String, byte[]> byteCode = compileSourceCode(sourceCode, PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_APITOOL);
                existingTool.setByteCodeMap(byteCode);
                existingTool.setSourceCode(sourceCode);
                existingTool.setClassName(PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_APITOOL);
                existingTool.setType("API");
                existingTool.setApiToolInfo(JsonUtils.convertToJSON(apiToolDto));
            } else if (toolDto.getType().equals(PromptConstants.CONNECTOR)) {
                String operationSpecification = toolDto.getOperationConfig();

                String requestFields = extractVariableKeys(operationSpecification);
                log.debug("requestFields : {}", requestFields);
                Map<String, Object> variableMap = Map.of(PromptConstants.DESCRIPTION, toolDto.getDescription(),
                        "requestFields", requestFields, "name", toolDto.getConnectorName());

                String sourceCode = TemplateUtils.getResolvedPrompt(connectorTool, variableMap);
                Map<String, byte[]> byteCode = compileSourceCode(sourceCode, PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_CONNECTORTOOL);
                existingTool.setByteCodeMap(byteCode);
                existingTool.setSourceCode(sourceCode);
                existingTool.setClassName(PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_CONNECTORTOOL);
                existingTool.setType(PromptConstants.CONNECTOR);
            } else {
                Map<String, byte[]> byteCode = compileSourceCode(toolDto.getSourceCode(), toolDto.getClassName());
                existingTool.setByteCodeMap(byteCode);
                existingTool.setSourceCode(toolDto.getSourceCode());
            }

            extractedUpdateTool(toolDto, existingTool);

            ToolAuthDto toolAuthentication = toolDto.getToolAuthentication();
            if (toolAuthentication != null) {
                existingTool.setAuthType(toolAuthentication.getAuthType());
                existingTool.setAuthKey(toolAuthentication.getKey());
                existingTool.setAuthValue(toolAuthentication.getValue());
            }

            List<ToolVersionDetailsDto> versionsOfTool = getVersionsOfTool(toolDto.getApplicationName(), toolDto.getName(), toolDto.getCategory(), toolDto.getStatus());
            List<String> versionList = versionsOfTool.stream().map(ToolVersionDetailsDto::getVersion).toList();
            String nextVersion = getNextVersion(versionList, toolDto.getStatus());
            log.debug("generateTools with next version : {}", nextVersion);
            toolDto.setVersion(nextVersion);
            if (toolDto.getToolId() != null) {
                existingTool.setToolId(newToolId(toolDto.getApplicationName(),toolDto.getName(),toolDto.getCategory(),toolDto.getVersion()));
            }

            existingTool.setToolImage(toolDto.getToolImage());

            existingTool.setModifiedTime(new Date());
            log.debug("successfully update Tool with new data");
            // Save the updated toolDto entity
            toolDao.save(existingTool);

            result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
            return result;
        } catch (Exception e) {
            log.error("error while updating toolDto", e);
            if (e instanceof BusinessException ex) {
                throw new BusinessException(ex.getMessage(), e);
            }
            throw new BusinessException("unable to update tool", e);
        }
    }

    private static void extractedUpdateTool(ToolDto toolDto, Tool existingTool) {
      log.debug("Inside @method extractedUpdateTool");
        if (toolDto.getLanguage() != null) {
            existingTool.setLanguage(toolDto.getLanguage());
        }
        if (toolDto.getType() != null) {
            existingTool.setType(toolDto.getType());
        }
        if (toolDto.getRequestType() != null) {
            existingTool.setRequestType(toolDto.getRequestType());
        }
        if (toolDto.getStatus() != null) {
            existingTool.setStatus(toolDto.getStatus());
        }
        if (toolDto.getAgentId() != null) {
            existingTool.setAgentId(toolDto.getAgentId());
        }

        if (toolDto.getTags() != null) {
            existingTool.setTags(toolDto.getTags());
        }
        if (toolDto.getHttpMethod() != null) {
            existingTool.setHttpMethod(toolDto.getHttpMethod());
        }

        if (toolDto.getParameters() != null) {
            existingTool.setParameters(toolDto.getParameters());
        }
        if (toolDto.getToolJson() != null) {
            existingTool.setToolJson(toolDto.getToolJson());
        }
        if (toolDto.getUrl() != null) {
            existingTool.setUrl(toolDto.getUrl());
        }
        if (toolDto.getReturnDirect() != null) {
            existingTool.setReturnDirect(toolDto.getReturnDirect());
        }
        log.debug("Successfully updated existing tool");
    }

    private static Tool getExistingTool(ToolDto toolDto, Optional<Tool> existingToolOpt) {
        log.debug("Inside @method getExistingTool");
        if (existingToolOpt.isEmpty()) {
            throw new BusinessException("Tool with ID " + toolDto.getId() + " not found.");
        }
        Tool existingTool = existingToolOpt.get();
        // Update fields from the DTO
        if (toolDto.getName() != null) {
            existingTool.setToolName(toolDto.getName());
        }
        if (toolDto.getClassName() != null) {
            existingTool.setClassName(toolDto.getClassName());
        }
        if (toolDto.getDisplayName() != null) {
            existingTool.setDisplayName(toolDto.getDisplayName());
        }
        if (toolDto.getApplicationName() != null) {
            existingTool.setApplicationName(toolDto.getApplicationName());
        }
        if (toolDto.getCategory() != null) {
            existingTool.setCategory(toolDto.getCategory());
        }
        if (toolDto.getDescription() != null) {
            existingTool.setDescription(toolDto.getDescription());
        }

        if (toolDto.getType() == null) throw new BusinessException("type is not provided");
        log.debug("Successfully get existing tool");
        return existingTool;
    }

    /**
     * Retrieves a list of tools associated with a specific application for SDK.
     * This method fetches all tools linked to the given application name and converts them
     * into a list of {@link ToolDtoSdk} objects.
     *
     * @param appName the name of the application for which tools are to be retrieved
     * @return List of tools for the specified application.
     */
    @Override
    public List<ToolDtoSdk> getToolsByApplication(String appName) {
        List<Tool> toolsByApplication = toolDao.getToolsByApplication(appName);
        return SdkUtils.getToolDtoListSdk(toolsByApplication);
    }

    /**
     * Generates tools from a given Swagger JSON specification.
     * This method parses the Swagger JSON to extract API endpoints and their respective methods.
     * It then generates corresponding tool representations and saves them to the database.
     *
     * @param swaggerDto the {@link SwaggerDto} containing Swagger JSON and tool-related metadata
     * @return a {@link Map} with keys "success" and "failed" representing the count of successfully
     *         and unsuccessfully generated tools
     * @throws BusinessException if an error occurs while processing the Swagger JSON
     */
    @Override
    public Map<String, Integer> generateTools(SwaggerDto swaggerDto) {
        Map<String, Integer> result = new HashMap<>();
        int success = 0;
        int failed = 0;
        String hostname = swaggerDto.getHostname();
        String toolImage = swaggerDto.getToolImage();
        ToolAuthDto toolAuthentication = swaggerDto.getToolAuthentication();
        log.info("Inside @method generateTools. @params : hostName {} authorizationType {}", hostname, toolAuthentication.getAuthorizationType());

        try {
            JSONObject swagger = new JSONObject(swaggerDto.getSwaggerJson());

            Map<String, JSONObject> definitionsMap = new HashMap<>();
            JSONObject definitions = swagger.optJSONObject("definitions");
            if (definitions != null) {
                for (String key : definitions.keySet()) {
                    definitionsMap.put(key, definitions.getJSONObject(key));
                }
            }

            // Parse paths and generate classes
            JSONObject paths = swagger.getJSONObject("paths");
            for (String endpoint : paths.keySet()) {
                JSONObject methods = paths.getJSONObject(endpoint);
                for (String method : methods.keySet()) {
                    boolean toolCreated = createTool(new ToolCreateDto(
                            endpoint, method, methods.getJSONObject(method), hostname, toolAuthentication,
                            definitionsMap, swaggerDto, toolImage));

                    if (toolCreated) {
                        success++;
                    } else {
                        failed++;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error while generating tools from swagger : exception message : {}", e.getMessage(), e);
            throw new BusinessException("unable to create tool from swagger json");
        }

        result.put(PromptConstants.SUCCESS, success);
        result.put(PromptConstants.FAILED, failed);
        return result;
    }

    private boolean createTool(ToolCreateDto request) {
        try {
            Tool tool = generateApiClass(new GenerateToolDto(
                    request.getEndpoint(),
                    request.getMethod(),
                    request.getMethodObj(),
                    request.getHostname(),
                    request.getToolAuthentication().getAuthType(),
                    request.getToolAuthentication().getKey(),
                    request.getToolAuthentication().getValue(),
                    request.getDefinitionsMap(),
                    request.getSwaggerDto().getApplicationName(),
                    request.getSwaggerDto().getCategory()
            ));

            SwaggerDto swaggerDto = request.getSwaggerDto();

            tool.setStatus(swaggerDto.getStatus());
            tool.setTags(swaggerDto.getTags());
            tool.setCreatedTime(new Date());
            tool.setModifiedTime(new Date());

            List<ToolVersionDetailsDto> versionsOfTool = getVersionsOfTool(
                    swaggerDto.getApplicationName(),
                    tool.getToolName(),
                    swaggerDto.getCategory(),
                    swaggerDto.getStatus()
            );

            List<String> versionList = versionsOfTool.stream()
                    .map(ToolVersionDetailsDto::getVersion)
                    .toList();
            String nextVersion = getNextVersion(versionList, swaggerDto.getStatus());
            log.debug("generateTools with next version : {}", nextVersion);
            tool.setVersion(nextVersion);

            String toolId = newToolId(
                    swaggerDto.getApplicationName(),
                    tool.getToolName(),
                    swaggerDto.getCategory(),
                    nextVersion
            );

            tool.setToolId(toolId);
            tool.setToolJson(request.getMethodObj().toString());
            tool.setToolImage(request.getToolImage());
            tool.setReturnDirect(swaggerDto.getReturnDirect());

            toolDao.save(tool);
            return true;
        } catch (Exception e) {
            log.error("error while creating tool : {}", e.getMessage(), e);
            return false;
        }
    }



    /**
     * Changes the status of a tool identified by its ID.
     * This method retrieves the tool from the database using its ID, updates its status,
     * and saves the changes.
     *
     * @param id the unique identifier of the tool
     * @param status the new status to be set
     * @return a {@link Map} containing the result of the operation, with a key result
     *         and value "success" if the update is successful
     * @throws BusinessException if the tool with the given ID does not exist
     */
    @Override
    public Map<String, String> changeToolStatus(Integer id, String status) {
        Tool tool = toolDao.findById(id).orElseThrow(() -> new BusinessException("tool does not exist with id : " + id));
        tool.setStatus(status);
        toolDao.save(tool);

        return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
    }

    /**
     * Generates and saves a tool based on workflow data.
     * This method processes workflow data provided in {@link ToolWorkflowDto},
     * extracts necessary tool details, assigns metadata like version and status,
     * and saves the tool in the database.
     *
     * @param createToolWorkflow the workflow data used to generate the tool
     * @throws BusinessException if any error occurs during tool creation
     */
    @Override
    public void generateToolsFromWorkflow(ToolWorkflowDto createToolWorkflow) {
        log.debug("Inside @method generateToolsFromWorkflow");

        Tool tool = generateToolsFromWorkflowData(createToolWorkflow);

        tool.setStatus(createToolWorkflow.getStatus());
        tool.setTags(createToolWorkflow.getTags());
        tool.setCategory(createToolWorkflow.getCategory());
        tool.setApplicationName(createToolWorkflow.getApplicationName());
        tool.setCreatedTime(new Date());
        tool.setModifiedTime(new Date());

        List<ToolVersionDetailsDto> versionsOfTool = getVersionsOfTool(createToolWorkflow.getApplicationName(), createToolWorkflow.getName(), createToolWorkflow.getCategory(), createToolWorkflow.getStatus());
        List<String> versionList = versionsOfTool.stream().map(ToolVersionDetailsDto::getVersion).toList();
        String nextVersion = getNextVersion(versionList, createToolWorkflow.getStatus());
        log.debug("next version : {}", nextVersion);
        tool.setVersion(nextVersion);
        String toolId = newToolId(createToolWorkflow.getApplicationName(), tool.getToolName(), createToolWorkflow.getCategory(), nextVersion);
        tool.setToolId(toolId);
        tool.setReturnDirect(createToolWorkflow.getReturnDirect());
        log.debug("Going to save ToolsFromWorkflow data");
        toolDao.save(tool);
    }

    /**
     * Retrieves a tool by its ID and converts it into a DTO.
     *
     * @param id the unique identifier of the tool
     * @return  Tool data.
     * @throws IllegalArgumentException if the ID is null
     * @throws BusinessException if no tool is found with the given ID
     */
    @Override
    public ToolConvertorDto getToolById(Integer id) {
        Assert.notNull(id, "id is required");
        Tool tool = toolDao.findById(id).orElseThrow(() -> new BusinessException("tool is not found for id : " + id));
        return ToolConverter.convertToToolDto(tool);
    }

    /**
     * Retrieves a list of tools associated with a specific agent.
     *
     * @param agentId the unique identifier of the agent
     * @return a list of {@link Tool} entities assigned to the given agent
     */
    @Override
    public List<Tool> getToolByAgentId(Long agentId) {
        return toolDao.getToolByAgentId(agentId);
    }

    /**
     * Retrieves a list of tools based on their unique IDs and converts them into DTOs.
     *
     * @param id a list of tool IDs to fetch
     * @return a list of Tools for SDK.
     */

    @Override
    public List<ToolDtoSdk> getToolsByIds(List<Integer> id) {
        List<Tool> tools = toolDao.findAllById(id);
        return SdkUtils.getToolDtoListSdk(tools);
    }

    /**
     * Retrieves a list of tools by their unique IDs and converts them into DTOs for version V1.
     *
     * @param id a list of tool IDs to fetch
     * @return a list of Tools for version V1.
     */
    @Override
    public List<ToolConvertorDto> getToolsByIdsV1(List<Integer> id) {
        List<Tool> tools = toolDao.findAllById(id);
        return ToolConverter.getToolDtoList(tools);
    }

    /**
     * Registers an agent as a tool by creating a new tool based on the agent's information.
     * The function template is generated and applied to the tool's source code,
     * followed by compiling the source code and saving the tool to the database.
     *
     * @param agentId the unique identifier of the agent
     * @return a {@link Map} containing the status of the tool registration operation (success/failure)
     */
    @Override
    public Map<String, String> registerAgentAsTool(Long agentId) {
        log.debug("Inside @method registerAgentAsTool with agentId : {}", agentId);
        Map<String, String> result = new HashMap<>();
        try {
            Agent agent = agentDao.getReferenceById(agentId);
            String funcTemplate = UtilsMethods.getFunctionTemplate();
            if (funcTemplate == null) {
                result.put("status", PromptConstants.FAILED);
                return result;
            }
            funcTemplate = funcTemplate.replace("FUNC_NAME", agent.getName())
                    .replace("PromptConstants.DESCRIPTION", agent.getPurpose());

            Tool tool = setAgentDataIntoTool(agent);
            tool.setSourceCode(funcTemplate);

            Map<String, byte[]> byteCode = compileSourceCode(funcTemplate, agent.getName());
            tool.setByteCodeMap(byteCode);

            toolDao.save(tool);
            log.debug("Save Tool with AgentId : {} ", tool.getToolId());
            result.put("status", PromptConstants.SUCCESS);
            return result;
        } catch (Exception e) {
            log.error("unable to save tool from agentId");
            throw new BusinessException("unable to save tool from agentId", e);
        }
    }

    /**
     * Checks whether the provided source code can be compiled without errors.
     * This method attempts to compile the source code and returns true if compilation is successful,
     * or false if there is an error during the compilation process.
     *
     * @param sourceCode the source code to be compiled
     * @param className the name of the class in the source code
     * @return {@code true} if the compilation is successful, {@code false} otherwise
     */
    @Override
    public boolean checkCompilation(String sourceCode, String className) {
        try {
            compileSourceCode(sourceCode, className);
            return true;
        } catch (Exception e) {
            log.error("error while compiling source code", e);
            return false;
        }
    }

    /**
     * Exports tools data for a specific application as a CSV file.
     * The method retrieves tools associated with the given application name and converts them
     * into a CSV format, using a set of field extractors to extract relevant fields from the tool entities.
     *
     * @param appName the name of the application for which tools are to be exported
     * @return a {@link ResponseEntity<Resource>} containing the generated CSV file as a response
     */
    @Override
    public ResponseEntity<Resource> exportTool(String appName) {
       log.debug("Inside exportTool method");
        List<Tool> toolList = toolDao.getToolsByApplication(appName)
        .stream()
        .filter(tool -> "sourceCode".equals(tool.getType()))
        .toList();

        List<Function<Tool, Object>> fieldExtractors = List.of(
                Tool::getApplicationName,
                Tool::getToolName,
                Tool::getDisplayName,
                Tool::getCategory,
                Tool::getVersion,
                Tool::getType,
                Tool::getToolId,
                Tool::getDescription,
                Tool::getClassName,
                Tool::getRequestType,
                tool -> tool.getTags() == null ? "NULL" : tool.getTags(),
                tool -> {
                    try {
                        return JsonUtils.convertToJSON(tool.getByteCodeMap());
                    } catch (JsonProcessingException e) {
                        return "{}";
                    }},
                tool -> tool.getSourceCode() == null ? "NULL" : tool.getSourceCode(),
                tool -> tool.getToolJson() == null ? "NULL" : tool.getToolJson(),
                tool -> tool.getToolImage() == null ? "NULL" : tool.getToolImage(),
                tool -> Optional.ofNullable(tool.getPrompt()).map(Prompt::getPromptId).orElse(null),
                tool -> tool.getApiToolInfo() == null ? "NULL" : tool.getApiToolInfo(),
                tool -> tool.getAgentId() == null ? "NULL" : tool.getAgentId(),
                Tool::getReturnDirect
        );
        return CSVUtils.exportCSV(toolList, csvColumnHeader, appName, fieldExtractors);
    }

    /**
     * Imports tools data from a CSV file and processes each record to create or update tools.
     * The CSV file is parsed, and each record is transformed into a `Tool` entity which is then
     * saved or updated in the database. The number of records processed is limited by a maximum
     * value to avoid performance issues.
     *
     * @param file the CSV file containing tools data to be imported
     * @return a {@link ResponseEntity<Resource>} indicating the result of the import operation
     * @throws IOException if there is an error reading the CSV file
     * @throws BusinessException if there is a business logic error during import
     */
     @Override
        public ResponseEntity<Resource> importTool(MultipartFile file) {
           log.debug("Inside importTool method");
            int maxRecords = MAX_IMPORT_RECORDS;
            try {
                Function<CSVRecord, Tool> recordTransformer = csvRecord -> {
                    Tool tool = new Tool();
                    mapToolFields(csvRecord, tool);
                    return tool;
                };

                String exportedFileName = file.getOriginalFilename().replaceAll("\\.[^.]*$", "");
                return CSVUtils.importCSV(file, csvColumnHeader, recordTransformer, this::saveOrUpdateTool, exportedFileName, maxRecords);
            } catch (IOException | BusinessException e) {
                log.error("Business exception: {}", e.getMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ByteArrayResource(e.getMessage().getBytes()));
            }
        }

    @Override
    public Map<String, Boolean> existsTool(String toolName) {
        log.debug("Checking existence of Tool with name: {}", toolName);
        boolean exists = toolDao.existsByToolName(toolName);
        log.info("Tool existence check for '{}': {}", toolName, exists);
        return Map.of(PromptConstants.RESULT, exists);
    }

    /**
     * Maps fields from a CSV record to a Tool entity.
     *
     * @param csvRecord The CSV record containing tool data
     * @param tool The Tool entity to populate with data
     */
    private void mapToolFields(CSVRecord csvRecord, Tool tool) {
       log.debug("Going to map CSVRecord to Tool");
        tool.setApplicationName(csvRecord.get("Application Name"));
        tool.setToolName(csvRecord.get("Tool Name"));
        tool.setDisplayName(csvRecord.get("Display Name"));
        tool.setCategory(csvRecord.get("Category"));
        tool.setVersion(csvRecord.get("Version"));
        tool.setType(csvRecord.get("Type"));
        tool.setToolId(csvRecord.get("Tool ID"));
        tool.setDescription(csvRecord.get("Description"));
        tool.setClassName(csvRecord.get("Class Name"));
        tool.setRequestType(csvRecord.get("Request Type"));
        tool.setTags(csvRecord.get("Tags"));
        tool.setByteCodeMap(getByteCode(csvRecord.get("Byte Code Map")));
        tool.setSourceCode(csvRecord.get("Source Code"));
        tool.setToolJson(csvRecord.get("Tool JSON"));
        tool.setToolImage(csvRecord.get("Tool Image"));

        if (csvRecord.get(PromptConstants.AGENT_ID) != null && !csvRecord.get(PromptConstants.AGENT_ID).isEmpty()) {
            tool.setAgentId(Long.valueOf(csvRecord.get(PromptConstants.AGENT_ID)));
        }

        if (csvRecord.get(PromptConstants.PROMPT_ID) != null && !csvRecord.get(PromptConstants.PROMPT_ID).isEmpty()) {
            String promptId = csvRecord.get(PromptConstants.PROMPT_ID);
            Prompt prompt = promptDao.findByPromptId(promptId);
            tool.setPrompt(prompt);
        }
        tool.setApiToolInfo(csvRecord.get("Api Tool Info"));

        if (csvRecord.get(PromptConstants.RETURN_DIRECT) != null && !csvRecord.get(PromptConstants.RETURN_DIRECT).isEmpty()) {
            tool.setReturnDirect(Boolean.valueOf(csvRecord.get(PromptConstants.RETURN_DIRECT)));
        }

        log.debug("successfully map CSVRecord to Tool");

    }


    /**
     * Converts a string to a byte code map.
     *
     * @param byteCode The byte code string
     * @return Map of class names to their corresponding byte code
     * @throws BusinessException if JSON parsing fails
     */
    private Map<String, byte[]> getByteCode(String byteCode) {
        try {
            byteCode = byteCode.replace("\"\"", "\"");
            JSONObject jsonObject = new JSONObject(byteCode);
            Map<String, byte[]> resultMap = new HashMap<>();
            Iterator<String> keys = jsonObject.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                String value = jsonObject.getString(key);
                byte[] bytes = Base64.getDecoder().decode(value);
                resultMap.put(key, bytes);
            }
            log.debug("successfully generate bytecode");
            return resultMap;
        } catch (JSONException e) {
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * Saves a new tool or updates an existing one based on tool name.
     *
     * @param tool The Tool entity to save or update
     */
    private void saveOrUpdateTool(Tool tool) {
        Tool existingTool = toolDao.findByToolName(tool.getToolName());
        if (existingTool != null) {
            mapTool(tool, existingTool);
            existingTool.setModifiedTime(new Date());
            toolDao.save(existingTool);
        } else {
            tool.setStatus("PUBLISH");
            tool.setModifiedTime(new Date());
            tool.setCreatedTime(new Date());
            toolDao.save(tool);
        }
    }

    /**
     * Maps properties from a source Tool to a target Tool entity.
     *
     * @param source The source Tool containing new values
     * @param target The target Tool to update
     */
    private void mapTool(Tool source, Tool target) {
        copyBasicToolFields(source, target);
        copyAdditionalToolFields(source, target);
        log.debug("successfully map inside Tool");
    }

    /**
     * Copies basic fields from a source Tool to a target Tool entity.
     *
     * @param source The source Tool containing new values
     * @param target The target Tool to update
     */
    private void copyBasicToolFields(Tool source, Tool target) {
        try {
            log.debug("Going to copy basic tool fields");
            if (source.getApplicationName() != null) target.setApplicationName(source.getApplicationName());
            if (source.getToolName() != null) target.setToolName(source.getToolName());
            if (source.getDisplayName() != null) target.setDisplayName(source.getDisplayName());
            if (source.getCategory() != null) target.setCategory(source.getCategory());
            if (source.getVersion() != null) target.setVersion(source.getVersion());
            if (source.getType() != null) target.setType(source.getType());
            if (source.getToolId() != null) target.setToolId(source.getToolId());
            if (source.getDescription() != null) target.setDescription(source.getDescription());
            if (source.getClassName() != null) target.setClassName(source.getClassName());
            if (source.getRequestType() != null) target.setRequestType(source.getRequestType());
            log.debug("successfully copy basic tool fields");
        } catch (Exception e) {
            throw new BusinessException("unable to copy basic tool fields", e);
        }
    }

    /**
     * Copies additional fields from a source Tool to a target Tool entity.
     *
     * @param source The source Tool containing new values
     * @param target The target Tool to update
     */
    private void copyAdditionalToolFields(Tool source, Tool target) {
        try {
            log.debug("Going to copy additional tool fields");
            if (source.getTags() != null) target.setTags(source.getTags());
            if (source.getByteCodeMap() != null) target.setByteCodeMap(source.getByteCodeMap());
            if (source.getSourceCode() != null) target.setSourceCode(source.getSourceCode());
            if (source.getToolJson() != null) target.setToolJson(source.getToolJson());
            if (source.getToolImage() != null) target.setToolImage(source.getToolImage());
            if (source.getAgentId() != null) target.setAgentId(source.getAgentId());
            if (source.getPrompt() != null) target.setPrompt(source.getPrompt());
            log.debug("successfully copy additional tool fields");
        } catch (Exception e) {
            throw new BusinessException("unable to copy additional tool fields", e);
        }
    }

    /**
     * Creates a Tool entity from Agent data.
     *
     * @param agent The Agent entity to convert to a Tool
     * @return A new Tool entity populated with Agent data
     */
    private Tool setAgentDataIntoTool(Agent agent) {
        Tool tool = new Tool();
        tool.setAgentId(agent.getId());
        tool.setToolImage(agent.getIcon());
        tool.setClassName(agent.getName());
        tool.setType("AGENT");
        tool.setToolName(agent.getName());
        tool.setDisplayName(agent.getDisplayName());
        tool.setStatus(agent.getStatus());
        tool.setPrompt(agent.getPrompt());
        tool.setCreatedTime(new Date());
        return tool;
    }

    /**
     * Generates a Tool entity from workflow data.
     *
     * @param toolWorkflowDto The DTO containing workflow configuration
     * @return A new Tool entity configured for workflow
     * @throws BusinessException if tool generation fails
     */
    private Tool generateToolsFromWorkflowData(ToolWorkflowDto toolWorkflowDto) {
        log.debug("Generating tools from workflow data for display name: {}", toolWorkflowDto.getDisplayName());
        Tool tool = new Tool();
        try {
            StringBuilder classCode = new StringBuilder();
            String className = getClassName(toolWorkflowDto.getName());
            String toolImage = toolWorkflowDto.getToolImage();

            // Create class boilerplate
            classCode.append("""
                    package com.enttribe.promptanalyzer.function;
                    import com.enttribe.commons.ai.aspect.Body;
                    import com.enttribe.commons.ai.aspect.ValidateParametersAspect;
                    import java.io.IOException;
                    import java.lang.reflect.RecordComponent;
                    import java.net.URI;
                    import java.net.http.HttpClient;
                    import org.slf4j.Logger;
                    import org.slf4j.LoggerFactory;
                    import java.net.http.HttpRequest;
                    import java.net.http.HttpResponse;
                    import java.util.HashMap;
                    import java.util.Map;
                    import java.util.function.Function;
                    import org.springframework.beans.factory.annotation.Autowired;
                    import org.springframework.context.annotation.Description;
                    import org.json.JSONObject;
                    """);

            ToolAuthDto toolAuthentication = toolWorkflowDto.getToolAuthentication();

            String description = toolWorkflowDto.getDescription();
            classCode.append("@Description(\"\"\"\n").append(description).append("\n\"\"\")\n");
            classCode.append("public class ").append(className).append(" implements Function<").append(className).append(".Request, ").append(className).append(".Response> {\n\n");
            classCode.append(PromptConstants.STATIC_METHOD_DECLARATION).append("POST").append(PromptConstants.ENDING);
            classCode.append(PromptConstants.STATIC_HOSTNAME_DECLARATION).append(toolWorkflowDto.getHostName()).append(PromptConstants.ENDING);
            classCode.append("private static final Logger log = LoggerFactory.getLogger(").append(className).append(".class);\n");
            classCode.append(PromptConstants.AUTH_TYPE_DECLARATION).append(toolAuthentication.getAuthType()).append(PromptConstants.ENDING);
            classCode.append(PromptConstants.AUTH_VALUE_DECLARATION).append(toolAuthentication.getValue()).append(PromptConstants.ENDING);
            classCode.append("""
                    @Autowired
                    private ValidateParametersAspect validateParametersAspect;
                    
                    @Override
                    public Response apply(Request request) {
                    try {
                        String validated = validateParametersAspect.validateParameters(request);
                        if (validated != null) {
                            return new Response(validated);
                        }
                    } catch (Throwable e) {
                        log.error("Error while validation parameters, message : {}", e.getMessage(), e);
                    }
                    try {
                        URI uri = URI.create(HOSTNAME);
                        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                                .uri(uri)
                                .header("Content-Type", "application/json");
                        if (!AUTHORIZATION_TYPE.equalsIgnoreCase("NONE")) {
                            requestBuilder.header("Authorization", AUTHORIZATION_TYPE + " " + AUTHORIZATION_VALUE);
                        }
                    
                        HttpClient httpClient = HttpClient.newHttpClient();
                        String payload = extractRequestBody(request);
                            HttpRequest httpRequest = requestBuilder
                                    .POST(HttpRequest.BodyPublishers.ofString(payload))
                                    .build();
                    
                        HttpResponse<String> response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());
                            return new Response(response.body());
                    
                        } catch (IOException | InterruptedException e) {
                            e.printStackTrace();
                        }
                        return null;
                    }
                    private String extractRequestBody(Request request) {
                        JSONObject inputJSON = new JSONObject();
                        JSONObject finalJSON=new JSONObject();
                        try {
                            RecordComponent[] components = request.getClass().getRecordComponents();
                            for (RecordComponent component : components) {
                                if (component.getAnnotation(Body.class) == null) {
                                    Object value = component.getAccessor().invoke(request);
                                    if (value != null) {
                                        inputJSON.put(component.getName(), value);
                                    }
                                }
                            }
                    
                            finalJSON.put("inputs",inputJSON);
                            finalJSON.put("response_mode","blocking");
                            finalJSON.put("user","abc-123");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        return finalJSON.toString();
                    }
                    """);

            // Add Request and Response records
            generateRequestRecord(classCode, toolWorkflowDto.getRequiredParameters());
            classCode.append("  public record Response(String data) {}\n");
            classCode.append("}\n");

            tool.setDisplayName(toolWorkflowDto.getDisplayName());
            tool.setDescription(description);
            tool.setSourceCode(classCode.toString());
            tool.setToolName(className);
            String packageName = "com.enttribe.promptanalyzer.function";
            tool.setClassName(packageName + "." + className);

            tool.setAuthType(toolAuthentication.getAuthorizationType());
            tool.setAuthKey(toolAuthentication.getKey());
            tool.setAuthValue(toolAuthentication.getValue());
            tool.setParameters(toolWorkflowDto.getRequiredParameters() + "");
            tool.setHttpMethod("POST");
            tool.setUrl(toolWorkflowDto.getHostName());
            tool.setType("workflow");
            tool.setToolImage(toolImage);
            tool.setDeleted(false);
            log.debug("Compiling source code for class: {}", className);
            Map<String, byte[]> byteCode = compileSourceCode(classCode.toString(), className);
            tool.setByteCodeMap(byteCode);
        } catch (Exception e) {
            if (e instanceof BusinessException ex) {
                throw new BusinessException(ex.getMessage(), e);
            }
            log.error("Error while creating tool from workflow. exception message : {}", e.getMessage(), e);
            throw new BusinessException("unable to save tool from workflow", e);
        }
        return tool;
    }

    /**
     * Converts an input string to a valid Java class name.
     *
     * @param input The input string to convert
     * @return A properly formatted Java class name
     */
    private String getClassName(String input) {
        input = input.trim();
        if (input.isEmpty()) {
            throw new IllegalArgumentException("Input cannot be empty");
        }
        // Use a StringBuilder for efficient string manipulation
        StringBuilder result = new StringBuilder();

        // Split the input into words based on spaces and capitalize each word
        for (String word : input.split("\\s+")) {
            if (!word.isEmpty()) {
                result.append(Character.toUpperCase(word.charAt(0)))
                      .append(word.substring(1).toLowerCase());
            }
        }

        // Ensure the result is a valid Java identifier
        if (!Character.isJavaIdentifierStart(result.charAt(0))) {
            result.insert(0, '_');
        }

        for (int i = 1; i < result.length(); i++) {
            if (!Character.isJavaIdentifierPart(result.charAt(i))) {
                result.setCharAt(i, '_');
            }
        }

        log.debug("Converted class name: {}", result);
        return result.toString();
    }

    /**
     * Generates a Request record with fields based on required parameters.
     *
     * @param classCode The StringBuilder containing the class code
     * @param requiredParameters List of parameter definitions
     * @throws BusinessException if required parameters are null
     */
    private void generateRequestRecord(StringBuilder classCode, List<Map<String, String>> requiredParameters) {
        log.debug("Generating request record with parameters: {}", requiredParameters);
        if (requiredParameters == null) throw new BusinessException("Required parameters are not provided");
        classCode.append(" public record Request(");
        boolean first = true;
        for (Map<String, String> requiredParameter : requiredParameters) {
            String paramName = requiredParameter.get("name");
            String paramType = requiredParameter.get("type");

            // Capitalize the first letter of the type (e.g., "string" -> "String")
            if (paramType != null && !paramType.isEmpty()) {
                paramType = paramType.substring(0, 1).toUpperCase() + paramType.substring(1);
            }
            if (!first) {
                classCode.append(", ");
            }

            // Append the corrected type and parameter name
            classCode.append(paramType).append(" ").append(paramName);
            first = false;
        }

        // Close the record definition
        classCode.append(") {}\n");
        log.debug("Generated request record: {}", classCode);
    }


    /**
     * Retrieves all versions of a tool based on its identifiers.
     *
     * @param applicationName The name of the application
     * @param toolName The name of the tool
     * @param category The tool category
     * @param status The tool status
     * @return List of tool version details
     */
    private List<ToolVersionDetailsDto> getVersionsOfTool(String applicationName, String toolName, String category, String status) {
        log.debug("Retrieving versions of tool for application: {}, tool: {}, category: {}, status: {}", applicationName, toolName, category, status);
        return toolDao.getVersionsOfTool(applicationName, toolName, category, status);
    }

    /**
     * Generates the next version number for a tool.
     *
     * @param versions List of existing version strings
     * @param status The tool status
     * @return The next version string in format "v-n"
     */
    private String getNextVersion(List<String> versions, String status) {
        log.debug("Determining next version for status: {}", status);
        if (status.equalsIgnoreCase("DRAFT")) return "v-0";
        if (versions == null || versions.isEmpty()) {
            return "v-1"; // Default to v-1 if no versions are provided
        }

        int maxVersion = versions.stream().filter(version -> version.matches("v-\\d+")) // Only consider valid "v-<number>" strings
                .map(version -> version.replace("v-", ""))   // Remove the prefix "v-"
                .mapToInt(Integer::parseInt)                // Parse the numeric part
                .max()                                      // Find the max value
                .orElse(1);                           // Default to 1 if no valid versions

        String nextVersion = "v-" + (maxVersion + 1); // Increment and return the next version
        log.debug("Next version determined: {}", nextVersion);
        return nextVersion;
    }


    /**
     * Searches for Tools based on the provided filter criteria, with pagination and ordering.
     * The method uses the `customFilter` service to search tools that match the given filter,
     * and returns the results as a list of Tool objects. It also supports
     * pagination with `offset` and `size`, as well as ordering by a specified field and order type.
     *
     * @param filter    the filter criteria for searching tools (could be a query string or field condition)
     * @param offset    the starting point for pagination (the index of the first result to fetch)
     * @param size      the maximum number of results to fetch
     * @param orderBy   the field name to order the results by
     * @param orderType the order type (e.g., ascending or descending)
     * @return a list of Tools objects matching the filter criteria, with pagination and ordering applied
     * @throws BusinessException if there is an error during the search process
     */
    @Override
    public List<ToolConvertorDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        try {
            log.debug("Inside @method search. @param : filter -> {}", filter);
            List<Tool> tools = customFilter.searchByFilter(Tool.class, filter, orderBy, orderType, offset, size);
            return ToolConverter.getToolDtoList(tools);
        } catch (Exception e) {
            log.error("Error message : {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }

    /**
     * Compiles Java source code into bytecode.
     *
     * @param sourceCode The Java source code to compile
     * @param className The name of the class being compiled
     * @return A map containing the compiled bytecode, where keys are class names and values are the corresponding bytecode
     * @throws BusinessException if compilation fails or if no Java compiler is available
     */
    private Map<String, byte[]> compileSourceCode(String sourceCode, String className) {
        // Step 1: Prepare the Java Compiler
        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
        Assert.notNull(compiler, "No Java compiler is available. Ensure you are running with a JDK, not a JRE.");
        log.debug("@class ToolServiceImpl @method compileSourceCode class compiler success");
        Map<String, byte[]> byteCodeMap = null;
        try {
            InMemoryClassFileManager fileManager = new InMemoryClassFileManager(
                    compiler.getStandardFileManager(null, null, null));
            log.debug("@class ToolServiceImpl @method compileSourceCode fileManager success");

            // Step 2: Create a JavaFileObject for the source code
            InMemoryJavaFile sourceFile = new InMemoryJavaFile(className, sourceCode);

            log.debug("@class ToolServiceImpl @method compileSourceCode sourceFile success");
            Iterable<JavaFileObject> compilationUnits = List.of(sourceFile);

            // Step 3: Compile the source code
            JavaCompiler.CompilationTask task = compiler.getTask(
                    null, fileManager, null, null, null, compilationUnits);

            if (!Boolean.TRUE.equals(task.call())) {
                throw new IllegalArgumentException("Failed to compile the source code.");
            }
            // Step 4: Extract the bytecode for all classes
            byteCodeMap = fileManager.getAllClassBytes();
        } catch (IllegalArgumentException e) {
            log.error("error during source code compilation. Exception message : {}", e.getMessage(), e);
            throw new BusinessException("An unexpected error occurred while compiling the source code.", e);
        }

        return byteCodeMap;
    }

    /**
     * Converts a ToolDto object into a Tool entity.
     *
     * @param dto The ToolDto object containing the tool data
     * @return A new Tool entity populated with data from the DTO, or null if the input DTO is null
     */
    private Tool getToolEntity(ToolDto dto) {
        if (dto == null) {
            return null;
        }
        Tool tool = new Tool();
        tool.setApplicationName(dto.getApplicationName());
        tool.setCategory(dto.getCategory());
        tool.setToolName(dto.getName());
        tool.setDescription(dto.getDescription());
        tool.setLanguage(dto.getLanguage());
        tool.setSourceCode(dto.getSourceCode());
        tool.setToolId(dto.getToolId());
        tool.setType(dto.getType());
        tool.setStatus(dto.getStatus());
        tool.setVersion(dto.getVersion());
        tool.setClassName(dto.getClassName());
        tool.setDisplayName(dto.getDisplayName());
        tool.setRequestType(dto.getRequestType());
        tool.setCreatedTime(new Date());
        tool.setModifiedTime(new Date());
        tool.setDeleted(false);
        return tool;

    }

    /**
     * Generates a unique tool ID by combining application name, category, tool name, and version.
     *
     * @param application The application name
     * @param name The tool name
     * @param category The tool category
     * @param version The tool version
     * @return A concatenated string representing the unique tool ID, with spaces replaced by underscores
     */
    private String newToolId(String application, String name, String category, String version) {
        return (application + "-" + category + "-" + name + "-" + version).trim().replace(" ", "_");
    }

    /**
     * Counts the number of Tools.
     * The method uses the `customFilter` service to count the number of tools
     * based on the given filter.
     *
     * @param filter the filter criteria for counting tools (could be a query string or field condition)
     * @return the total number of tools that match the filter criteria
     */
    @Override
    public Long count(String filter) {
        return customFilter.countByFilter(Tool.class, filter);
    }

    /**
     * Soft deletes a Tool by marking it as deleted in the database.
     * The tool is not permanently removed; instead, its deleted flag is set to `true`.
     *
     * @param id the unique identifier of the tool to be deleted
     * @return a map containing the result of the soft delete operation ("success" or "failed")
     */
    @Override
    public Map<String, String> softDelete(Integer id) {
        Map<String, String> result = new HashMap<>();
        Optional<Tool> tool = toolDao.findById(id);
        if (tool.isPresent()) {
            Tool tool1 = tool.get();
            tool1.setDeleted(true);
            toolDao.save(tool1);
            result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
        } else {
            result.put(PromptConstants.RESULT, PromptConstants.FAILED);
        }
        return result;
    }

    private Tool generateApiClass(GenerateToolDto dto) {
        log.debug("Generating API class for endpoint: {}, method: {}", dto.getEndpoint(), dto.getMethod());

        String className = toClassName(dto.getEndpoint(), dto.getMethod());
        log.debug("generateApiClass method className : {}, endpoint : {}, method : {}", className, dto.getEndpoint(), dto.getMethod());

        StringBuilder classCode = new StringBuilder();
        String description = generateDescription(dto.getMethodDetails(), dto.getDefinitionsMap());

        // boilerplate + dynamic class content (use dto.get... instead of parameters)
        classCode.append("""
        package com.enttribe.promptanalyzer.function;
        import com.enttribe.commons.ai.aspect.Body;
        import com.enttribe.commons.ai.aspect.ValidateParametersAspect;
        import java.io.IOException;
        import java.lang.reflect.RecordComponent;
        import java.net.URI;
        import java.net.http.HttpClient;
        import java.net.http.HttpRequest;
        import java.net.http.HttpResponse;
        import org.slf4j.Logger;
        import org.slf4j.LoggerFactory;
        import java.util.HashMap;
        import java.util.Map;
        import java.util.function.Function;
        import org.springframework.beans.factory.annotation.Autowired;
        import org.springframework.context.annotation.Description;
        """);

        classCode.append("@Description(\"\"\"\n").append(description).append("\n\"\"\")\n");
        classCode.append("public class ").append(className).append(" implements Function<").append(className).append(".Request, ").append(className).append(".Response> {\n");
        classCode.append("private static final String ENDPOINT = \"").append(dto.getEndpoint()).append("\";\n");
        classCode.append(PromptConstants.STATIC_METHOD_DECLARATION).append(dto.getMethod().toUpperCase()).append(PromptConstants.SEMICOLON_NEWLINES);
        classCode.append(PromptConstants.STATIC_HOSTNAME_DECLARATION).append(dto.getHostName()).append(PromptConstants.SEMICOLON_NEWLINES);
        classCode.append("private static final String ENDPOINT = \"").append(dto.getEndpoint()).append(PromptConstants.ENDING);
        classCode.append(PromptConstants.STATIC_METHOD_DECLARATION).append(dto.getMethod().toUpperCase()).append(PromptConstants.SEMICOLON_NEWLINES);
        classCode.append(PromptConstants.STATIC_HOSTNAME_DECLARATION).append(dto.getHostName()).append(PromptConstants.SEMICOLON_NEWLINES);
        classCode.append("private static final Logger log = LoggerFactory.getLogger(").append(className).append(".class);\n");
        classCode.append(PromptConstants.AUTH_TYPE_DECLARATION).append(dto.getAuthorizationType()).append(PromptConstants.SEMICOLON_NEWLINES);
        classCode.append(PromptConstants.AUTH_VALUE_DECLARATION).append(dto.getAuthorizationKey()).append(PromptConstants.SEMICOLON_NEWLINES);

        // Continue rest of your logic unchanged, using `dto.get...()` wherever needed

        generateRequestRecord(classCode, dto.getMethodDetails(), dto.getDefinitionsMap());
        classCode.append(PromptConstants.AUTH_TYPE_DECLARATION).append(dto.getAuthorizationType()).append(PromptConstants.SEMICOLON_NEWLINES);
        classCode.append(PromptConstants.AUTH_VALUE_DECLARATION).append(dto.getAuthorizationKey()).append(PromptConstants.SEMICOLON_NEWLINES);
        classCode.append("""
                @Autowired
                private ValidateParametersAspect validateParametersAspect;
                
                @Override
                public Response apply(Request request) {
                try {
                    String validated = validateParametersAspect.validateParameters(request);
                    if (validated != null) {
                        return new Response(validated);
                    }
                } catch (Throwable e) {
                    log.error("Error while validation parameters, message : {}", e.getMessage(), e);
                }
                
                String endpoint = replacePathParams(ENDPOINT, request);
                String urlString = HOSTNAME + endpoint;
                try {
                    URI uri = URI.create(urlString);
                    HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                            .uri(uri)
                            .header("Content-Type", "application/json");
                    if (!AUTHORIZATION_TYPE.equalsIgnoreCase("NONE")) {
                        requestBuilder.header("Authorization", AUTHORIZATION_TYPE + " " + AUTHORIZATION_VALUE);
                    }
                
                    HttpClient httpClient = HttpClient.newHttpClient();
                
                    if (METHOD.equalsIgnoreCase("GET")) {
                        HttpRequest httpRequest = requestBuilder.GET().build();
                
                        // Send the HTTP request
                        HttpResponse<String> response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());
                
                        return new Response(response.body());
                    }
                    if (METHOD.equalsIgnoreCase("POST")) {
                        String payload = extractRequestBody(request);
                        HttpRequest httpRequest = requestBuilder
                                .POST(HttpRequest.BodyPublishers.ofString(payload))
                                .build();
                
                        HttpResponse<String> response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());
                        return new Response(response.body());
                    }
                } catch (IOException | InterruptedException e) {
                    e.printStackTrace();
                }
                    return null;
                }
                
                
                private String extractRequestBody(Request request) {
                    try {
                        RecordComponent[] components = request.getClass().getRecordComponents();
                        for (RecordComponent component : components) {
                            if (component.getAnnotation(Body.class) != null) {
                                Object authValue = component.getAccessor().invoke(request);
                                if (authValue != null) {
                                    return authValue.toString();
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return "";
                }
                
                private Map<String, String> extractPathParams(Request request) {
                    Map<String, String> pathParams = new HashMap<>();
                    try {
                        RecordComponent[] components = request.getClass().getRecordComponents();
                        for (RecordComponent component : components) {
                            if (component.getAnnotation(Body.class) == null) {
                                Object authValue = component.getAccessor().invoke(request);
                                if (authValue != null) {
                                    pathParams.put(component.getName(), authValue.toString());
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return pathParams;
                }
                
                private String replacePathParams(String endpoint, Request request) {
                    Map<String, String> pathParams = extractPathParams(request);
                    for (Map.Entry<String, String> entry : pathParams.entrySet()) {
                        String placeholder = "{" + entry.getKey() + "}";
                        if (endpoint.contains(placeholder)) {
                            endpoint = endpoint.replace(placeholder, entry.getValue());
                        }
                    }
                    return endpoint;
                }
                """);

        // Add Request and Response records
        generateRequestRecord(classCode, dto.getMethodDetails(), dto.getDefinitionsMap());
        generateResponseRecord(classCode);
        classCode.append("}\n");

        Tool tool = new Tool();
        tool.setApplicationName(dto.getApplicationName());
        tool.setCategory(dto.getCategory());
        tool.setToolName(className);
        tool.setClassName("com.enttribe.promptanalyzer.function." + className);
        tool.setDisplayName(className);
        tool.setHttpMethod(dto.getMethod());
        tool.setAuthType(dto.getAuthorizationType());
        tool.setAuthKey(dto.getAuthorizationKey());
        tool.setAuthValue(dto.getAuthValue());
        tool.setDescription(description);
        tool.setSourceCode(classCode.toString());
        tool.setUrl(dto.getEndpoint());
        tool.setType("swaggerJson");
        tool.setDeleted(false);
        log.debug("Generated API class code: {}", classCode);

        Map<String, byte[]> byteCode = compileSourceCode(classCode.toString(), className);
        tool.setByteCodeMap(byteCode);

        return tool;
    }

    /**
     * Converts an endpoint path and HTTP method into a valid Java class name.
     *
     * @param endpoint The API endpoint path
     * @param method The HTTP method
     * @return A camelCase class name derived from the endpoint and method
     */
    private static String toClassName(String endpoint, String method) {
        String[] parts = endpoint.split("/");
        StringBuilder className = new StringBuilder(method.substring(0, 1).toUpperCase() + method.substring(1).toLowerCase());
        for (String part : parts) {
            if (!part.isEmpty() && !part.contains("{")) {
                className.append(part.substring(0, 1).toUpperCase()).append(part.substring(1));
            }
        }
        return className.toString();
    }

    /**
     * Generates a human-readable description from Swagger method details.
     *
     * @param methodDetails JSON object containing the endpoint's details
     * @param definitionsMap Map of Swagger definitions for resolving references
     * @return A formatted string containing the API description and parameters
     */
    private String generateDescription(JSONObject methodDetails, Map<String, JSONObject> definitionsMap) {
        StringBuilder description = new StringBuilder();
        description.append("API Description: ").append(methodDetails.optString("summary", "No description available")).append("\n");

        JSONArray parameters = methodDetails.optJSONArray("parameters");
        if (parameters != null) {
            description.append("Parameters:\n");
            Iterator<Object> params = parameters.iterator();
            while (params.hasNext()) {
                JSONObject param = (JSONObject) params.next();
                String paramName = param.optString("name");
                JSONObject schema = param.optJSONObject(PromptConstants.SCHEMA);
                String paramType = mapSwaggerTypeToJava(param.optString("type"));
                description.append("- ").append(paramName).append(": ").append(paramType).append("\n");
                if (schema != null) {
                    JSONObject schemaObject = resolveSchema(schema, definitionsMap);
                    schemaObject.remove("xml");
                    description.append("  - Schema: ").append(schemaObject.toString()).append("\n");
                }
            }
        }

        return description.toString();
    }

    /**
     * Generates a Request record class with fields based on Swagger parameters.
     *
     * @param classCode StringBuilder to append the generated code
     * @param methodDetails JSON object containing the endpoint's details
     * @param definitionsMap Map of Swagger definitions for resolving references
     */
    private void generateRequestRecord(StringBuilder classCode, JSONObject methodDetails, Map<String, JSONObject> definitionsMap) {
        classCode.append("    public record Request(");

        // Dynamically generate fields for the Request record based on Swagger JSON
        JSONArray parameters = methodDetails.optJSONArray("parameters");

        if (parameters != null) {
            Iterator<Object> params = parameters.iterator();
            boolean first = true;

            while (params.hasNext()) {
                JSONObject param = (JSONObject) params.next();
                String paramName = "";
                String paramType = "";
                if (param.has(PromptConstants.SCHEMA)) {
                    JSONObject schema = param.getJSONObject(PromptConstants.SCHEMA);
                    JSONObject resolvedSchema = resolveSchema(schema, definitionsMap);
                    resolvedSchema.remove("xml");
                    paramName = param.optString("name");
                    paramType = mapSwaggerTypeToJava(param.optString("type"));
                    classCode.append("@Body(value=\"").append(resolvedSchema.toString().replace("\"", "\\\"")).append("\") ")
                            .append(paramType).append(" ").append(paramName);
                } else {
                    paramName = param.optString("name");
                    paramType = mapSwaggerTypeToJava(param.optString("type"));
                    classCode.append(paramType).append(" ").append(paramName);
                }

                if (!first) {
                    classCode.append(", ");
                }

                first = false;
            }
        }

        classCode.append(") {}\n");

    }

    /**
     * Generates a Response record class for the API endpoint.
     *
     * @param classCode StringBuilder to append the generated code
     */
    private void generateResponseRecord(StringBuilder classCode) {
        classCode.append("    public record Response(String data) {}\n");
    }

    /**
     * Maps Swagger data types to corresponding Java types.
     *
     * @param swaggerType The Swagger data type
     * @return The corresponding Java type
     */
    private String mapSwaggerTypeToJava(String swaggerType) {
        return switch (swaggerType) {
            case "string" -> "String";
            case "integer" -> "Integer";
            case "boolean" -> "Boolean";
            case "array" -> "List";
            case "object" -> "Object";
            default -> "String"; // Default type
        };
    }

    /**
     * Resolves Swagger schema references to their full definitions.
     *
     * @param schema The schema object that may contain a reference
     * @param definitionsMap Map of Swagger definitions
     * @return The resolved schema object
     */
    private JSONObject resolveSchema(JSONObject schema, Map<String, JSONObject> definitionsMap) {
        if (schema.has("$ref")) {
            String ref = schema.getString("$ref");
            String definitionKey = ref.replace("#/definitions/", "");
            JSONObject definition = definitionsMap.get(definitionKey);
            if (definition != null) {
                return definition;
            }
        }
        return schema;
    }

    @Override
    public Map<String, String> updateTagById(Integer id, Map<String, String> tags) {
        String newTag = tags.get("tags");
        log.debug("inside @method updateTagById. @param  : id -> {} tags : {}", id, newTag);
        Tool tool = toolDao.findById(id).orElseThrow(() -> new BusinessException("tool is not found for id : " + id));

        try {
            tool.setTags(newTag);
            toolDao.save(tool);
            return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
        } catch (Exception e) {
            log.error("error while updating tag of tool : {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }

    @Override
    public List<HashMap<String, String>> getToolCallbackProvider() {
        log.info("Starting getToolCallbackProvider method");
        try {
            // Create a new MethodToolCallbackProvider directly
            MethodToolCallbackProvider provider = MethodToolCallbackProvider.builder()
                    .toolObjects(testService)
                    .build();

            ToolCallback[] callbacks = provider.getToolCallbacks();
            log.info("Found {} tool callbacks", callbacks.length);

            if (callbacks.length == 0) {
                log.warn("No tool callbacks found. Checking method annotations...");
                // Log all methods with @Tool annotation
                Arrays.stream(this.getClass().getDeclaredMethods())
                        .filter(method -> method.isAnnotationPresent(org.springframework.ai.tool.annotation.Tool.class))
                        .forEach(method -> {
                            org.springframework.ai.tool.annotation.Tool tool = method.getAnnotation(org.springframework.ai.tool.annotation.Tool.class);
                            log.info("Found @Tool method: {} - {} - {}", method.getName(), tool.description(), tool.returnDirect());
                        });
            }

            return Arrays.stream(callbacks).map(tool -> {
                HashMap<String, String> map = new HashMap<>();
                map.put(PromptConstants.DESCRIPTION, tool.getToolDefinition().description());
                map.put("name", tool.getToolDefinition().name());
                InputTypeSchemaWrapper inputTypeSchemaWrapper = new InputTypeSchemaWrapper(tool.getToolDefinition().inputSchema());
                map.put(PromptConstants.SCHEMA, inputTypeSchemaWrapper.getSchema());
                map.put("type", inputTypeSchemaWrapper.getType());
                map.put("properties", inputTypeSchemaWrapper.getProperties());
                map.put("required", inputTypeSchemaWrapper.getRequired());
                map.put("additionalProperties", inputTypeSchemaWrapper.isAdditionalProperties());
                return map;
            }).toList();
        } catch (Exception e) {
            log.error("Error in getToolCallbackProvider: ", e);
            throw e;
        }
    }

    @Override
    public ToolDto getToolByName(String toolName) {
        log.debug("Searching for tool with name: {}", toolName);

        return Optional.ofNullable(toolDao.findByToolName(toolName))
                .map(ToolConverter::mapToToolDto)
                .orElseThrow(() -> new ResourceNotFoundException("Tool with name '" + toolName + "' not found."));
    }

    @Override
    public List<ToolDto> getToolsDtoByAgentId(Long agentId) {
        return Optional.ofNullable(toolDao.getToolByAgentId(agentId))
                .filter(list -> !list.isEmpty())
                .map(ToolConverter::mapToToolDtoList)
                .orElseThrow(() -> new ResourceNotFoundException("No tools found for agent ID: " + agentId));
    }

    @Override
    public List<ToolDto> findToolsByIds(List<Integer> ids) {
        List<Tool> tools = toolDao.findAllById(ids);
        log.debug("Found {} tools for IDs: {}", tools.size(), ids);
        return ToolConverter.mapToToolDtoList(tools);
    }



}


