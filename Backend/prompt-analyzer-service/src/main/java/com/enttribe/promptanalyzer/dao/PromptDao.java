package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.dto.prompt.PromptVersionDetailsDto;
import com.enttribe.promptanalyzer.model.Prompt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Data Access Object interface for Prompt entity operations.
 * Provides methods to interact with the Prompt table in the database,
 * including version management, prompt retrieval, and application-specific queries.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface PromptDao extends JpaRepository<Prompt, Integer> {

    /**
     * Retrieves versions of a prompt based on application, name, category, and status.
     * Only returns non-deleted prompts.
     *
     * @param applicationName The name of the application
     * @param name The name of the prompt
     * @param category The category of the prompt
     * @param status The status of the prompt
     * @return List of prompt version details
     */
    @Query("SELECT NEW com.enttribe.promptanalyzer.dto.prompt.PromptVersionDetailsDto(p.id, p.version) FROM Prompt p WHERE p.application = :applicationName AND p.name = :name AND p.category = :category AND p.status = :status AND p.deleted = false")
    List<PromptVersionDetailsDto> getVersionsOfPrompt(String applicationName, String name, String category, String status);

    /**
     * Checks if a prompt exists with the given name, application, category, and status.
     *
     * @param name The name to check
     * @param application The application name
     * @param category The category
     * @param status The status
     * @return true if prompt exists, false otherwise
     */
    @Query("SELECT CASE WHEN COUNT(p) > 0 THEN TRUE ELSE FALSE END FROM Prompt p WHERE p.name = :name AND p.application = :application AND p.category = :category AND p.status = :status")
    boolean existsByNameAndApplicationAndCategoryAndStatus(String name, String application, String category, String status) ;

    /**
     * Retrieves a non-deleted prompt by its promptId.
     *
     * @param promptId The unique identifier of the prompt
     * @return The matching Prompt entity or null if not found
     */
    @Query("SELECT p FROM Prompt p WHERE p.promptId = :promptId AND p.deleted = false")
    Prompt getPromptByPromptId(@Param("promptId") String promptId);

    /**
     * Retrieves all published, non-deleted prompts for a specific application.
     *
     * @param applicationName The name of the application
     * @return List of matching Prompt entities
     */
    @Query("SELECT p FROM Prompt p WHERE p.application = :applicationName AND p.deleted = false AND p.status = 'PUBLISH'")
    List<Prompt> getPromptByApplication(@Param("applicationName") String applicationName);

    /**
     * Retrieves distinct application names, optionally filtered by a partial name match.
     *
     * @param applicationName Optional partial application name to filter by
     * @return List of distinct application names
     */
    @Query("SELECT DISTINCT p.application FROM Prompt p WHERE :applicationName IS NULL OR p.application LIKE %:applicationName%")
    List<String> getDistinctApplications(@Param("applicationName") String applicationName);

    /**
     * Retrieves distinct categories for a specific application.
     *
     * @param applicationName The name of the application
     * @return List of distinct categories
     */
    @Query("SELECT DISTINCT p.category FROM Prompt p WHERE p.application = :applicationName")
    List<String> getDistinctCategoriesByApp(String applicationName);

    /**
     * Retrieves basic details (name and promptId) of published, non-deleted prompts for an application.
     *
     * @param applicationName The name of the application
     * @return List of Object arrays containing name and promptId
     */
    @Query("SELECT p.name, p.promptId FROM Prompt p WHERE p.application = :applicationName AND p.deleted = false AND p.status = 'PUBLISH'")
    List<Object[]> getPromptBasicDetailByApplication(String applicationName);

    /**
     * Finds a non-deleted prompt by its promptId.
     *
     * @param promptId The unique identifier of the prompt
     * @return The matching Prompt entity or null if not found
     */
    @Query("SELECT p FROM Prompt p WHERE p.promptId = :promptId AND p.deleted = false")
    Prompt findByPromptId(String promptId);

    @Query("SELECT p FROM Prompt p WHERE p.name = :promptName AND p.deleted = false")
    Prompt findByName(String promptName);
}

