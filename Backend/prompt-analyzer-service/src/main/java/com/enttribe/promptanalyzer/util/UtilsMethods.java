package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import javax.tools.JavaCompiler;
import javax.tools.JavaFileObject;
import javax.tools.ToolProvider;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Utility class providing methods for reading function templates and compiling Java source code.
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class UtilsMethods {

    // Private constructor to prevent instantiation
    private UtilsMethods() {
    }

    /**
     * Reads and returns the content of the function template file.
     *
     * @return the content of the function template file as a String, or null if an error occurs
     */
    public static String getFunctionTemplate() {
        try {
            Path path = Paths.get(UtilsMethods.class.getClassLoader().getResource("function_template.txt").toURI());
            return Files.readString(path);
        } catch (IOException | URISyntaxException e) {
            log.error("Error reading function template file: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Compiles Java source code and returns a map of class names to their compiled byte arrays.
     *
     * @param sourceCode the Java source code to compile
     * @param className  the name of the class being compiled
     * @return a map of class names to their compiled byte arrays
     * @throws BusinessException if an error occurs during compilation
     */
    public static Map<String, byte[]> getCompileSourceCode(String sourceCode, String className) {
        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
        Assert.notNull(compiler, "No Java compiler is available. Ensure you are running with a JDK, not a JRE.");
        log.debug("@class UtilsMethods @method compileSourceCode class compiler success");
        Map<String, byte[]> byteCodeMap = null;
        try {
            InMemoryClassFileManager fileManager = new InMemoryClassFileManager(
                    compiler.getStandardFileManager(null, null, null));
            log.debug("@class UtilsMethods @method compileSourceCode fileManager success");

            // Step 2: Create a JavaFileObject for the source code
            InMemoryJavaFile sourceFile = new InMemoryJavaFile(className, sourceCode);

            log.debug("@class UtilsMethods @method compileSourceCode sourceFile success");
            Iterable<JavaFileObject> compilationUnits = List.of(sourceFile);

            // Step 3: Compile the source code
            JavaCompiler.CompilationTask task = compiler.getTask(
                    null, fileManager, null, null, null, compilationUnits);

            if (!task.call().booleanValue()) {
                throw new IllegalArgumentException("Failed to compile the source code.");
            }
            // Step 4: Extract the bytecode for all classes
            byteCodeMap = fileManager.getAllClassBytes();
        } catch (IllegalArgumentException e) {
            log.error("error during source code compilation. Exception message : {}", e.getMessage(), e);
            throw new BusinessException("An unexpected error occurred while compiling the source code.", e);
        }

        return byteCodeMap;
    }

    public static Map<String, String> buildJsonMap(List<Map<String, Object>> dataList) {
        Map<String, String> result = new HashMap<>();
        for (Map<String, Object> data : dataList) {
            String id = (String) data.get("id");
            String name = (String) data.get("displayName");
            String description = (String) data.get("description");
            String pkg = (String) data.get("key");
            String properties = data.get("properties") != null ? data.get("properties").toString() : "";

            String json = !properties.isEmpty()
                    ? String.format(
                    "{\"name\": \"%s\",\"description\": \"%s\",\"package\": \"%s\",\"properties\": %s}",
                    name, description, pkg, properties)
                    : String.format(
                    "{\"name\": \"%s\",\"description\": \"%s\",\"package\": \"%s\"}",
                    name, description, pkg);

            result.put(String.valueOf(id), json);
        }
        return result;
    }
}


