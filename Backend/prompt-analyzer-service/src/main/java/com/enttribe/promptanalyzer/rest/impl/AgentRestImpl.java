package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.agent.AgentConvertDto;
import com.enttribe.promptanalyzer.dto.agent.AgentDto;
import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;
import com.enttribe.promptanalyzer.dto.nififlow.NifiFlowDto;
import com.enttribe.promptanalyzer.model.AgentHistory;
import com.enttribe.promptanalyzer.rest.AgentRest;
import com.enttribe.promptanalyzer.service.AgentHistoryService;
import com.enttribe.promptanalyzer.service.AgentService;
import com.enttribe.promptanalyzer.service.CustomAgentService;
import com.enttribe.promptanalyzer.service.NifiFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing agent-related operations.
 * Provides endpoints for creating agents, searching agents, and managing NiFi triggers.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/agent")
@RequiredArgsConstructor
public class AgentRestImpl implements AgentRest {

    private final AgentService agentService;
    private final CustomAgentService customAgentService;
    private final NifiFlowService nifiFlowService;
    private final AgentHistoryService agentHistoryService;

    /**
     * Creates a new agent.
     *
     * @param agent The agent details to create
     * @return Map containing the result of the operation
     */
    @Override
    public Map<String, String> create(AgentDto agent) {
        return agentService.createAgent(agent);
    }

    /**
     * Searches for agents based on provided filters and pagination parameters.
     *
     * @param filter Optional filter string to search agents
     * @param offset Optional pagination offset
     * @param size Optional pagination size
     * @param orderby Optional field to order results by
     * @param orderType Optional order direction (asc/desc)
     * @return List of matching agents converted to DTOs
     */
    @Override
    public List<AgentConvertDto> search(String filter, Integer offset, Integer size, String orderby, String orderType) {
        return agentService.search(filter, offset, size, orderby, orderType);
    }

    /**
     * Generates a plan based on user query for custom agent.
     *
     * @param agent Custom agent details containing the user query
     * @return Map containing the generated plan response
     */
    @Override
    public Map<String, String> getPlanforUserQuery(CustomAgentDto agent) {
        Map<String, String> planResponse = customAgentService.getPlanforUserQuery(agent);
        log.info("Plan response is {}", planResponse.toString());
        return planResponse;
    }

    @Override
    public AgentHistory getAgentHistory(String agentName) {
        return agentHistoryService.getAgentHistory(agentName);
    }

    /**
     * Creates a trigger in NiFi based on flow configuration.
     *
     * @param flowDto Flow configuration details
     * @return Map containing the result of trigger creation
     */
    @Override
    public Map<String, String> createTriggerInNifi(NifiFlowDto flowDto) {
        return nifiFlowService.createTriggerNifi(flowDto);
    }

    /**
     * Generates a trigger name and description based on user query.
     *
     * @param requestBody Map containing the user query under "userQuery" key
     * @return Map containing the generated trigger name and description
     */
    @Override
    public Map<String, String> createTriggerName(Map<String, String> requestBody) {
        return customAgentService.createTriggerNameDescription(requestBody.get("userQuery"));
    }








}




