package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.TriggerDao;
import com.enttribe.promptanalyzer.dto.trigger.TriggerRequestDto;
import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Trigger;
import com.enttribe.promptanalyzer.service.TriggerService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.TriggerConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Comparator;
import java.util.Optional;

/**
 * Implementation of the {@link TriggerService} interface.
 * This class provides functionality for managing triggers in the system, including
 * searching, creating, and updating trigger records. It handles the business logic
 * for trigger operations and interacts with the persistence layer through the DAO.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TriggerServiceImpl implements TriggerService {

   private final CustomFilter customFilter;
   private final TriggerDao triggerDao;

    /**
     * Searches for triggers based on the provided filter criteria.
     *
     * @param filter    The filter criteria to search triggers
     * @param offset    The starting position of the result set
     * @param size      The maximum number of results to return
     * @param orderBy   The field to sort the results by
     * @param orderType The sort direction (ascending/descending)
     * @return List of TriggerResponseDto containing the search results
     * @throws BusinessException if an error occurs during the search operation
     */
    @Override
    public List<TriggerResponseDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        try {
            log.debug("Inside @method search. @param : filter -> {}", filter);
            List<Trigger> triggers = customFilter.searchByFilter(Trigger.class, filter, orderBy, orderType, offset, size);
            log.debug("Number of triggers found: {}", triggers.size());
            // Sort the list to ensure "Recurrence" comes first
            triggers.sort(Comparator.comparing((Trigger t) -> !"Recurrence".equalsIgnoreCase(t.getType()))
                    .thenComparing(Trigger::getId)); // Preserving original order for other fields

            return TriggerConverter.getTriggerDtoList(triggers);
        } catch (Exception e) {
            log.error("Error while searching for triggers: {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }

    /**
     * Counts the number of triggers matching the given filter criteria.
     *
     * @param filter The filter criteria to count triggers
     * @return The total number of triggers matching the filter
     */
    @Override
    public Long count(String filter) {
        return customFilter.countByFilter(Trigger.class, filter);
    }

    /**
     * Creates a new trigger based on the provided request data.
     *
     * @param dto The TriggerRequestDto containing the trigger details
     * @return Map containing the status of the operation
     * @throws BusinessException if an error occurs while saving the trigger
     */
    @Override
    public Map<String, String> createTrigger(TriggerRequestDto dto) {
        log.debug("Inside createTrigger method with name: {}", dto.getName());
        Map<String, String> response = new HashMap<>();
        try {
            Trigger trigger = new Trigger();
            trigger = mapToEntity(trigger, dto);
            trigger.setCreatedTime(new Date());
            triggerDao.save(trigger);
            log.info("Trigger created successfully with ID: {}", trigger.getId());
            response.put("status", "success");
        } catch (Exception e) {
            log.error("Unable to save Trigger: {}", e.getMessage(), e);
            throw new BusinessException("Unable to save Trigger: " + e.getMessage());
        }
        return response;
    }

    /**
     * Updates an existing trigger with the provided request data.
     *
     * @param dto The TriggerRequestDto containing the updated trigger details
     * @return Map containing the status of the operation
     * @throws BusinessException if the trigger is not found or an error occurs during update
     */
    @Override
    public Map<String, String> updateTrigger(TriggerRequestDto dto) {
        log.debug("Inside updateTrigger method with ID: {}", dto.getId());
        Map<String, String> response = new HashMap<>();
        try {
            Trigger existingTrigger = triggerDao.findById(dto.getId())
                    .orElseThrow(() -> {
                 log.warn("Trigger with ID {} not found", dto.getId());
                        return new IllegalArgumentException("Trigger Id :" + dto.getId() + " not found");
                    });

            existingTrigger = mapToEntity(existingTrigger, dto);
            existingTrigger.setModifiedTime(new Date());
            triggerDao.save(existingTrigger);
            log.info("Trigger with ID {} updated successfully", dto.getId());
            response.put("status", "success");
        } catch (Exception e) {
            log.error("Error while updating Trigger: {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
        return response;
    }

    @Override
    public TriggerResponseDto getTriggerByName(String name) {
        log.debug("Inside getTriggerByName method with name: {}", name);
        Trigger trigger = Optional.ofNullable(triggerDao.findByName(name))
                .orElseThrow(() -> {
                    log.warn("Trigger with name {} not found", name);
                    return new BusinessException("Trigger with name " + name + " not found");
                });

        return TriggerConverter.convertToTriggerDto(trigger);
    }

    /**
     * Maps the data from TriggerRequestDto to Trigger entity.
     *
     * @param trigger The Trigger entity to update
     * @param dto The TriggerRequestDto containing the source data
     * @return The updated Trigger entity
     */
    private Trigger mapToEntity(Trigger trigger, TriggerRequestDto dto) {
        trigger.setName(dto.getName());
        trigger.setDescription(dto.getDescription());
        trigger.setDisplayName(dto.getDisplayName());
        trigger.setMetadata(dto.getMetadata());
        log.debug("Mapped Trigger entity: {}", trigger);
        return trigger;
    }

}