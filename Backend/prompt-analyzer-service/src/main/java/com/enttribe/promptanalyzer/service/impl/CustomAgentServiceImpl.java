package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.audit.AuditAdvisor;
import com.enttribe.promptanalyzer.dao.AgentHistoryDao;
import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.manager.InferenceManager;
import com.enttribe.promptanalyzer.model.AgentHistory;
import com.enttribe.promptanalyzer.model.AgentMessage;
import com.enttribe.promptanalyzer.service.CustomAgentService;
import com.enttribe.promptanalyzer.service.LlmApiService;
import com.enttribe.promptanalyzer.service.NifiFlowService;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.util.MessageUtils;
import com.enttribe.promptanalyzer.util.TemplateUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.document.Document;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.ResponseFormat;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Implementation of the {@link CustomAgentService} interface.
 * This class provides functionality for managing custom agents in the system, including
 * creating, updating, and retrieving agent configurations. It handles the business logic
 * for agent operations and manages the interaction between custom agents and their
 * associated configurations through the DAO layer.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomAgentServiceImpl implements CustomAgentService {

    private static final String SUCCESS = "success";
    private static final String STATUS = "status";
    private static final String CONDITION = "condition";
    private static final String MESSAGE = "message";
    private static final String FORMAT = "format";
    private static final String CONVERSATION_ID = "conversationId";
    private static final String CUSTOM_AGENT_FILTER = "f0b90d7d-c386-4b18-a1c5-9cf99b497324-abc";
    private static final String ERROR_CREATING_PLAN = "Error while creating plan ";
    private static final double TEMPERATURE = 0.2;
    private static final double SIMILARITY_THRESHOLD = 0.4;
    private static final int TOP_K = 3;

    private final AgentHistoryDao agentHistoryDao;
    private final NifiFlowService nifiFlowService;
    private final VectorStore vectorStore;
    private final InferenceManager inferenceManager;
    private final AuditAdvisor auditAdvisor;

    private final ObjectMapper objectMapper = JsonUtils.getObjectMapper();

    @Value("classpath:template/agent/orchestrator.st")
    private Resource orchestratorPrompt;

    @Value("classpath:template/agent/populate_processor.st")
    private Resource populateProcessorsPrompt;

    @Value("classpath:template/agent/identify_processors.st")
    private Resource identifyProcessorsPrompt;

    @Value("classpath:template/agent/improve_query_prompt.st")
    private Resource improveQueryPrompt;

    private static final Map<String, String> processorMap;

    static {
        processorMap = loadJsonToMap("processors.json");
        log.info("processorMap has been loaded with {} entries", processorMap.size());
    }

    private static Map<String, String> loadJsonToMap(String resourcePath) {
        ObjectMapper mapper = new ObjectMapper();
        try (InputStream is = new ClassPathResource(resourcePath).getInputStream()) {
            return mapper.readValue(is, new TypeReference<Map<String, String>>() {});
        } catch (IOException e) {
            log.error("Error loading JSON resource: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * Processes a user query and generates an execution plan.
     * The plan includes appropriate triggers, conditions, and actions based on the query analysis.
     *
     * @param agent DTO containing the user query and conversation context
     * @return Map containing the execution status, message, conversation ID, and generated plan
     */
    @Override
    public Map<String, String> getPlanforUserQuery(CustomAgentDto agent) {
        boolean isFirstMessage = agent.getConversationId() == null;
        String conversationId = getConversationId(agent);
        agent.setConversationId(conversationId);
        try {
            log.info("Inside getPlanForUserQuery with conversationId {}", agent.getConversationId());
            String userQuery = agent.getUserQuery();
            String userQuery1 = userQuery;
            log.info("userQuery is {}", userQuery);
            ChatClient chatClient = createChatClient();
            AgentHistory agentHistory = agentHistoryDao.getAgentHistory(agent.getProcessGroupId()).orElse(new AgentHistory());
            String contextForOrchestrator = getConversationContextForOrchestrator(agentHistory);

            boolean isValidating = false;
            if (agent.getUserQuery().contains("I accept the flow object")) {
                List<String> validationErrors = nifiFlowService.getValidationErrorsProcessGroup(agent.getProcessGroupId());
                userQuery = userQuery + "\n\n Validation message (ask user to rectify this if any) :\n" + validationErrors;
                log.info("userQuery after adding validation message is {}", userQuery);
                isValidating = true;
                userQuery1 = "Accept all steps";
            }
            String identifiedProcessorsJson;
            boolean askingForAllProcessors = false;
            OrchestratorResponse orchestratorResponse = getOrchestratorResponse(userQuery, chatClient, contextForOrchestrator);
            if (isValidating || (orchestratorResponse.fillProperties() && !orchestratorResponse.changeProcessor())) {
                identifiedProcessorsJson = agentHistory.getCurrentPlan();
                identifiedProcessorsJson = mergePlans(getPlan(agentHistory.getAgentMessages()), identifiedProcessorsJson);
            } else {
                ProcessorMatchResponse processorMatchResponse = identifyProcessors(userQuery, chatClient, conversationId, contextForOrchestrator);
                askingForAllProcessors = processorMatchResponse.isAskingForProcessorsList();
                List<String> identifiedProcessors = processorMatchResponse.matchedProcessors().stream().map(p -> processorMap.get(p.id())).filter(Objects::nonNull).toList();
                identifiedProcessorsJson = JsonUtils.convertToJSON(identifiedProcessors);
            }
            PopulatedProcessors populatedProcessors;
            if (askingForAllProcessors) {
                populatedProcessors = new PopulatedProcessors(List.of(), "Sorry, I can't provide processors list. Please specify a specific processor.");
            } else {
                populatedProcessors = populateProcessors(userQuery, chatClient, identifiedProcessorsJson, agentHistory.getLastFlow());
            }
            String message = populatedProcessors.message();
            String plan = JsonUtils.convertToJSON(populatedProcessors.processors());
            //save the current plan when user accepts the object
            if (isValidating) {
                //take plan of the last message, which is getting accepted now
                plan = mergePlans(plan, getPlan(agentHistory.getAgentMessages()));
                agentHistory.setCurrentPlan(plan);
            }

            log.debug("Going to save agent history");
                agentHistory.setAgentName(agent.getProcessGroupId());
                AgentMessage agentMessage = new AgentMessage();
                agentMessage.setTimeStamp(agent.getTimeStamp());
                agentMessage.setUserMessage(userQuery1);

                JSONObject assistantMessageJson = new JSONObject();
                assistantMessageJson.put(MESSAGE, message);
                assistantMessageJson.put("plan", plan);
                assistantMessageJson.put(CONVERSATION_ID, conversationId);
                assistantMessageJson.put(STATUS, SUCCESS);
                String assistantMessage = assistantMessageJson.toString();
                agentMessage.setAssistantMessage(assistantMessage);

                agentMessage.setAgentHistory(agentHistory);
                agentHistory.getAgentMessages().add(agentMessage);
                agentHistory.setLastFlow(JsonUtils.convertToJSON(populatedProcessors.processors()));
                agentHistoryDao.save(agentHistory);

                if (isValidating && !isFirstMessage) {
                return Map.of(STATUS, SUCCESS, MESSAGE, message, CONVERSATION_ID, conversationId, "plan", "");
            }
            if (plan.equals("[]")) plan = "";
            return Map.of(STATUS, SUCCESS, MESSAGE, message, CONVERSATION_ID, conversationId, "plan", plan);
        } catch (Exception e) {
            log.error("Error while generating plan ", e);
            return Map.of(STATUS, SUCCESS, MESSAGE, MessageUtils.getErrorMessage(), CONVERSATION_ID, conversationId);
        }
    }

    private String mergePlans(String currentPlan, String previousPlan) {
       log.info("Inside mergePlans");
        if (previousPlan == null || previousPlan.isEmpty()) {
            return currentPlan;
        }
        try {
            List<Map<String, Object>> selectedList = objectMapper.readValue(currentPlan, new TypeReference<>() {});
            List<Map<String, Object>> latestList = objectMapper.readValue(previousPlan.replace("\\\"", "\""), new TypeReference<>() {});

            Map<String, Map<String, Object>> latestMap = latestList.stream()
                    .collect(Collectors.toMap(p -> p.get("name").toString(), p -> p));

            return margePlansExtract(selectedList, latestMap);
        } catch (Exception e) {
            log.error("error while merging plans : {}", e.getMessage(), e);
            return currentPlan;
        }
    }

    private String margePlansExtract(List<Map<String, Object>> selectedList, Map<String, Map<String, Object>> latestMap) throws JsonProcessingException {
        try {
            log.info("Inside margePlansExtract");
            for (Map<String, Object> processor : selectedList) {
                String name = processor.get("name").toString();
                if (latestMap.containsKey(name)) {
                    mergeConditions(processor, latestMap.get(name));
                }
            }
            log.info("Successfully merged plans {} and {}", selectedList, latestMap);
            return objectMapper.writeValueAsString(selectedList);
        } catch (Exception e) {
            throw new BusinessException("error while merging plans extracting: " + e.getMessage());
        }
    }

    private void mergeConditions(Map<String, Object> processor, Map<String, Object> latestProcessor) {
        if (latestProcessor.containsKey(CONDITION)) {
            Map<String, Object> latestCondition = (Map<String, Object>) latestProcessor.get(CONDITION);
            Map<String, Object> currentCondition = (Map<String, Object>) processor.get(CONDITION);
            for (Map.Entry<String, Object> entry : latestCondition.entrySet()) {
                if (entry.getValue() != null && !entry.getValue().toString().isEmpty()) {
                    currentCondition.put(entry.getKey(), entry.getValue());
                }
            }
        }
    }

    private String getConversationContextForOrchestrator(AgentHistory agentHistory) {
        List<AgentMessage> agentMessages = agentHistory.getAgentMessages();
        StringBuilder conversation = new StringBuilder();
        for (AgentMessage agentMessage : agentMessages) {
            conversation.append("user : ").append(agentMessage.getUserMessage()).append("\n");
            String assistantMessage = agentMessage.getAssistantMessage();
            JSONObject jsonObject = new JSONObject(assistantMessage);
            if (jsonObject.has(MESSAGE)) {
                assistantMessage = jsonObject.getString(MESSAGE);
            } else {
                assistantMessage = "";
            }
            conversation.append("LLM : ").append(assistantMessage).append("\n");
        }
        String plan = agentHistory.getCurrentPlan();
        conversation.append("\nlatest plan is : ").append(plan);
        return conversation.toString();
    }

    private String getPlan(List<AgentMessage> agentMessages) {
        String plan = "";
        if (!agentMessages.isEmpty()) {
            AgentMessage lastMessage = agentMessages.getLast();
            String assistantMessage = lastMessage.getAssistantMessage();
            JSONObject jsonObject = new JSONObject(assistantMessage);
            if (jsonObject.has("plan")) {
                plan = jsonObject.getString("plan");
            }
        }
        return plan;
    }

    private String getConversationId(CustomAgentDto agent) {
        return agent.getConversationId() == null ? UUID.randomUUID().toString() : agent.getConversationId();
    }

    /**
     * Creates and configures a ChatClient instance with specific LLM model settings.
     *
     * @return Configured ChatClient instance
     */
    public ChatClient createChatClient() {
        OpenAiChatOptions options = new OpenAiChatOptions();
        options.setModel("llama-3.3-70b-versatile");
        options.setTemperature(TEMPERATURE);
        options.setResponseFormat(ResponseFormat.builder()
                .type(ResponseFormat.Type.JSON_OBJECT).build());

        ChatModel model = inferenceManager.getChatModelByProvider("groq");

        return ChatClient.builder(model)
                .defaultAdvisors(auditAdvisor)
                .defaultToolContext(Map.of("auditId", UUID.randomUUID().toString()))
                .defaultOptions(options)
                .build();
    }


    public OrchestratorResponse getOrchestratorResponse(String userQuery, ChatClient chatClient, String conversationHistory) {
        try {
            log.info("Going to get getOrchestratorResponse for user query {} for conversationHistory {}", userQuery, conversationHistory);
            BeanOutputConverter<OrchestratorResponse> converter = new BeanOutputConverter<>(OrchestratorResponse.class);
            String resolvedPrompt = TemplateUtils.getResolvedPrompt(orchestratorPrompt, Map.of("conversationHistory", conversationHistory, FORMAT, converter.getFormat()));

            String plan = chatClient.prompt()
                    .system(resolvedPrompt)
                    .user(userQuery)
                    .call().content();

            OrchestratorResponse orchestratorResponse = JsonUtils.convertJsonToObject(plan, OrchestratorResponse.class);
            log.info("orchestratorResponse for user query is {}", plan);
            return orchestratorResponse;
        } catch (Exception e) {
            log.error(ERROR_CREATING_PLAN, e);
            return null;
        }
    }

    public PopulatedProcessors populateProcessors(String userQuery, ChatClient chatClient, String identifiedProcessors, String currentPlan) {
        try {
            log.info("Going to populateProcessors for user query {}", userQuery);
            if (identifiedProcessors.isEmpty() || identifiedProcessors.equals("[]")) {
                log.info("No processors were identified");
                return new PopulatedProcessors(List.of(), "Unfortunately, we couldn't find a processor that matches your request.");
            }
            BeanOutputConverter<PopulatedProcessors> converter = new BeanOutputConverter<>(PopulatedProcessors.class);
            Map<String, Object> variableMap = Map.of("processors", identifiedProcessors, FORMAT, converter.getFormat(),
                    "lastFlow", currentPlan);
            String resolvedPrompt = TemplateUtils.getResolvedPrompt(populateProcessorsPrompt, variableMap);

            String plan = chatClient.prompt()
                    .system(resolvedPrompt)
                    .user(userQuery)
                    .call().content();

            PopulatedProcessors populatedProcessors = JsonUtils.convertJsonToObject(plan, PopulatedProcessors.class);
            log.info("populateProcessors for user query is {}", plan);
            return populatedProcessors;
        } catch (Exception e) {
            log.error(ERROR_CREATING_PLAN, e);
            return null;
        }
    }

    private List<String> getRelevantContext(String userQuery, ChatClient chatClient) {
        List<Document> documents = new ArrayList<>();
        List<String> queries = improveQuery(userQuery, chatClient);

        for (String query : queries) {
            SearchRequest searchRequest = SearchRequest.builder()
                    .similarityThreshold(SIMILARITY_THRESHOLD)
                    .filterExpression(String.format("'custom_agent_id' == '%s'", CUSTOM_AGENT_FILTER))
                    .topK(TOP_K)
                    .query(query)
                    .build();

            List<Document> documentList = vectorStore.similaritySearch(searchRequest);
            documents.addAll(documentList);
        }
        return documents.stream().map(Document::getText).toList();
    }

    public ProcessorMatchResponse identifyProcessors(String userQuery, ChatClient chatClient, String conversationId, String conversationHistory) {
        try {
            log.info("Going to identifyProcessors for user query {} for conversationId {}", userQuery, conversationId);
            BeanOutputConverter<ProcessorMatchResponse> converter = new BeanOutputConverter<>(ProcessorMatchResponse.class);

            List<String> documents = getRelevantContext(userQuery, chatClient);

            Map<String, Object> variableMap = Map.of("context", documents, FORMAT, converter.getFormat(),
                    "conversationHistory", conversationHistory, "currentMessage", userQuery);

            String resolvedPrompt = TemplateUtils.getResolvedPrompt(identifyProcessorsPrompt, variableMap);

            String plan = chatClient.prompt()
                    .system(resolvedPrompt)
                    .user(userQuery)
                    .call().content();

            ProcessorMatchResponse orchestratorResponse = JsonUtils.convertJsonToObject(plan, ProcessorMatchResponse.class);
            log.info("identifyProcessors for user query is {}", plan);
            return orchestratorResponse;
        } catch (Exception e) {
            log.error(ERROR_CREATING_PLAN, e);
            return null;
        }
    }

    private List<String> improveQuery(String userQuery, ChatClient chatClient) {
        try {
            String systemPrompt = TemplateUtils.getResolvedPrompt(improveQueryPrompt, Map.of("query", userQuery));

            OpenAiChatOptions options = new OpenAiChatOptions();
            options.setModel("llama-3.3-70b-versatile");
            options.setTemperature(TEMPERATURE);

            List<String> queries = chatClient.prompt()
                    .system(systemPrompt)
                    .options(options)
                    .user(userQuery)
                    .call()
                    .entity(new ParameterizedTypeReference<List<String>>() {
                    });

            log.info("improvedQuery is {}", queries);
            return queries;
        } catch (Exception e) {
            log.error("Error while improving query : {}", e.getMessage());
            return List.of();
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record OrchestratorResponse(
            @JsonPropertyDescription("True if a processor is getting added or removed, else false") Boolean changeProcessor,
            @JsonPropertyDescription("True if properties of processor is provided, else false") Boolean fillProperties,
            @JsonPropertyDescription("reasoning for this decision") String reasoning
    ) {}

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record ProcessorMatchResponse(
            List<MatchedProcessor> matchedProcessors,
            @JsonPropertyDescription("True if user is asking for the list of processors, else false") Boolean isAskingForProcessorsList,
            @JsonPropertyDescription("reasoning for this decision") String reasoning
    ) {}

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record MatchedProcessor(
            @JsonPropertyDescription("name of the processor") String name,
            @JsonPropertyDescription("Id of the processor") String id
    ) {}

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record PopulatedProcessors(
            @JsonPropertyDescription("list of all processors") List<Processor> processors,
            @JsonPropertyDescription("message to be shown to the user") String message
    ) {}

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record Processor(
            String name,
            @JsonProperty("package") String _package,
            String description,
            @JsonPropertyDescription("type can either be TRIGGER or ACTION") String type,
            @JsonPropertyDescription("fields from parameters of the processor") Map<String, String> condition
    ) {}


    /**
     * Creates a business-friendly name and description for NiFi flows using AI.
     * Takes a user's description and generates a PascalCase name with a clear description,
     * suitable for NiFi data flow identification.
     *
     * @param userQuery User's description of the flow
     * @return Map with status and generated name/description in JSON format
     */
    @Override
    public Map<String, String> createTriggerNameDescription(String userQuery) {
        try {
            log.info("userQuery to generate flow name is {}", userQuery);
            ChatClient chatClient = createChatClient();
            ChatClient.ChatClientRequestSpec triggerNameRequest = chatClient.prompt().system("""
                              You are a naming assistant responsible for creating meaningful and logical business names and descriptions for NiFi data flows. Your task is to take the user's input query, analyze the business context, and generate a clear, concise, and descriptive name and description that reflects the purpose and functionality of the NiFi flow.
                            
                            Guidelines for Output:
                            
                            Provide the result as a JSON object with the following structure:
                            
                            {
                              "name": "Business Name in User friendly title case",
                              "description": "Detailed description of the flow's functionality and purpose."
                            }
                            
                            Guidelines for Naming:
                            
                            - Use space-separated Title Case (e.g., "Customer Payment Kafka to Postgres").
                            - Include essential business functions (e.g., Data Ingestion, Data Transformation, or Data Enrichment).
                            - Indicate key data sources, destinations, or domains (e.g., Customer Data, Payment Processing).
                            - Maintain clarity and brevity (avoid overly long names).
                             Guidelines for Description:
                            
                            Provide a brief yet comprehensive explanation of the flow's purpose.
                            Include the key operations performed and data sources or destinations involved.
                            Examples:
                            
                            Query: "Process customer payment data from Kafka and send to PostgreSQL"
                            Output:
                            
                            {
                              "name": "Customer Payment Kafka to Postgres",
                              "description": "This flow processes customer payment data received from Kafka and stores it in a PostgreSQL database."
                            }
                            
                            
                            The output should be a parsable json with no additional information
                            """)
                    .user(userQuery);

            String triggerName = triggerNameRequest.call().chatResponse().getResult().getOutput().getText();
            log.debug("successfully created trigger name and description");
            return Map.of(STATUS, SUCCESS, "data", triggerName);
        } catch (Exception e) {
            log.error("Error while creating name for trigger");
            return Map.of(STATUS, "failed");
        }
    }

}
