package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.McpServerDao;
import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.McpServer;
import com.enttribe.promptanalyzer.service.McpServerService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.SdkUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Implementation of the {@link McpServerService} interface.
 * This class provides the business logic for managing MCP servers.
 * It handles server creation, updating, searching, and data mapping operations while
 * interacting with the data access layer.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class McpServerServiceImpl implements McpServerService {

    private static final String RESULT = "result";
    private static final String SUCCESS = "success";
    private static final String FAILED = "failed";

    private final McpServerDao mcpServerDao;
    private final CustomFilter customFilter;

    /**
     * Creates a new MCP server.
     * This method handles the creation of a new server based on the provided McpServerDto.
     *
     * @param serverDto The McpServerDto containing the server details to be created
     * @return Map containing the operation result with keys:
     *         - "result": "success" or "failed"
     * @throws BusinessException if server creation fails or validation errors occur
     */
    @Override
    public Map<String, String> create(McpServerDto serverDto) {
        log.debug("@class McpServerServiceImpl @method create going to create MCP Server");
        Map<String, String> result = new HashMap<>();
        try {
            McpServer mcpServer = new McpServer();
            mapDtoToEntity(serverDto, mcpServer);
            mcpServer.setId(null);
            // Set creation and modification times
            Date now = new Date();
            mcpServer.setCreatedTime(now);
            mcpServer.setModifiedTime(now);
            mcpServerDao.save(mcpServer);
            log.debug("Successfully saved MCP Server");
            result.put(RESULT, SUCCESS);
            return result;
        } catch (Exception e) {
            log.error("Exception occurred while saving MCP Server: {}", e.getMessage(), e);
            throw new BusinessException("Unable to save MCP Server: " + e.getMessage());
        }
    }

    /**
     * Updates an existing MCP server.
     * This method updates the details of an existing server based on the provided McpServerDto.
     *
     * @param serverDto The McpServerDto containing the updated server details
     * @return Map containing the operation result with keys:
     *         - "result": "success" or "failed"
     * @throws BusinessException if server update fails or validation errors occur
     */
    @Override
    public Map<String, String> update(McpServerDto serverDto) {
        log.debug("@class McpServerServiceImpl @method update going to update MCP Server with ID: {}", serverDto.getId());
        Map<String, String> result = new HashMap<>();
        try {
            if (serverDto.getId() == null) {
                throw new BusinessException("Server ID cannot be null for update operation");
            }

            McpServer existingServer = mcpServerDao.findById(serverDto.getId())
                    .orElseThrow(() -> new BusinessException("MCP Server not found with ID: " + serverDto.getId()));

            mapDtoToEntity(serverDto, existingServer);
            // Update modification time
            existingServer.setModifiedTime(new Date());
            mcpServerDao.save(existingServer);

            log.debug("Successfully updated MCP Server with ID: {}", serverDto.getId());
            result.put(RESULT, SUCCESS);
            return result;
        } catch (Exception e) {
            log.error("Exception occurred while updating MCP Server: {}", e.getMessage(), e);
            throw new BusinessException("Unable to update MCP Server: " + e.getMessage());
        }
    }


    /**
     * Searches for MCP servers based on specified criteria and pagination parameters.
     *
     * @param filter The search filter criteria to apply
     * @param offset The starting position in the result set
     * @param size The maximum number of results to return
     * @param orderBy The field to sort results by
     * @param orderType The sort direction (ascending/descending)
     * @return List of McpServer objects matching the search criteria
     * @throws BusinessException if the search operation fails
     */
    @Override
    public List<McpServer> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        try {
            log.debug("Searching for MCP Servers with filter: {}", filter);
            return customFilter.searchByFilter(McpServer.class, filter, orderBy, orderType, offset, size);
        } catch (Exception e) {
            log.error("Error while searching for MCP Servers: {}", e.getMessage(), e);
            throw new BusinessException("Failed to search MCP Servers: " + e.getMessage());
        }
    }

    /**
     * Counts MCP servers based on the provided filter.
     *
     * @param filter The filter criteria to count matching servers
     * @return Count of matching servers
     * @throws BusinessException if the count operation fails
     */
    @Override
    public Long count(String filter) {
        try {
            log.debug("Counting MCP Servers with filter: {}", filter);
            return customFilter.countByFilter(McpServer.class, filter);
        } catch (Exception e) {
            log.error("Error while counting MCP Servers: {}", e.getMessage(), e);
            throw new BusinessException("Failed to count MCP Servers: " + e.getMessage());
        }
    }

    /**
     * Soft deletes an MCP server by ID.
     * The server will not be permanently removed from the database.
     *
     * @param id The ID of the server to delete
     * @return Map containing the operation result with keys:
     *         - "result": "success" or "failed"
     * @throws BusinessException if the delete operation fails
     */
    @Override
    public Map<String, String> softDelete(Integer id) {
        log.debug("@class McpServerServiceImpl @method delete going to soft delete MCP Server with ID: {}", id);
        Map<String, String> result = new HashMap<>();
        try {
            Optional<McpServer> serverOptional = mcpServerDao.findById(id);
            if (serverOptional.isEmpty()) {
                result.put(RESULT, FAILED);
                result.put("message", "MCP Server not found with ID: " + id);
                return result;
            }

            McpServer server = serverOptional.get();
            server.setDeleted(true);
            mcpServerDao.save(server);
            log.debug("Successfully soft deleted MCP Server with ID: {}", id);
            result.put(RESULT, SUCCESS);
            return result;
        } catch (Exception e) {
            log.error("Exception occurred while soft deleting MCP Server: {}", e.getMessage(), e);
            throw new BusinessException("Unable to soft delete MCP Server: " + e.getMessage());
        }
    }

    @Override
    public List<McpServerDto> getMcpServerByIds(List<Integer> ids) {
        List<McpServer> mcpServers = mcpServerDao.findAllById(ids);
        return SdkUtils.getMcpServersDtoListSdk(mcpServers);
    }

    /**
     * Maps an McpServerDto to an McpServer entity.
     * This method creates or updates an McpServer entity with data from the provided DTO.
     *
     * @param dto The McpServerDto containing the source data
     * @return An McpServer entity populated with the DTO data
     */
    private McpServer mapDtoToEntity(McpServerDto dto, McpServer server) {
        if (dto.getName() != null) {
            server.setName(dto.getName());
        }
        if (dto.getDescription() != null) {
            server.setDescription(dto.getDescription());
        }
        if (dto.getType() != null) {
            server.setType(dto.getType());
        }
        if (dto.getIcon() != null) {
            server.setIcon(dto.getIcon());
        }
        if (dto.getUrl() != null) {
            server.setUrl(dto.getUrl());
        }
        if (dto.getIsCustomServer() != null) {
            server.setIsCustomServer(dto.getIsCustomServer());
        }
        if (dto.getCommandToRun() != null) {
            server.setCommandToRun(dto.getCommandToRun());
        }

        server.setModifiedTime(new Date());
        
        if (server.getCreatedTime() == null) {
            server.setCreatedTime(new Date());
        }

        log.debug("Updated McpServer entity with new values: {}", dto.getName());
        return server;
    }


}