package com.enttribe.promptanalyzer.service.impl;

import com.opencsv.CSVReader;
import com.opencsv.CSVWriter;
import java.io.*;
import java.util.*;

public class TimeParserTestRunner {

    private final TimeParserServiceImpl timeParser;

    public TimeParserTestRunner(TimeParserServiceImpl timeParser) {
        this.timeParser = timeParser;
    }

    public void runTests(String inputCsv, String outputCsv) {
        try (CSVReader reader = new CSVReader(new FileReader(inputCsv));
             CSVWriter writer = new CSVWriter(new FileWriter(outputCsv))) {

            String[] header = reader.readNext();
            writer.writeNext(new String[]{"Input Query", "Expected From", "Expected To", "Actual From", "Actual To", "Result"});

            String[] row;
            while ((row = reader.readNext()) != null) {
                String query = row[0];
                String expectedFrom = row.length > 1 ? row[1] : "";
                String expectedTo = row.length > 2 ? row[2] : "";

                Map<String, String> req = new HashMap<>();
                req.put("userQuery", query);
                req.put("timeZone", "Asia/Riyadh");
                req.put("locale", "en_US");

                String result = timeParser.parseTimeFromQuery(req);
                String actualFrom = extractDate(result, "from:");
                String actualTo = extractDate(result, "to:");

                boolean passed = expectedFrom.equals(actualFrom) &&
                        (expectedTo.isEmpty() || expectedTo.equals(actualTo));

                writer.writeNext(new String[]{
                        query, expectedFrom, expectedTo, actualFrom, actualTo, passed ? "PASS" : "FAIL"
                });
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String extractDate(String result, String label) {
        if (result == null || !result.contains(label)) return "";

        int idx = result.indexOf(label) + label.length();
        int end;

        if ("from:".equals(label)) {
            end = result.indexOf("to:", idx);
        } else {
            end = result.length();
        }

        if (end == -1 || idx >= end) return "";

        return result.substring(idx, end).trim();
    }


    public static void main(String[] args) {
        TimeParserServiceImpl timeParser = new TimeParserServiceImpl(); // Inject dependencies as needed
        TimeParserTestRunner runner = new TimeParserTestRunner(timeParser);

        runner.runTests("/Users/<USER>/Documents/Work/prompt-analyzer/Backend/test_temporal/test_data.csv",
                "/Users/<USER>/Documents/Work/prompt-analyzer/Backend/test_temporal/test_result.csv");
        System.out.println("Test results written to test_results.csv");
    }
}