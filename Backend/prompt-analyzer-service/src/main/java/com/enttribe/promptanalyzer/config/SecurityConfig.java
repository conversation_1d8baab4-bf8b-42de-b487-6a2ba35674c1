package com.enttribe.promptanalyzer.config;

import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;

import static org.springframework.security.config.http.SessionCreationPolicy.STATELESS;

/**
 * This class configures the security settings for the application, 
 * defining how HTTP requests are secured and managed.
 * Author: VisionWaves
 * Version: 1.0
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    /**
     * Configures the security filter chain for HTTP requests.
     * This method sets up the security configurations for the application, 
     * including CSRF protection, request authorization, session management, 
     * and authentication methods. It allows all requests and disables 
     * form-based and basic authentication.
     *
     * @param http the HttpSecurity object used to configure security settings
     * @return a SecurityFilterChain object that contains the configured security settings
     * @throws Exception if an error occurs while configuring security
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(req ->
                        req.requestMatchers("/**").permitAll())// Disable CSRF if not needed
                .authorizeHttpRequests(auth -> auth
                        .anyRequest().permitAll() // Allow all requests
                )
                .sessionManagement(session -> session.sessionCreationPolicy(STATELESS))
                .formLogin(AbstractHttpConfigurer::disable) // Disable login form
                .httpBasic(AbstractHttpConfigurer::disable); // Disable basic auth

        return http.build();
    }

    @Bean
    public ToolCallbackProvider weatherTools(TestService testService) {
        return MethodToolCallbackProvider.builder().toolObjects(testService).build();
    }

}