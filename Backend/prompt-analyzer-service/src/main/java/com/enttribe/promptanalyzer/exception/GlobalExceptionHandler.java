package com.enttribe.promptanalyzer.exception;

import jakarta.persistence.PersistenceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Global exception handler for handling exceptions across the application.
 * Provides specific handlers for different exception types to return appropriate HTTP responses.
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * Handles ResourceNotFoundException and returns a NOT_FOUND response.
     *
     * @param ex the ResourceNotFoundException
     * @param request the web request
     * @return a ResponseEntity with error details and NOT_FOUND status
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ErrorDetails> handlerResourceNotFoundException(ResourceNotFoundException ex, WebRequest request) {
        ErrorDetails errorDetails = new ErrorDetails(new Date(), ex.getMessage(), request.getDescription(false));
        return new ResponseEntity<>(errorDetails, HttpStatus.NOT_FOUND);
    }

    /**
     * Handles BusinessException and returns a BAD_REQUEST response.
     *
     * @param ex the BusinessException
     * @param request the web request
     * @return a ResponseEntity with error details and BAD_REQUEST status
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorDetails> handlerBusinessException(BusinessException ex, WebRequest request) {
        Throwable cause = ex.getCause();
        ErrorDetails errorDetails;
        if (cause != null) {
            errorDetails = new ErrorDetails(new Date(), ex.getMessage(), request.getDescription(false), cause.getMessage());
        } else {
            errorDetails = new ErrorDetails(new Date(), ex.getMessage(), request.getDescription(false));
        }
        return new ResponseEntity<>(errorDetails, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles generic Exception and returns an INTERNAL_SERVER_ERROR response.
     *
     * @param ex the Exception
     * @param request the web request
     * @return a ResponseEntity with error details and INTERNAL_SERVER_ERROR status
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorDetails> handlerException(Exception ex, WebRequest request) {
        boolean causeExist = ex.getCause() != null;
        if (ex instanceof PersistenceException || (causeExist && ex.getCause() instanceof PersistenceException)) {
            String userMessage = "A database error occurred. Please contact administrator.";
            log.error("Database error occurred: {}", ex.getMessage(), ex);
            ErrorDetails errorDetails = new ErrorDetails(new Date(), userMessage, request.getDescription(false), ex.getMessage());
            return new ResponseEntity<>(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        ErrorDetails errorDetails = new ErrorDetails(new Date(), ex.getMessage(), request.getDescription(false));

        if (ex.getMessage().contains("No static resource")) return new ResponseEntity<>(errorDetails, HttpStatus.NOT_FOUND);

        return new ResponseEntity<>(errorDetails, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * Handles MethodArgumentNotValidException and returns a BAD_REQUEST response with validation errors.
     *
     * @param ex the MethodArgumentNotValidException
     * @return a ResponseEntity with validation errors and BAD_REQUEST status
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Map<String, String>> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getFieldErrors().forEach(error -> errors.put(error.getField(), error.getDefaultMessage()));
        return new ResponseEntity<>(errors, HttpStatus.BAD_REQUEST);
    }

}