package com.enttribe.promptanalyzer.audit;

import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.AuditService;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.metadata.Usage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class AuditAdvisor implements CallAdvisor {

    private static final Logger log = LoggerFactory.getLogger(AuditAdvisor.class);
    public static final int ORDER = 999;

    private String applicationName = "PROMPT_ANALYZER_APP_NAME";

    @Value("${prompt.audit.enable:false}")
    private boolean promptAuditEnabled;

    private final AuditService auditService;

    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_FAILED = "failed";

    public AuditAdvisor(AuditService auditService) {
        this.auditService = auditService;
    }

    @Override
    public ChatClientResponse adviseCall(ChatClientRequest chatClientRequest, CallAdvisorChain callAdvisorChain) {
        List<Message> messages = new ArrayList<>(chatClientRequest.prompt().getInstructions());
        if (messages.isEmpty()) {
            log.info("adding system message for audit");
            String systemText = chatClientRequest.prompt().getSystemMessage().getText();
            if (!systemText.isEmpty()) {
                messages.add(new SystemMessage(systemText));
            }
        }
        if (!chatClientRequest.prompt().getUserMessage().getText().isEmpty()) {
            UserMessage userMessage = new UserMessage(chatClientRequest.prompt().getUserMessage().getText());
            messages.add(userMessage);
        }

        String auditId = (String) chatClientRequest.context().get("auditId");
        ChatClientResponse advisedResponse = null;
        try {
            Instant startTime = Instant.now();
            advisedResponse = callAdvisorChain.nextCall(chatClientRequest);
            Instant endTime = Instant.now();
            Long timeTakenMillis = Duration.between(startTime, endTime).toMillis();
            ChatResponse chatResponse = advisedResponse.chatResponse();
            doSuccessPromptAudit(chatClientRequest, timeTakenMillis, chatResponse, messages, auditId);
            return advisedResponse;
        } catch (Exception e) {
            try {
                log.info("error while executing prompt : {}", e.getMessage(), e);
                doFailedPromptAudit(chatClientRequest, messages, e.getMessage(), auditId);
            }
            catch (JsonProcessingException ex) {
                log.info("error while saving prompt audit : {}", ex.getMessage(), ex);
            }
            throw new BusinessException("error while executing prompt : " + chatClientRequest + e.getMessage());
        }
    }

    @Override
    public String getName() {
        return "AuditAdvisor";
    }

    @Override
    public int getOrder() {
        return ORDER;
    }

    private void doSuccessPromptAudit(ChatClientRequest advisedRequest, Long timeTakenMillis,
                                      ChatResponse chatResponse, List<Message> messages, String auditId) throws JsonProcessingException {
        if (promptAuditEnabled) {
            //Audit related code
            Usage usage = chatResponse.getMetadata().getUsage();


            PromptAuditDto promptAudit = PromptAuditDto.builder()
                    .applicationName(applicationName)
                    .chatOptions(advisedRequest.prompt().getOptions() != null ? Map.of("chatOptions", advisedRequest.prompt().getOptions()) : Map.of())
                    .auditId(AuditUtils.generateAuditId(auditId))
                    .model("llama-3.3-70b-versatile")
                    .responseText(chatResponse.getResult().getOutput().getText())
                    .totalToken(usage.getTotalTokens().longValue())
                    .promptToken(usage.getPromptTokens().longValue())
                    .generationTokens(usage.getCompletionTokens().longValue())
                    .responseTime(timeTakenMillis.doubleValue())
                    .creationTime(new Date())
                    .requestText(JsonUtils.convertToJSON(messages))
                    .status(STATUS_SUCCESS)
                    .build();

            auditService.savePromptAudit(promptAudit);
        }
    }

    private void doFailedPromptAudit(ChatClientRequest advisedRequest,
                                     List<Message> messages, String errorMessage, String auditId) throws JsonProcessingException {
        if (promptAuditEnabled) {
            //Audit related code

            ChatOptions chatOptions = advisedRequest.prompt().getOptions();

            PromptAuditDto promptAudit = PromptAuditDto.builder()
                    .applicationName(applicationName)
                    .chatOptions(chatOptions != null ? Map.of("chatOptions", chatOptions) : Collections.emptyMap())
                    .auditId(AuditUtils.generateAuditId(auditId))
//                    .promptId(promptModel.getPromptId())
                    .model(chatOptions != null ? chatOptions.getModel() : null)
//                    .promptName(promptModel.getPromptName())
                    .creationTime(new Date())
//                    .provider(promptModel.getProvider())
                    .requestText(JsonUtils.convertToJSON(messages))
                    .httpStatus(extractStatusCode(errorMessage))
                    .status(STATUS_FAILED)
                    .errorMessage(errorMessage)
                    .build();

            auditService.savePromptAudit(promptAudit);
        }
    }

    private static Integer extractStatusCode(String errorMessage) {
        Pattern pattern = Pattern.compile("^(\\d{3})");
        Matcher matcher = pattern.matcher(errorMessage);

        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1)); // Extracts the first 3-digit number
        }

        return null; // Return null if no status code is found
    }

    public static void main(String[] args) {
        String error1 = "401 - {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}";
        String error2 = "40 - {\"error\":{\"message\":\"'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.\",\"type\":\"invalid_request_error\"}}";

       log.info("Extracted Status Code: {}" , extractStatusCode(error1)); // Output: 401
       log.info("Extracted Status Code: {}" , extractStatusCode(error2));
    }

}
