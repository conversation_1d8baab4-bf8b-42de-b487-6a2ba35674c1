package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.AgentHistoryDao;
import com.enttribe.promptanalyzer.model.AgentHistory;
import com.enttribe.promptanalyzer.service.AgentHistoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


@Service
@RequiredArgsConstructor
public class AgentHistoryServiceImpl implements AgentHistoryService {

    private final AgentHistoryDao agentHistoryDao;

    @Override
    public AgentHistory getAgentHistory(String agentName) {
        return agentHistoryDao.getAgentHistory(agentName).orElse(null);
    }

}
