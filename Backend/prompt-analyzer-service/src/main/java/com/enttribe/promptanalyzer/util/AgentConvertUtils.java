package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.agent.AgentConvertDto;
import com.enttribe.promptanalyzer.model.Agent;

import java.util.List;

/**
 * Utility class for converting Agent entities to AgentConvertDto objects.
 * Provides methods for converting lists of agents and individual agent entities.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class AgentConvertUtils {

    // Private constructor to prevent instantiation
    private AgentConvertUtils() {
    }

    /**
     * Converts a list of Agent entities to a list of AgentConvertDto objects.
     *
     * @param agents the list of Agent entities to convert
     * @return a list of AgentConvertDto objects
     */
    public static List<AgentConvertDto> getAgentDtoList(List<Agent> agents) {
        return agents.stream()
                .map(AgentConvertUtils::convertToAgentDto)
                .toList();
    }

    /**
     * Converts an individual Agent entity to an AgentConvertDto object.
     *
     * @param agent the Agent entity to convert
     * @return the converted AgentConvertDto object
     */
    private static AgentConvertDto convertToAgentDto(Agent agent) {
        return AgentConvertDto.builder().
                id(agent.getId())
                .name(agent.getName()).build();
    }

}


