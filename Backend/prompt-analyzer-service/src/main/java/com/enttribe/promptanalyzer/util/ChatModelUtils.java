package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.model.Prompt;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.ResponseFormat;

import java.util.List;
import java.util.Map;

/**
 * Utility class for preparing chat options and resolving messages for chat models.
 * Provides methods to configure chat options and transform prompt messages into chat messages.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public final class ChatModelUtils {


    /**
     * Private constructor to prevent instantiation of this utility class.
     */
    private ChatModelUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    /**
     * Prepares chat options based on the given prompt model.
     *
     * @param promptModel the prompt model containing configuration details
     * @return the configured ChatOptions
     */
    public static ChatOptions prepareChatOptions(Prompt promptModel) {
        ResponseFormat responseFormat = ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT).build();
        if (promptModel.getJsonMode() == null || !promptModel.getJsonMode()) {
            return OpenAiChatOptions.builder()
                    .model(promptModel.getLlmModel().getModel())
                    .temperature(promptModel.getTemperature())
                    .topP(promptModel.getTopP())
                    .maxTokens(promptModel.getMaxToken())
                    .build();
        } else {
            return OpenAiChatOptions.builder()
                    .model(promptModel.getLlmModel().getModel())
                    .temperature(promptModel.getTemperature())
                    .topP(promptModel.getTopP())
                    .responseFormat(responseFormat)
                    .maxTokens(promptModel.getMaxToken())
                    .build();
        }
    }

    /**
     * Resolves and returns a list of chat messages based on the given prompt model and variable map.
     *
     * @param promptModel the prompt model containing message templates
     * @param variableMap the map of variables to resolve in the message templates
     * @return a list of resolved chat messages
     */
    public static List<Message> getResolvedMessages(Prompt promptModel, Map<String, Object> variableMap) {
        List<com.enttribe.promptanalyzer.model.Message> messages = promptModel.getMessages();
        return messages.stream().map(message -> prepareMessage(message, variableMap)).toList();
    }

    /**
     * Prepares a chat message by resolving variables in the message content.
     *
     * @param message the message template
     * @param variableMap the map of variables to resolve in the message content
     * @return the resolved chat message
     */
    private static Message prepareMessage(com.enttribe.promptanalyzer.model.Message message, Map<String, Object> variableMap) {
        String resolvedPrompt = TemplateUtils.getResolvedPrompt(message.getContent(), variableMap);
        return getMessage(resolvedPrompt, message.getRole());
    }

    /**
     * Creates a chat message of the appropriate type based on the role.
     *
     * @param content the content of the message
     * @param role the role of the message (system, user, or assistant)
     * @return the created chat message
     * @throws IllegalStateException if the role is not recognized
     */
    private static Message getMessage(String content, String role) {
        MessageType messageType = getMessageType(role);
        return switch (messageType) {
            case MessageType.SYSTEM -> new SystemMessage(content);
            case MessageType.USER -> new UserMessage(content);
            case MessageType.ASSISTANT -> new AssistantMessage(content);

            default -> throw new IllegalStateException("role must be one of 'system', 'user' or 'assistant");
        };
    }

    /**
     * Determines the message type based on the role.
     *
     * @param role the role of the message
     * @return the MessageType corresponding to the role
     * @throws IllegalStateException if the role is not recognized
     */
    private static MessageType getMessageType(String role) {
        return switch (role.toLowerCase()) {
            case "system" -> MessageType.SYSTEM;
            case "user" -> MessageType.USER;
            case "assistant" -> MessageType.ASSISTANT;

            default ->
                    throw new IllegalStateException("role must be one of 'system', 'user' or 'assistant. provided role is : " + role);
        };
    }

}