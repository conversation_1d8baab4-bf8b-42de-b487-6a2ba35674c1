package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.audit.ExceptionAuditDto;
import com.enttribe.promptanalyzer.dto.audit.ToolAuditDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.model.PromptAudit;
import com.enttribe.promptanalyzer.model.ToolAudit;

import java.util.List;
import java.util.Map;

/**
 * Manages audit logging operations for prompts and exceptions.
 * This service provides functionality to save and retrieve audit records,
 * helping track system activities and exceptions for monitoring purposes.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface AuditService {

    String saveExceptionAudit(ExceptionAuditDto exceptionAuditDto);
    String savePromptAudit(PromptAuditDto promptAuditDto);
    List<PromptAudit> getPromptAuditListByAuditId(String auditId);

    List<PromptAuditDto> searchPromptAudit(String filter, Integer offset, Integer size, String orderBy, String orderType);

    Long countPromtAudit(String filter);

    List<PromptAudit> getPromptAuditListByPromptId(String promptId);

    Map<String, String> saveToolAudit(ToolAuditDto toolAuditDto);

    List<ToolAudit> getToolAuditListByAuditId(String auditId);
}
