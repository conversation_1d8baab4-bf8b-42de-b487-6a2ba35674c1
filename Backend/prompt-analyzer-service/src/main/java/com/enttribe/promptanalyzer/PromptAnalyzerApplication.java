package com.enttribe.promptanalyzer;

import org.springframework.ai.embedding.BatchingStrategy;
import org.springframework.ai.embedding.TokenCountBatchingStrategy;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

@SpringBootApplication(scanBasePackages = {"com.enttribe.commons", "com.enttribe.promptanalyzer"})
public class PromptAnalyzerApplication {


	@Bean
	@ConditionalOnMissingBean(BatchingStrategy.class)
	BatchingStrategy milvusBatchingStrategy() {
		return new TokenCountBatchingStrategy();
	}


	public static void main(String[] args) {
		SpringApplication.run(PromptAnalyzerApplication.class, args);
	}

}
