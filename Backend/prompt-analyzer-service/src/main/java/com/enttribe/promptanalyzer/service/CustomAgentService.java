package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;

import java.util.Map;

/**
 * Handles custom agent operations and query planning.
 * This service manages the generation of plans based on user queries
 * and creates trigger name descriptions for custom agents.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface CustomAgentService {
    Map<String,String> getPlanforUserQuery(CustomAgentDto agent);
    Map<String,String> createTriggerNameDescription(String userQuery);
}
