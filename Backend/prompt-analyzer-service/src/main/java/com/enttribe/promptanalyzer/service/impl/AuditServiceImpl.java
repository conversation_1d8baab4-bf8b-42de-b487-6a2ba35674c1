package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dao.ExceptionAuditDao;
import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dao.PromptAuditDao;
import com.enttribe.promptanalyzer.dao.ToolAuditDao;
import com.enttribe.promptanalyzer.dto.audit.ExceptionAuditDto;
import com.enttribe.promptanalyzer.dto.audit.ToolAuditDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.model.ExceptionAudit;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.PromptAudit;
import com.enttribe.promptanalyzer.model.ToolAudit;
import com.enttribe.promptanalyzer.service.AuditService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.AuditConvertor;
import com.enttribe.promptanalyzer.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Implementation of the {@link AuditService} interface.
 * This class provides the business Audit logs of prompt and exception handling.
 * It manages the persistence of both exception and prompt audit records, offering
 * functionality to track system behavior, errors, and prompt interactions.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AuditServiceImpl implements AuditService {

    private final ExceptionAuditDao exceptionDao;
    private final PromptAuditDao promptAuditDao;
    private final LlmModelDao llmModelDao;
    private final CustomFilter customFilter;
    private final ToolAuditDao toolAuditDao;

    /**
     * Saves an exception audit record to the database.
     *
     * @param exceptionAuditDto The DTO containing exception audit information to be saved
     * @return "success" if the operation was successful, "failed" if an error occurred
     */
    @Override
    public String saveExceptionAudit(ExceptionAuditDto exceptionAuditDto) {
        try {
            ExceptionAudit exceptionAudit = new ExceptionAudit();
            exceptionAudit.setTimestamp(new Date());
            exceptionAudit.setExceptionMessage(exceptionAuditDto.getExceptionMessage());
            exceptionAudit.setExceptionTrace(exceptionAuditDto.getExceptionTrace());
            exceptionAudit.setMethodName(exceptionAuditDto.getMethodName());
            exceptionAudit.setApplicationName(exceptionAuditDto.getApplicationName());
            exceptionAudit.setPromptId(exceptionAuditDto.getPromptId());
            exceptionAudit.setCategory(exceptionAuditDto.getCategory());
            exceptionAudit.setAuditId(exceptionAuditDto.getAuditId());

            // Convert Map to JSON String (if needed)
            if (exceptionAuditDto.getMethodParameters() != null) {
                exceptionAudit.setMethodParameters(JsonUtils.convertToJSON(exceptionAuditDto.getMethodParameters()));
                log.debug("Converted method parameters to JSON: {}", exceptionAudit.getMethodParameters());
            }
            if(exceptionAuditDto.getIdentifier() != null ){
                exceptionAudit.setIdentifier(JsonUtils.convertToJSON(exceptionAuditDto.getIdentifier()));
                log.debug("Converted identifier to JSON: {}", exceptionAudit.getIdentifier());
            }

            exceptionDao.save(exceptionAudit);
            log.debug("Exception audit saved successfully for audit ID: {}", exceptionAudit.getAuditId());
            return "success";
        } catch (Exception e) {
            log.error("Error saving exception audit: {}", e.getMessage(), e);
            return "failed";
        }
    }

    /**
     * Saves a prompt audit record to the database.
     * This method captures various metrics and details about prompt interactions.
     *
     * @param promptAuditDto The DTO containing prompt audit information to be saved
     * @return "success" if the operation was successful, "failed" if an error occurred
     */
    @Override
    public String savePromptAudit(PromptAuditDto promptAuditDto) {
        log.info("Inside savePromptAudit method with auditId: {}", promptAuditDto.getAuditId());
        try {
            PromptAudit promptAudit = new PromptAudit();
            promptAudit.setModel(promptAuditDto.getModel());
            promptAudit.setResponseText(promptAuditDto.getResponseText());
            promptAudit.setTotalToken(promptAuditDto.getTotalToken());
            promptAudit.setPromptToken(promptAuditDto.getPromptToken());
            promptAudit.setResponseTime(promptAuditDto.getResponseTime());
            promptAudit.setApplicationName(promptAuditDto.getApplicationName());
            promptAudit.setGenerationTokens(promptAuditDto.getGenerationTokens());
            promptAudit.setCategory(promptAuditDto.getCategory());
            promptAudit.setPromptId(promptAuditDto.getPromptId());
            promptAudit.setAuditId(promptAuditDto.getAuditId());
            promptAudit.setMetaTags(JsonUtils.convertToJSON(promptAuditDto.getMetaTags()));
            promptAudit.setChatOptions(JsonUtils.convertToJSON(promptAuditDto.getChatOptions()));
            promptAudit.setFunctionArgs(promptAuditDto.getFunctionArgs());
            promptAudit.setPromptName(promptAuditDto.getPromptName());
            promptAudit.setHttpStatus(promptAuditDto.getHttpStatus());
            promptAudit.setRequestText(promptAuditDto.getRequestText());
            promptAudit.setStatus(promptAuditDto.getStatus());
            promptAudit.setErrorMessage(promptAuditDto.getErrorMessage());
            promptAudit.setTotalCost(calculateTotalCost(promptAuditDto));
            promptAudit.setProvider(promptAuditDto.getProvider());
            promptAudit.setCreationTime(promptAuditDto.getCreationTime());
            promptAudit.setAgentName(promptAuditDto.getAgentName());

            promptAuditDao.save(promptAudit);
            return "success";
        } catch (Exception e) {
            log.error("Error saving prompt audit: {}", e.getMessage(), e);
            return "failed";
        }
    }

    /**
     * Calculates the total cost for the given prompt audit based on input and output token costs.
     *
     * @param promptAuditDto the prompt audit data transfer object containing the model, provider, prompt token, and total token information.
     * @return the total cost of the tokens for the given model and provider. Returns 0 if an error occurs during the calculation.
     */
    private double calculateTotalCost(PromptAuditDto promptAuditDto) {
        try {
            LlmModel llmModel = llmModelDao.findModelAndProvider(promptAuditDto.getModel(), promptAuditDto.getProvider());

            // returning total cost
            return (promptAuditDto.getPromptToken() * (llmModel.getInputCost() / 1_000_000)) +
                               (promptAuditDto.getTotalToken() * (llmModel.getOutputCost() / 1_000_000));
        } catch (Exception e) {
            log.error("Exception in calculating cost: {}", e.getMessage());
            return 0;
        }
    }

    private double calculateTotalCost(String model, String provider, long promptToken, long totalToken) {
        try {
            LlmModel llmModel = llmModelDao.findModelAndProvider(model, provider);

            // returning total cost
            return (promptToken * (llmModel.getInputCost() / 1_000_000)) +
                               (totalToken * (llmModel.getOutputCost() / 1_000_000));
        } catch (Exception e) {
            log.error("Exception in calculating cost: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * Retrieves a list of prompt audit records associated with a specific audit ID.
     *
     * @param auditId The unique identifier used to find related prompt audit records
     * @return List of PromptAudit objects matching the given audit ID
     */
    @Override
    public List<PromptAudit> getPromptAuditListByAuditId(String auditId) {
        return promptAuditDao.getPromptAuditListByAuditId(auditId);
    }

    @Override
    public List<PromptAudit> getPromptAuditListByPromptId(String promptId) {
        return promptAuditDao.getPromptAuditListByPromptId(promptId);
    }

    @Override
    public Map<String, String> saveToolAudit(ToolAuditDto toolAuditDto) {
        try {
            ToolAudit toolAudit = mapToToolAudit(toolAuditDto);
            toolAuditDao.save(toolAudit);
            log.debug("Tool audit saved successfully for audit ID: {}", toolAudit.getAuditId());
            return Map.of(APIConstants.RESULT, APIConstants.SUCCESS);
        } catch (Exception e) {
            return Map.of(APIConstants.RESULT, e.getMessage());
        }

    }

    @Override
    public List<ToolAudit> getToolAuditListByAuditId(String auditId) {
        log.debug("Getting tool audit list by audit id : {}", auditId);
        return toolAuditDao.getToolAuditListByAuditId(auditId);
    }

    private ToolAudit mapToToolAudit(ToolAuditDto dto) {
        ToolAudit toolAudit = new ToolAudit();
        toolAudit.setId(dto.getId());
        toolAudit.setRequestText(dto.getRequestText());
        toolAudit.setChatOptions(dto.getChatOptions());
        toolAudit.setAuditId(dto.getAuditId());
        toolAudit.setToolCallRequest(dto.getToolCallRequest());
        toolAudit.setToolDefinitions(dto.getToolDefinitions());
        toolAudit.setToolResponse(dto.getToolResponse());
        toolAudit.setAgentName(dto.getAgentName());
        toolAudit.setPromptId(dto.getPromptId());
        toolAudit.setPromptName(dto.getPromptName());
        toolAudit.setTotalToken(dto.getTotalToken());
        toolAudit.setPromptToken(dto.getPromptToken());
        toolAudit.setGenerationTokens(dto.getGenerationTokens());
        toolAudit.setResponseTime(dto.getResponseTime());
        toolAudit.setCreationTime(dto.getCreationTime());
        toolAudit.setModel(dto.getModel());
        toolAudit.setProvider(dto.getProvider());
        toolAudit.setTotalCost(calculateTotalCost(dto.getModel(), dto.getProvider(), dto.getPromptToken(), dto.getTotalToken()));
        toolAudit.setApplicationName(dto.getApplicationName());
        toolAudit.setStatus(dto.getStatus());
        return toolAudit;
    }


    @Override
    public List<PromptAuditDto> searchPromptAudit(String filter, Integer offset, Integer size, String orderBy, String orderType) {
       List<PromptAudit> promptAuditList = customFilter.searchByFilter(PromptAudit.class, filter, orderBy, orderType, offset, size);
      log.debug("Searching prompt audit records : {}", promptAuditList.size());
      return AuditConvertor.getPromptAuditDtoList(promptAuditList);
    }

    @Override
    public Long countPromtAudit(String filter) {
        log.debug("Counting prompt audit records");
        return customFilter.countByFilter(PromptAudit.class, filter);
    }



}
