package com.enttribe.promptanalyzer.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "intent")
public class IntentHintConfig {

    private String hintFilter;
    private String hintValue;
    private String filterExpressionFormat;
    private String metadataKey;

    private double defaultSimilarityThreshold;
    private double altSimilarityThreshold;
    private double exactMatchScoreThreshold;
    private int defaultTopK;
}
