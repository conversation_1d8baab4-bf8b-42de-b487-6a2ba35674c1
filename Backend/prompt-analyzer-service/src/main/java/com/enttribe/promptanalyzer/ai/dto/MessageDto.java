package com.enttribe.promptanalyzer.ai.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * This class represents a data transfer object for chat completion requests, 
 * containing parameters for generating chat responses.
 * Author: VisionWaves
 * Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class MessageDto {

    /**
     * The role of the sender of the message (e.g., "user" or "assistant").
     */
    private String role;

    /**
     * The content of the message.
     */
    private String content;

    /**
     * Constructs a new MessageDto with the specified role and content.
     *
     * @param role the role of the message sender
     * @param content the content of the message
     */
    public MessageDto(String role, String content) {
        this.role = role;
        this.content = content;
    }
}