package com.enttribe.promptanalyzer.ai.dto;

import java.util.Map;

public class VectorMetaData {

    private String vectorDatabase = "milvus";
    private String databaseName = "default";
    private String collectionName = "vector-store";
    private String embeddingModel = "nomic-embed-text-v1_5-preview1";
    private String chatModel = "llama-3.3-70b-versatile";
    private String provider = "groq";

    public static Builder builder() {
        return new Builder();
    }

    @Override
    public String toString() {
        return "VectorMetaData{" +
                "vectorDatabase='" + vectorDatabase + '\'' +
                ", databaseName='" + databaseName + '\'' +
                ", collectionName='" + collectionName + '\'' +
                ", embeddingModel='" + embeddingModel + '\'' +
                ", chatModel='" + chatModel + '\'' +
                ", provider='" + provider + '\'' +
                '}';
    }

    public static VectorMetaData fromMap(Map<String, String> valueMap) {

        return VectorMetaData.builder()
                .vectorDatabase(valueMap.get("vectorDatabase") != null ? valueMap.get("vectorDatabase") : "milvus")
                .databaseName(valueMap.get("databaseName") != null ? valueMap.get("databaseName") : "default")
                .collectionName(valueMap.get("collectionName") != null ? valueMap.get("collectionName") : "vector_store_knowledge_base")
                .chatModel(valueMap.get("chatModel") != null ? valueMap.get("chatModel") : "llama-3.3-70b-versatile")
                .provider(valueMap.get("provider") != null ? valueMap.get("provider") : "groq")
                .build();
    }

    public static class Builder {
        private final VectorMetaData vectorMetaData;

        private Builder() {
            this.vectorMetaData = new VectorMetaData();
        }

        public Builder vectorDatabase(String vectorDatabase) {
            vectorMetaData.vectorDatabase = vectorDatabase;
            return this;
        }

        public Builder databaseName(String databaseName) {
            vectorMetaData.databaseName = databaseName;
            return this;
        }

        public Builder collectionName(String collectionName) {
            vectorMetaData.collectionName = collectionName;
            return this;
        }

        public Builder embeddingModel(String embeddingModel) {
            vectorMetaData.embeddingModel = embeddingModel;
            return this;
        }

        public Builder chatModel(String chatModel) {
            vectorMetaData.chatModel = chatModel;
            return this;
        }

        public Builder provider(String provider) {
            vectorMetaData.provider = provider;
            return this;
        }

        public VectorMetaData build() {
            return vectorMetaData;
        }

    }

    public String getVectorDatabase() {
        return vectorDatabase;
    }

    public void setVectorDatabase(String vectorDatabase) {
        this.vectorDatabase = vectorDatabase;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getCollectionName() {
        return collectionName;
    }

    public void setCollectionName(String collectionName) {
        this.collectionName = collectionName;
    }

    public String getEmbeddingModel() {
        return embeddingModel;
    }

    public void setEmbeddingModel(String embeddingModel) {
        this.embeddingModel = embeddingModel;
    }

    public String getChatModel() {
        return chatModel;
    }

    public void setChatModel(String chatModel) {
        this.chatModel = chatModel;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }
}
