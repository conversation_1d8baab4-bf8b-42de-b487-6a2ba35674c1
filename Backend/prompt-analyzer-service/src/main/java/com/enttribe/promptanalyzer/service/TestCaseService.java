package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.TestCaseRequestDto;
import com.enttribe.promptanalyzer.model.TestCase;

import java.util.List;
import java.util.Map;

/**
 * Manages test case operations and validation scenarios.
 * This service handles the creation, updating, and management of test cases,
 * providing functionality for test case tracking and execution with search capabilities.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface TestCaseService {

    Map<String, String> create(TestCaseRequestDto requestDto);

    Map<String, String> update(TestCase updatedTestcase);

    Map<String, String> deleteTestcase(Integer id);

    List<TestCase> search(String filter, Integer offset, Integer size, String orderBy, String orderType);

    Long count(String filter);

    Map<String, String> createBatch(List<TestCaseRequestDto> requestDtos);

}
