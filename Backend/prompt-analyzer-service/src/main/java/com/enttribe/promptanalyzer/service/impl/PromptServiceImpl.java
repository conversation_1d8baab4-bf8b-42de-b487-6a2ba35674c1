package com.enttribe.promptanalyzer.service.impl;


import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dto.prompt.PromptVariableDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVersionDetailsDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDtoSdk;
import com.enttribe.promptanalyzer.dto.prompt.PromptRequestDto;
import com.enttribe.promptanalyzer.dto.AssertionTemplateDto;

import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.service.PromptService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.CSVUtils;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.util.PromptConvertor;
import com.enttribe.promptanalyzer.util.SdkUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Implementation of the {@link PromptService} interface.
 * This class provides the actual business logic for managing prompts and prompt variables
 * for a specific application. It interacts with the data access layer to fetch and modify prompt data.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PromptServiceImpl implements PromptService {

    private final PromptDao promptDao;
    private final CustomFilter customFilter;
    private final LlmModelDao llmModelDao;
    private final EntityManager entityManager;

    private static final List<String> csvColumnHeader = List.of("Application", "Name", "Category",
            "PromptId", "Temperature", "MaxToken", "Version", "JsonMode", "TopP", "Type", "LlmGuard", "AssertionTemplate", "DefaultFormat","Tags","Messages", "LLMModel");

    private static final String RESULT = "result";
    private static final String SUCCESS= "success";
    private static final String PUBLISH = "PUBLISH";

    private static final int MAX_IMPORT_RECORDS = 100;
    private static final int LLM_MODEL_PARTS_LENGTH = 2;
    private static final int VARIABLE_GROUP_INDEX = 2;

    /**
     * Saves a new prompt.
     * Description:
     * This method is responsible for saving a new prompt entity to the database.It maps the
     * data from the provided `PromptDto` to the `Prompt` entity, sets the created time, and saves the entity in the database.
     * If any constraint violations or other exceptions occur, the method logs the error and throws a `BusinessException`.
     *
     * @param promptDto The `PromptDto` object containing the data to be saved.
     * @return A map containing the result of the save operation (e.g., "success").
     * @throws BusinessException If there is an issue with saving the prompt, including constraint violations or other exceptions.
     */
    @Override
    public Map<String, String> savePrompt(PromptDto promptDto) {
        log.debug("Inside @method createPrompt");
        Map<String, String> result = new HashMap<>();
        try {
            List<PromptVersionDetailsDto> versionsOfPrompt = getVersionsOfPrompt(promptDto.getApplication(), promptDto.getName(), promptDto.getCategory(), promptDto.getStatus());
            List<String> versionList = versionsOfPrompt.stream().map(PromptVersionDetailsDto::getVersion).toList();
            log.debug("versions of prompt : {}", versionList);
            String nextVersion = getNextVersion(versionList, promptDto.getStatus());
            log.debug("next version : {}", nextVersion);

            Prompt prompt = new Prompt();
            prompt.setVersion(nextVersion);
            prompt.setPromptId(newPromptId(promptDto.getApplication(), promptDto.getName(), promptDto.getCategory(), nextVersion));

            mapDtoToEntity(prompt, promptDto);

            prompt.setCreatedTime(new Date());

            Prompt savedPrompt = promptDao.save(prompt);
            log.info("Prompt saved successfully with prompt id : {}", prompt.getPromptId());

            result.put(RESULT, SUCCESS);
            result.put("promptId", String.valueOf(savedPrompt.getId()));
        } catch (ConstraintViolationException e) {
            ConstraintViolation<?> constraintViolation = e.getConstraintViolations().stream().findFirst().orElse(null);
            log.error("ConstraintViolationException while creating prompt: {}", constraintViolation, e);
            throw new BusinessException(String.format("%s %s", constraintViolation.getPropertyPath(), constraintViolation.getMessage()), e);
        } catch (Exception e) {
            log.error("Error while creating prompt: {}", e.getMessage(), e);
            throw new BusinessException("Unable to save prompt", e);
        }
        return result;
    }

    /**
     * Maps data from a PromptDto to a Prompt entity.
     * Updates the messages and tools collections, and sets the LLM model.
     *
     * @param prompt The target Prompt entity to update
     * @param promptDto The source DTO containing new data
     * @throws IllegalArgumentException if LLM model is not found
     */
    private void mapDtoToEntity(Prompt prompt, PromptDto promptDto) {
        log.debug("Mapping PromptDto to Prompt entity for application: {}, name: {}", promptDto.getApplication(), promptDto.getName());
        prompt.setApplication(promptDto.getApplication());
        prompt.setCategory(promptDto.getCategory());
        prompt.setStatus(promptDto.getStatus());
        prompt.setTemperature(promptDto.getTemperature());
        prompt.setMaxToken(promptDto.getMaxTokens());
        prompt.setTopP(promptDto.getTopP());
        prompt.setName(promptDto.getName());
        prompt.setAssertionTemplate(promptDto.getAssertionTemplate());
        prompt.setDefaultFormat(promptDto.getDefaultFormat());
        Boolean jsonMode = promptDto.getJsonMode();
        prompt.setJsonMode(!(jsonMode == null || !jsonMode));
        prompt.setLlmGuard(promptDto.getLlmGuard());
        prompt.setType(promptDto.getType());
        prompt.setTag(promptDto.getTags());

        if (promptDto.getMessages() != null) {
            // Clear the existing collection
            prompt.getMessages().clear();

            // Add all messages from DTO to the existing collection
            for (Message message : promptDto.getMessages()) {
                message.setPrompt(prompt); // Associate the message with the prompt
                prompt.getMessages().add(message); // Add the message to the collection
            }
        } else {
            // If the incoming DTO has null messages, clear the existing collection
            prompt.getMessages().clear();
        }

        LlmModel llmModel = llmModelDao.findByModelAndProvider(promptDto.getModel(), promptDto.getProvider(), "chat");
        Assert.notNull(llmModel, "llm model is not provided");
        prompt.setLlmModel(llmModel);
        prompt.setModifiedTime(new Date());
        log.debug("Mapped Prompt entity: {}", prompt);
    }

    /**
     * Retrieves the versions of a prompt based on the application, name, category, and status.
     * Description:
     * This method queries the database to fetch the versions of a prompt based on the provided parameters: application,
     * name, category, and status. It then filters out the version `"v-0"` and returns the remaining versions.
     *
     * @param application The application name associated with the prompt.
     * @param name        The name of the prompt.
     * @param category    The category of the prompt.
     * @param status      The status of the prompt.
     * @return A list of versions of the prompt, excluding the `"v-0"` version.
     */
    @Override
    public List<PromptVersionDetailsDto> getVersionsOfPrompt(String application, String name, String category, String status) {
        List<PromptVersionDetailsDto> versionsOfPrompt = promptDao.getVersionsOfPrompt(application, name, category, status);
        return versionsOfPrompt.stream().filter(detail -> !detail.getVersion().equals("v-0")).toList();
    }

    /**
     * Soft deletes a prompt by marking it as deleted without actually removing it from the database.
     * Description:
     * This method sets the `deleted` flag of the specified prompt to `true`, effectively soft deleting it.
     * The prompt is retrieved from the database using its ID, and if found, the `deleted` status is updated.
     *
     * @param id The ID of the prompt to be softly deleted.
     * @return If the prompt is successfully updated, a success message is returned, otherwise, a failure message is returned.
     */
    @Override
    public Map<String, String> softDelete(int id) {
        Map<String, String> result = new HashMap<>();
        Optional<Prompt> prompt1 = promptDao.findById(id);
        if (prompt1.isPresent()) {
            Prompt prompt = prompt1.get();
            prompt.setDeleted(true);
            promptDao.save(prompt);
            log.info("Prompt with ID {} marked as deleted", id);
            result.put(RESULT, SUCCESS);
        } else {
            log.warn("Prompt with ID {} not found for deletion", id);
            result.put(RESULT, "failed");
        }
        return result;
    }

    /**
     * Retrieves a prompt by its ID.
     * Description:
     * This method fetches the prompt from the database using the provided ID. If the prompt is found, it is
     * prompt data returned. If the prompt is not found, an exception is thrown.
     * The method ensures that the provided ID is not null and raises an exception if the ID is missing.
     *
     * @param id The ID of the prompt to retrieve.
     * @return prompt data of the retrieved prompt.
     * @throws IllegalArgumentException if the provided ID is null.
     * @throws  BusinessException if no prompt is found with the provided ID.
     */
    @Override
    public PromptConvertorDto getPromptById(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("ID is required.");
        }

        Optional<Prompt> promptOptional = promptDao.findById(id);

        if (promptOptional.isPresent()) {
            Prompt prompt = promptOptional.get();
            // Convert the Prompt entity to a PromptConvertorDto
            return PromptConvertor.getPromptDto(prompt);
        } else {
            log.debug("No prompt found with ID: {}", id);
            throw new BusinessException("Error while fetching data: No prompt found with ID " + id);
        }
    }

    /**
     * Retrieves a prompt by its ID for SDK.
     * Description:
     * This method fetches the prompt from the database using the provided ID. If the prompt is found, it is
     * prompt returned. If the prompt is not found, a `BusinessException` is thrown.
     *
     * @param id The ID of the prompt to retrieve.
     * @return prompt data for SDK.
     * @throws BusinessException if no prompt is found with the provided ID.
     */
    @Override
    public PromptDtoSdk findPromptById(Integer id) {
        Prompt prompt = promptDao.findById(id).orElseThrow(()
                -> new BusinessException("Prompt with ID " + id + " not found."));
        return SdkUtils.getPromptDto(prompt);
    }

    /**
     * Checks if a prompt with the specified name, application, category, and status exists in the database.
     * Description:
     * This method queries the database to check if a prompt exists with the given `name`, `application`, `category`, and `status`.
     * It returns a map with a boolean value indicating whether the prompt exists. If an error occurs during the process,
     * a `BusinessException` is thrown with a relevant error message.
     *
     * @param promptRequestDto The DTO containing the details of the prompt (name, application, category, and status).
     * @return A map containing a single entry with the key "result" and a boolean value indicating the existence of the prompt.
     * @throws BusinessException if an error occurs while checking for the existence of the prompt in the database.
     */
    @Override
    public Map<String, Object> exists(PromptRequestDto promptRequestDto) {
        try {
            boolean exists = promptDao.existsByNameAndApplicationAndCategoryAndStatus(promptRequestDto.getName(), promptRequestDto.getApplication(), promptRequestDto.getCategory(), promptRequestDto.getStatus());
            return Map.of(RESULT, exists);
        } catch (Exception e) {
            log.error("Error while checking existence of prompt: ", e);
            throw new BusinessException("An error occurred while processing the existence check.", e);
        }
    }

    /**
     * Updates the assertion template of an existing prompt.
     * Description:
     * This method allows updating the assertion template of a prompt with the specified `promptId`.
     * The prompt's status is checked to ensure it is "PUBLISH". If the status is not "PUBLISH", an exception is thrown.
     * If the prompt exists and its status is valid, the assertion template is updated, and the modification time is set to the current time.
     * The method returns a map indicating the result of the operation.
     *
     * @param assertionTemplateDto(promptId,assertionTemplate) The data transfer object containing the details of the assertion template to be updated,
     *                                   including the prompt ID and the new assertion template content.
     * @return A map with a key "result", containing the value "success" if the update was successful.
     * @throws BusinessException If the prompt is not found, the status is not "PUBLISH", or any other error occurs.
     */
    @Override
    public Map<String, String> updateAssertionTemplate(AssertionTemplateDto assertionTemplateDto) {
        Map<String, String> response = new HashMap<>();
        try {
            Prompt existingPrompt = promptDao.findById(assertionTemplateDto.getPromptId()).orElseThrow(()
                    -> new BusinessException("Prompt with ID " + assertionTemplateDto.getPromptId() + " not found."));

            // Check if the prompt's status is PUBLISH
            if (!PUBLISH.equalsIgnoreCase(existingPrompt.getStatus())) {
                throw new BusinessException("Prompt status is not PUBLISH. Cannot update assertionTemplate.");
            }

            existingPrompt.setAssertionTemplate(assertionTemplateDto.getAssertionTemplate());
            existingPrompt.setModifiedTime(new Date());
            promptDao.save(existingPrompt);
            response.put(RESULT, SUCCESS);
        } catch (BusinessException e) {
            log.error("@class promtService @method updateAssertionTemplate error ", e);
            throw new BusinessException(e.getMessage());
        }
        return response;
    }

    /**
     * Updates an existing prompt with new details provided in the PromptDto.
     * Description:
     * This method updates the details of an existing prompt. It performs a validation to ensure the prompt exists.
     * If the prompt's status is `DRAFT` and the new status is `PUBLISH`, it calculates the next version and updates the prompt version accordingly.
     * The method updates the prompt with the provided data and sets the modified timestamp. The updated prompt is then saved in the database.
     *
     * @param promptDto The data transfer object containing the updated details for the prompt, including the new status, version, and other attributes.
     * @return A map with a key "result" indicating the success of the update operation (e.g., "success").
     * @throws BusinessException If the prompt does not exist, the status is invalid, or any error occurs during the update process.
     * @throws ConstraintViolationException If any constraint violation occurs during the update.
     */
    @Override
    public Map<String, String> updatePrompt(PromptDto promptDto) {
        Map<String, String> result = new HashMap<>();
        try {
            Prompt existingPrompt = promptDao.findById(promptDto.getId())
                    .orElseThrow(() -> new BusinessException("prompt does not exist"));

            String status = existingPrompt.getStatus();
            if (status.equalsIgnoreCase("DRAFT") && promptDto.getStatus().equalsIgnoreCase(PUBLISH)) {
                List<PromptVersionDetailsDto> versionsOfPrompt = getVersionsOfPrompt(promptDto.getApplication(), promptDto.getName(), promptDto.getCategory(), promptDto.getStatus());
                List<String> versionList = versionsOfPrompt.stream().map(PromptVersionDetailsDto::getVersion).toList();
                log.debug("versions of prompt : {}", versionList);
                String nextVersion = getNextVersion(versionList, promptDto.getStatus());
                log.debug("next version : {}", nextVersion);
                existingPrompt.setVersion(nextVersion);
                existingPrompt.setPromptId(newPromptId(promptDto.getApplication(), promptDto.getName(), promptDto.getCategory(), nextVersion));
            }

            mapDtoToEntity(existingPrompt, promptDto);

            existingPrompt.setModifiedTime(new Date());

            promptDao.save(existingPrompt);
            result.put(RESULT, SUCCESS);

        } catch (ConstraintViolationException e) {
            ConstraintViolation<?> constraintViolation = e.getConstraintViolations().stream().findFirst().orElse(null);
            log.error("ConstraintViolationException while updating prompt", e);
            throw new BusinessException(String.format("%s %s", constraintViolation.getPropertyPath(), constraintViolation.getMessage()), e);
        } catch (Exception e) {
            log.error("Unexpected error while updating prompt", e);
            throw new BusinessException("Unable to update prompt", e);
        }
        return result;
    }

    /**
     * Searches for prompts based on the provided filter and pagination parameters.
     * Description:
     * This method searches for prompts by applying the provided filter criteria, sorting by the specified fields, and
     * supporting pagination. It returns a list of prompt objects, which are a converted representation
     * of the filtered prompts.
     *
     * @param filter The filter criteria to apply for searching the prompts.
     * @param offset The starting point (index) for the search result pagination.
     * @param size The number of results to return per page.
     * @param orderBy The field by which the results should be ordered.
     * @param orderType The direction of the ordering (e.g., "ASC" or "DESC").
     * @return A list of prompt objects representing the filtered, sorted, and paginated prompts.
     */
    @Override
    public List<PromptConvertorDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        List<Prompt> prompts = customFilter.searchByFilter(Prompt.class, filter, orderBy, orderType, offset, size);
        return PromptConvertor.getPromptDtoList(prompts);
    }

    /**
     * Counts the total number of prompts in the database.
     * Description:
     * This method counts how many prompts present in DB.
     *
     * @param filter The filter criteria used to count the prompts.
     * @return The total number of prompt.
     */
    @Override
    public Long count(String filter) {
        return customFilter.countByFilter(Prompt.class, filter);
    }


    /**
     * Generates a new prompt ID based on the provided parameters.
     *
     * @param application The application name
     * @param name The prompt name
     * @param category The category
     * @param version The version string
     * @return A formatted prompt ID string
     */
    private String newPromptId(String application, String name, String category, String version) {
        String promptId = (application + "-" + category + "-" + name + "-" + version).trim().replace(" ", "_");
        log.debug("Generated new prompt ID: {}", promptId);
        return promptId;
    }

    /**
     * Determines the next version number based on existing versions.
     *
     * @param versions List of existing version strings
     * @param status The status of the prompt
     * @return The next version string
     */
    private String getNextVersion(List<String> versions, String status) {
        log.debug("Determining next version for status: {}", status);
        if (status.equalsIgnoreCase("DRAFT")) return "v-0";
        if (versions == null || versions.isEmpty()) {
            return "v-1"; // Default to v-0 if no versions are provided
        }

        int maxVersion = versions.stream().filter(version -> version.matches("v-\\d+")) // Only consider valid "v-<number>" strings
                .map(version -> version.replace("v-", ""))   // Remove the prefix "v-"
                .mapToInt(Integer::parseInt)                // Parse the numeric part
                .max()                                      // Find the max value
                .orElse(1);                                 // Default to 0 if no valid versions

        return "v-" + (maxVersion + 1); // Increment and return the next version
    }

    /**
     * Retrieves a list of prompts associated with the specified application name.
     * This method fetches all prompts that are linked to the provided application name
     * and returns them as a list of  Prompt objects for SDK.
     *
     * @param appName the name of the application for which the prompts are being retrieved.
     *                This parameter cannot be null or empty.
     * @return a list of prompt representing the prompts for the given application.
     * @throws IllegalArgumentException if the provided {@code appName} is null or empty.
     */
    @Override
    public List<PromptDtoSdk> getPromptByApplication(String appName) {
        log.debug("@class PromptServiceImpl @method getPromptByApplication @param appName : {} ", appName);

        if (appName == null || appName.trim().isEmpty()) {
            throw new IllegalArgumentException("Application name cannot be null or empty.");
        }

        List<Prompt> prompts = promptDao.getPromptByApplication(appName);
        return SdkUtils.getPromptDtoList(prompts);
    }

    /**
     * Retrieves a list of distinct applications based on the provided application name.
     * This method queries the database to fetch distinct applications that match the given
     * {@code applicationName}.
     *
     * @param applicationName the name of the application used to filter distinct applications.
     *                        Can be null or empty to retrieve all distinct applications.
     * @return a list of distinct application names matching the filter criteria.
     */
    @Override
    public List<String> getDistinctApplications(String applicationName) {
        return promptDao.getDistinctApplications(applicationName);
    }

    /**
     * Retrieves a list of distinct categories for the specified application name.
     * This method queries the database to fetch distinct categories that match the given
     * {@code applicationName}.
     *
     * @param applicationName the name of the application used to filter distinct categories.
     *                        Can be null or empty to retrieve all distinct categories.
     * @return a list of distinct categories associated with the specified application name.
     */
    @Override
    public List<String> getDistinctCategoriesByApp(String applicationName) {
        return promptDao.getDistinctCategoriesByApp(applicationName);
    }

    /**
     * Filters the prompts based on the provided filter criteria.
     * This method applies the filters from the provided {@code filterMap} to the database
     * query and returns a list of Prompt entities that match the specified conditions.
     * The method dynamically builds a query using CriteriaBuilder and CriteriaQuery based on the key-value pairs in the {@code filterMap}. It is assumed
     * that each filter key represents a column name in the {@code Prompt} entity, and the value
     * represents the value to be matched for equality.
     *
     * @param filterMap a map of filter criteria, where the key is the column name and the value is
     *                  the value to match for equality.
     *                  (e.g., {"name": "example", "status": "ACTIVE"})
     * @return a list of Prompt entities that match the specified filter criteria.
     * @throws IllegalArgumentException if any of the filter keys are invalid or the criteria cannot be applied.
     */
    @Override
    public List<Prompt> filter(Map<String, Object> filterMap) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Prompt> query = cb.createQuery(Prompt.class);
        Root<Prompt> root = query.from(Prompt.class);

        List<Predicate> predicates = new ArrayList<>();

        // Build predicates based on filterMap keys
        for (Map.Entry<String, Object> entry : filterMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // Assuming all filters are for equality
            if (value != null) {
                predicates.add(cb.equal(root.get(key), value));
            }
        }

        query.select(root).where(predicates.toArray(new Predicate[0]));

        // Execute the query
        return entityManager.createQuery(query).getResultList();

    }

    /**
     * Searches for prompts for v1 based on the provided filter criteria and pagination options.
     * This method uses a custom filter implementation to retrieve a list of Prompt
     * entities that match the specified filter. It supports pagination, ordering, and custom
     * filter conditions to efficiently fetch results based on user input.
     *
     * @param filter the filter criteria as a string, which is used to apply search conditions
     *               (e.g., a JSON or query-like string that the filter implementation can process).
     * @param offset the offset (starting point) for pagination, indicating the first record to retrieve.
     * @param size the number of results to retrieve starting from the offset (pagination).
     * @param orderBy the field by which to sort the results (e.g., "name", "status").
     * @param orderType the type of sorting (e.g., "ASC" for ascending or "DESC" for descending).
     * @return a list of Prompt entities that match the filter and pagination criteria.
     * @throws IllegalArgumentException if any of the filter parameters are invalid or improperly formatted.
     */
    @Override
    public List<Prompt> searchV1(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        return customFilter.searchByFilter(Prompt.class, filter, orderBy, orderType, offset, size);
    }

    /**
     * Retrieves basic details of prompts by the specified application name.
     * This method queries the database to fetch basic details of prompts associated with a
     * particular application. The returned details include the prompt name and prompt ID,
     * which are returned in a list of maps.
     *
     * @param applicationName the name of the application whose prompt details are to be fetched.
     *                        It should not be null or empty.
     * @return a list of maps where each map contains basic details of a prompt,
     *         such as "name" and "promptId", for the given application.
     * @throws IllegalArgumentException if the application name is null or empty.
     */
    @Override
    public List<Map<String, String>> getPromptBasicDetailByApplication(String applicationName) {
        List<Map<String, String>> promptDetails = new ArrayList<>();
        List<Object[]> promptBasicDetails = promptDao.getPromptBasicDetailByApplication(applicationName);
        for (Object[] result : promptBasicDetails) {
            Map<String, String> response = new HashMap<>();
            String name = (String) result[0];
            String promptId = (String) result[1];
            response.put("name", name);
            response.put("promptId", promptId);
            promptDetails.add(response);
        }
        return promptDetails;
    }

    /**
     * Exports prompt data of a specified application into a CSV file.
     * This method retrieves a list of prompts associated with the given application name and
     * converts it into a CSV format. The prompt data is then returned as a downloadable resource.
     *
     * @param appName the name of the application whose prompt data needs to be exported.
     *                It should not be null or empty.
     * @return a ResponseEntity containing the CSV file as a resource.
     * @throws JsonProcessingException if there is an error during JSON processing while extracting data.
     */
    @Override
    public ResponseEntity<Resource> exportPrompt(String appName) {
        log.debug("Inside method exportPrompt");
        List<Prompt> promptList = promptDao.getPromptByApplication(appName);
        return getResponse(appName, promptList);
    }

    @Override
    public ResponseEntity<Resource> exportPromptsByIds(List<Integer> promptIds) {
        log.debug("Inside method exportPromptsByIds");
        List<Prompt> promptList = promptDao.findAllById(promptIds);
        promptList.removeIf(prompt -> !Boolean.FALSE.equals(prompt.getDeleted()));
        return getResponse("prompts", promptList);
    }

    @Override
    public PromptDto fetchPromptById(Integer id) {
        log.debug("Inside method fetchPromptById");
        return promptDao.findById(id)
                .map(PromptConvertor::mapToPromptDto)
                .orElseThrow(() -> new BusinessException("Prompt not found with ID: " + id));
    }

    @Override
    public PromptDto getPromptByName(String promptName) {
        log.debug("get prompt by name: {}", promptName);
        return Optional.ofNullable(promptDao.findByName(promptName))
                .map(PromptConvertor::mapToPromptDto)
                .orElseThrow(() -> new BusinessException("Prompt not found with name: " + promptName));
    }

    private static ResponseEntity<Resource> getResponse(String fileName, List<Prompt> promptList) {
        log.debug("Inside method getResponse");
        List<Function<Prompt, Object>> fieldExtractors = getPromptFieldExtractors();
        log.debug("Going to export prompts as CSV");
        return CSVUtils.exportCSV(promptList, csvColumnHeader, fileName, fieldExtractors);
    }

    private static List<Function<Prompt, Object>> getPromptFieldExtractors() {
        return List.of(
                Prompt::getApplication,
                Prompt::getName,
                Prompt::getCategory,
                Prompt::getPromptId,
                Prompt::getTemperature,
                Prompt::getMaxToken,
                Prompt::getVersion,
                Prompt::getJsonMode,
                Prompt::getTopP,
                Prompt::getType,
                Prompt::getLlmGuard,
                prompt -> prompt.getAssertionTemplate() == null ? "NULL" : prompt.getAssertionTemplate(),
                prompt -> prompt.getDefaultFormat() == null ? "NULL" : prompt.getDefaultFormat(),
                prompt -> prompt.getTag() == null ? "NULL" : prompt.getTag(),
                prompt -> {
                    try {
                        return JsonUtils.convertToJSON(prompt.getMessages());
                    } catch (JsonProcessingException e) {
                        // Consider logging here if you want
                        return "{}";
                    }
                },
                prompt -> {
                    var llmModel = prompt.getLlmModel();
                    return llmModel == null ? "NULL" : llmModel.getProvider() + "/" + llmModel.getModel();
                }
        );
    }

    /**
     * Imports prompts from a CSV file and saves them to the database.
     * This method processes the given CSV file, maps each record to a Prompt object,
     * and saves or updates the corresponding data in the database. The CSV file should contain
     * columns matching the expected format and structure for prompt data.
     *
     * @param file the CSV file containing the prompt data to be imported. It should not be null.
     * @return a ResponseEntity containing the result of the import process. If successful,
     *         it returns a response indicating the status of the import; otherwise, an error message.
     * @throws IOException if an I/O error occurs while processing the file.
     * @throws BusinessException if an error occurs while saving or updating the prompts.
     */
    @Override
    public ResponseEntity<Resource> importPrompt(MultipartFile file) {
        log.debug("Inside importPrompt method");
        int maxRecords = MAX_IMPORT_RECORDS;
        try {
            Function<CSVRecord, Prompt> recordTransformer = csvRecord -> {
                Prompt prompt = new Prompt();
                prompt.setApplication(csvRecord.get("Application"));
                prompt.setName(csvRecord.get("Name"));
                prompt.setCategory(csvRecord.get("Category"));
                prompt.setPromptId(csvRecord.get("PromptId"));

                String temperatureValue = csvRecord.get("Temperature");
                prompt.setTemperature(Double.valueOf(temperatureValue));

                String maxTokenValue = csvRecord.get("MaxToken");
                prompt.setMaxToken(Integer.valueOf(maxTokenValue));

                prompt.setVersion(csvRecord.get("Version"));

                String jsonModeValue = csvRecord.get("JsonMode");
                prompt.setJsonMode(Boolean.valueOf(jsonModeValue));

                String topPValue = csvRecord.get("TopP");
                prompt.setTopP(Double.valueOf(topPValue));

                prompt.setType(csvRecord.get("Type"));

                String llmGuardValue = csvRecord.get("LlmGuard");
                prompt.setLlmGuard(Boolean.valueOf(llmGuardValue));

                prompt.setAssertionTemplate(csvRecord.get("AssertionTemplate"));
                prompt.setDefaultFormat(csvRecord.get("DefaultFormat"));
                prompt.setTag(csvRecord.get("Tags"));

                try {
                    List<Message> messages = JsonUtils.convertJsonToList(csvRecord.get("Messages"), Message.class);
                    prompt.setMessages(messages);
                    for (Message message : messages) {
                        message.setPrompt(prompt);
                    }
                } catch (JsonProcessingException e) {
                    throw new BusinessException("Error processing messages", e);
                }
                String llmModelData = csvRecord.get("LLMModel");
                if (llmModelData != null && !llmModelData.isEmpty()) {
                    String[] llmModelParts = llmModelData.split("/");
                    if (llmModelParts.length == LLM_MODEL_PARTS_LENGTH) {
                        String provider = llmModelParts[0];
                        String model = llmModelParts[1];
                        LlmModel llmModel = llmModelDao.findByModelAndProvider(model, provider, "chat");
                        prompt.setLlmModel(llmModel);
                    }
                }
                if (prompt.getLlmModel() == null) {
                    throw new ResourceNotFoundException("unable to determine llm model");
                }
                return prompt;
            };

            String exportedFileName = file.getOriginalFilename().replaceAll("\\.[^.]*$", "");
            log.info("Importing prompts from file: {}", exportedFileName);
            return CSVUtils.importCSV(file, csvColumnHeader, recordTransformer, this::saveOrUpdatePrompt, exportedFileName, maxRecords);
        } catch (IOException | BusinessException e) {
            log.error("Error during prompt import: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ByteArrayResource(e.getMessage().getBytes()));
        }
    }

    /**
     * Retrieves the prompt variables for a given prompt ID.
     * This method fetches the Prompt by its ID from the database and extracts the
     * corresponding prompt variables into a prompt. If the prompt with the specified ID
     * does not exist, a BusinessException is thrown.
     *
     * @param promptId the promptId of the prompt whose variables are to be retrieved. Must not be null.
     * @return a prompt containing the variables of the prompt.
     * @throws BusinessException if no prompt with the specified ID is found.
     */
    @Override
    public PromptVariableDto getPromptVariablesById(String promptId) {
        log.debug("Inside getPromptVariablesById method");

        Prompt prompt = promptDao.findByPromptId(promptId);
        if (prompt == null) {
            throw new BusinessException("Prompt ID " + promptId + " not found");
        }

        PromptVariableDto dto = extractPromptVariables(prompt);
        log.debug("Successfully fetched prompt variables");
        return dto;
    }

    /**
     * Retrieves the prompt variables for all prompts associated with a specific application.
     * This method fetches a list of prompts for the given application name and extracts
     * the relevant prompt variables into a list of variables. If no prompts
     * are found for the specified application, it returns an empty list.
     *
     * @param appName the name of the application for which prompt variables are to be fetched.
     *                It cannot be null or empty.
     * @return a list of Prompt Variables containing the variables of all prompts
     *         for the given application. If no prompts are found, an empty list is returned.
     */
    @Override
    public List<PromptVariableDto> getPromptVariablesByAppName(String appName) {
        log.debug("Inside getPromptVariablesByAppName method");

        List<Prompt> prompts = promptDao.getPromptByApplication(appName);
        log.debug("prompts count {}",prompts.size());
        List<PromptVariableDto> promptVariableDtos = new ArrayList<>();

        for (Prompt prompt : prompts) {
            PromptVariableDto dto = extractPromptVariables(prompt);
            promptVariableDtos.add(dto);
        }
        log.debug("Successfully fetched prompt variables for application: " + appName);
        return promptVariableDtos;
    }

    /**
     * Extracts prompt variables from a Prompt entity and organizes them by message role.
     * Processes user, system, and assistant messages to extract variables from their content.
     *
     * @param prompt The Prompt entity to extract variables from
     * @return DTO containing organized prompt variables by role
     */
    private PromptVariableDto extractPromptVariables(Prompt prompt) {
        log.debug("Extracting prompt variables for prompt");
        String promptName = prompt.getName();
        List<String> userPromptVariables = new ArrayList<>();
        List<String> systemPromptVariables = new ArrayList<>();
        List<String> assistantPromptVariables = new ArrayList<>();

        for (Message message : prompt.getMessages()) {
            String content = message.getContent();

            List<String> variables = extractVariables(content);

            if (message.getRole().equalsIgnoreCase("user")) {
                userPromptVariables.addAll(variables);
            } else if (message.getRole().equalsIgnoreCase("system")) {
                systemPromptVariables.addAll(variables);
            } else if (message.getRole().equalsIgnoreCase("assistant")) {
                assistantPromptVariables.addAll(variables);
            }
        }

        PromptVariableDto dto = new PromptVariableDto();
        dto.setPromptName(promptName);
        dto.setUserPrompt(userPromptVariables);
        dto.setSystemPrompt(systemPromptVariables);
        dto.setAssistantPrompt(assistantPromptVariables);
        log.debug("successfully Extracted prompt variables");
        return dto;
    }

    /**
     * Extracts variables from message content using regex patterns.
     * Matches variables in both {{variable}} and {variable} formats.
     * The method uses a regex pattern to identify and extract variable names
     * enclosed in either double curly braces {{}} or single curly braces {}.
     * It allows for optional spaces around the variable names.
     *
     * @param content The message content to extract variables from
     * @return List of extracted variable names
     */
    private static List<String> extractVariables(String content) {
        log.debug("Extracting variables from content");
        List<String> variables = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\{\\{\\s*([^\\s{}\"]+?)\\s*\\}\\}|\\{\\s*([^\\s{}\"]+?)\\s*\\}");
        Matcher matcher = pattern.matcher(content);


        while (matcher.find()) {
            // Check if the match corresponds to the {{variable}} format
            if (matcher.group(1) != null) {
                variables.add(matcher.group(1));
            }
            // Check if the match corresponds to the {variable} format
            else if (matcher.group(VARIABLE_GROUP_INDEX) != null) {
                variables.add(matcher.group(VARIABLE_GROUP_INDEX));
            }
        }
        log.debug("Extracted variables: {}", variables);
        return variables;
    }

    /**
     * Saves a new prompt or updates an existing one based on promptId.
     * If a prompt with the same promptId exists, it updates that prompt;
     * otherwise, creates a new prompt with PUBLISH status.
     *
     * @param prompt The Prompt entity to save or update
     */
    private void saveOrUpdatePrompt(Prompt prompt) {
        log.debug("Saving or updating prompt with Prompt ID: {}", prompt.getPromptId());
        Prompt existingPrompt = promptDao.findByPromptId(prompt.getPromptId());

        if (existingPrompt != null) {
            mapPrompt(prompt, existingPrompt);
            existingPrompt.setModifiedTime(new Date());
            promptDao.save(existingPrompt);
            log.info("Updating existing prompt with ID: {}", prompt.getPromptId());
        } else {
            prompt.setStatus(PUBLISH);
            prompt.setModifiedTime(new Date());
            prompt.setCreatedTime(new Date());
            log.info("Creating new prompt with name: {}", prompt.getName());
            promptDao.save(prompt);
        }
        log.info("Prompt saved or updated successfully with ID: {}", prompt.getPromptId());
    }

    /**
     * Maps properties from one Prompt entity to another.
     *
     * @param source The source Prompt containing new values
     * @param target The target Prompt to update
     */
    private void mapPrompt(Prompt source, Prompt target) {
        log.debug("Mapping properties from source prompt to target prompt");
        if (source.getApplication() != null) {
            target.setApplication(source.getApplication());
        }
        if (source.getName() != null) {
            target.setName(source.getName());
        }
        if (source.getCategory() != null) {
            target.setCategory(source.getCategory());
        }
        if (source.getTemperature() != null) {
            target.setTemperature(source.getTemperature());
        }
        if (source.getMaxToken() != null) {
            target.setMaxToken(source.getMaxToken());
        }
        if (source.getVersion() != null) {
            target.setVersion(source.getVersion());
        }
        if (source.getJsonMode() != null) {
            target.setJsonMode(source.getJsonMode());
        }
        if (source.getTopP() != null) {
            target.setTopP(source.getTopP());
        }
        if (source.getType() != null) {
            target.setType(source.getType());
        }
        if (source.getLlmGuard() != null) {
            target.setLlmGuard(source.getLlmGuard());
        }
        extractedMapPrompt(source, target);
        log.debug("Mapped target prompt: {}", target);
    }

    private static void  extractedMapPrompt(Prompt source, Prompt target) {
        log.debug("Inside extractedMapPrompt");
        if (source.getAssertionTemplate() != null) {
            target.setAssertionTemplate(source.getAssertionTemplate());
        }
        if (source.getDefaultFormat() != null) {
            target.setDefaultFormat(source.getDefaultFormat());
        }
        if (source.getTag() != null) {
            target.setTag(source.getTag());
        }
        if (source.getLlmModel() != null) {
            target.setLlmModel(source.getLlmModel());
        }
        List<Message> messages = source.getMessages();
        if (messages != null && !messages.isEmpty()) {
            target.getMessages().clear();
            target.getMessages().addAll(messages);
            for (Message message : messages) {
                message.setPrompt(target);
            }
        }
        log.debug("Successfully mapped prompt");
    }

    @Override
    public Map<String, String> updateTagById(Integer id, Map<String, String> tags) {
        String newTag = tags.get("tags");
        log.debug("inside @method updateTagById. @param  : id -> {} tags : {}", id, newTag);
        Prompt prompt = promptDao.findById(id).orElseThrow(() -> new BusinessException("prompt is not found for id : " + id));

        try {
            prompt.setTag(newTag);
            promptDao.save(prompt);
            return Map.of(RESULT, SUCCESS);
        } catch (Exception e) {
            log.error("error while updating tag of prompt : {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }

}
