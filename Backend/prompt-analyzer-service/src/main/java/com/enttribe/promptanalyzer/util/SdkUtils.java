package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.MessageConvertorDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDtoSdk;
import com.enttribe.promptanalyzer.dto.tool.ToolDtoSdk;
import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.model.McpServer;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.Tool;

import java.util.ArrayList;
import java.util.List;


/**
 * Utility class for converting model entities to SDK DTO objects.
 * Provides methods to convert individual entities and lists of entities for SDK use.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public final class SdkUtils {

    // Private constructor to prevent instantiation
    private SdkUtils() {
    }

    /**
     * Converts a list of Prompt entities to a list of PromptDtoSdk objects.
     *
     * @param prompts the list of Prompt entities to convert
     * @return a list of converted PromptDtoSdk objects
     */
    public static List<PromptDtoSdk> getPromptDtoList(List<Prompt> prompts) {
        List<PromptDtoSdk> promptList = new ArrayList<>();
        for (Prompt prompt : prompts) {
            promptList.add(getPromptDto(prompt));
        }
        return promptList;
    }

    /**
     * Converts a single Prompt entity to a PromptDtoSdk object.
     *
     * @param prompt the Prompt entity to convert
     * @return the converted PromptDtoSdk object
     */
    public static PromptDtoSdk getPromptDto(Prompt prompt) {
        return PromptDtoSdk.builder()
                .id(prompt.getId().toString())
                .promptName(prompt.getName())
                .category(prompt.getCategory())
                .promptId(prompt.getPromptId())
                .temperature(prompt.getTemperature())
                .maxTokens(prompt.getMaxToken())
                .topP(prompt.getTopP())
                .model(prompt.getLlmModel().getModel())
                .defaultFormat(prompt.getDefaultFormat())
                .messages(getMessageDtos(prompt.getMessages()))
                .provider(prompt.getLlmModel().getProvider())
                .jsonMode(prompt.getJsonMode())
                .llmGuard(prompt.getLlmGuard())
                .applicationName(prompt.getApplication())
                .build();
    }

    /**
     * Converts a list of Message entities to a list of MessageConvertorDto objects.
     *
     * @param messages the list of Message entities to convert
     * @return a list of converted MessageConvertorDto objects, or an empty list if the input is null or empty
     */
    private static List<MessageConvertorDto> getMessageDtos(List<Message> messages) {
        if (messages == null || messages.isEmpty()) {
            return List.of();
        }

        return messages.stream()
                .map(message -> MessageConvertorDto.builder()
                        .role(message.getRole())
                        .content(message.getContent())
                        .build())
                .toList();
    }

    /**
     * Converts a Tool entity to a ToolDtoSdk object.
     *
     * @param tool the Tool entity to convert
     * @return the converted ToolDtoSdk object
     */
    public static ToolDtoSdk convertToToolDtoSdk(Tool tool) {
        return ToolDtoSdk.builder()
                .id(tool.getId())
                .toolName(tool.getToolName())
                .byteCodeMap(tool.getByteCodeMap())
                .className(tool.getClassName())
                .toolId(tool.getToolId())
                .requestType(tool.getRequestType())
                .description(tool.getDescription())
                .returnDirect(tool.getReturnDirect())
                .build();
    }

    /**
     * Converts a list of Tool entities to a list of ToolDtoSdk objects.
     *
     * @param tool the list of Tool entities to convert
     * @return a list of converted ToolDtoSdk objects
     */
    public static List<ToolDtoSdk> getToolDtoListSdk(List<Tool> tool) {
        return tool.stream()
                .map(SdkUtils::convertToToolDtoSdk)
                .toList();
    }

    /**
     * Converts a list of KnowledgeBase entities to a list of KnowledgeBaseSdkDto objects.
     *
     * @param knowledgeBases the list of KnowledgeBase entities to convert
     * @return a list of converted KnowledgeBaseSdkDto objects
     */
    public static List<KnowledgeBaseSdkDto> getKnowledgeBaseSdkDtoList(List<KnowledgeBase> knowledgeBases) {
        return knowledgeBases.stream()
                .map(SdkUtils::mapToKnowledgeBaseSdkDto)
                .toList();
    }

    /**
     * Converts a single KnowledgeBase entity to a KnowledgeBaseSdkDto object.
     *
     * @param knowledgeBase the KnowledgeBase entity to convert
     * @return the converted KnowledgeBaseSdkDto object, or null if the input is null
     */
    public static KnowledgeBaseSdkDto mapToKnowledgeBaseSdkDto(KnowledgeBase knowledgeBase) {
        if (knowledgeBase == null) {
            return null;
        }
        return KnowledgeBaseSdkDto.builder()
                .id(knowledgeBase.getId())
                .name(knowledgeBase.getName())
                .type(knowledgeBase.getType())
                .description(knowledgeBase.getDescription())
                .docType(knowledgeBase.getDocType())
                .topK(knowledgeBase.getTopK())
                .filter(knowledgeBase.getFilter())
                .similarityThreshold(knowledgeBase.getSimilarityThreshold())
                .fileName(knowledgeBase.getFileName())
                .isContext(knowledgeBase.getIsContext())
                .returnDirect(knowledgeBase.getReturnDirect())
                .build();
    }

    public static List<McpServerDto> getMcpServersDtoListSdk(List<McpServer> mcpServers) {
        return mcpServers.stream()
                .map(SdkUtils::convertToMcpServersDtoSdk)
                .toList();
    }

    private static McpServerDto convertToMcpServersDtoSdk(McpServer mcpServer) {
        return McpServerDto.builder()
                .id(mcpServer.getId())
                .name(mcpServer.getName())
                .description(mcpServer.getDescription())
                .type(mcpServer.getType())
                .icon(mcpServer.getIcon())
                .url(mcpServer.getUrl())
                .isCustomServer(mcpServer.getIsCustomServer())
                .commandToRun(mcpServer.getCommandToRun())
                .build();
    }


}
