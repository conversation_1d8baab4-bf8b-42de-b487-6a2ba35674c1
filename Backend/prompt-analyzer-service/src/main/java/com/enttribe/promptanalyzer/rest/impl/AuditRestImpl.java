package com.enttribe.promptanalyzer.rest.impl;


import com.enttribe.promptanalyzer.dto.audit.ExceptionAuditDto;
import com.enttribe.promptanalyzer.dto.audit.ToolAuditDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.model.PromptAudit;
import com.enttribe.promptanalyzer.model.ToolAudit;
import com.enttribe.promptanalyzer.rest.AuditRest;
import com.enttribe.promptanalyzer.service.AuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing audit-related operations.
 * Provides endpoints for saving and retrieving exception and prompt audit records.
 * All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/audit")
@RequiredArgsConstructor
@Slf4j
public class AuditRestImpl implements AuditRest {


    private final AuditService auditService;

    /**
     * Saves an exception audit record.
     * Requires ROLE_API_EXCEPTION_SAVE security role.
     *
     * @param exceptionAuditDto The exception audit details to save
     * @return String containing the audit ID of the saved record
     * @apiNote Response Codes:
     *          200 - Save exception audit successfully
     *          500 - Error occurred during save operation
     */
    @Override
    public String saveExceptionAudit(ExceptionAuditDto exceptionAuditDto) {
        log.debug("Inside @method saveExceptionAudit. @param : auditId -> {}", exceptionAuditDto.getAuditId());
        return auditService.saveExceptionAudit(exceptionAuditDto);
    }

    /**
     * Saves a prompt audit record.
     * Requires ROLE_API_EXCEPTION_SAVE security role.
     *
     * @param promptAuditDto The prompt audit details to save
     * @return String containing the audit ID of the saved record
     * @apiNote Response Codes:
     *          200 - Save Prompt Audit exception successfully
     *          500 - Error occurred during save operation
     */
    @Override
    public String savePromptAudit(PromptAuditDto promptAuditDto) {
        log.info("Inside Saving prompt audit with data");
        return auditService.savePromptAudit(promptAuditDto);
    }

    /**
     * Retrieves a list of prompt audit records for a specific audit ID.
     * Requires ROLE_API_EXCEPTION_READ security role.
     *
     * @param auditId The audit ID to retrieve records for
     * @return List of PromptAudit records matching the audit ID
     * @apiNote Response Codes:
     *          200 - Get Prompt Audit List successfully
     *          500 - Error occurred during retrieval
     */
    @Override
    public List<PromptAudit> getPromptAuditListByAuditId(String auditId) {
        log.info("Retrieving prompt audit list for audit ID: {}", auditId);
       return auditService.getPromptAuditListByAuditId(auditId);

    }

    @Override
    public List<PromptAudit> getPromptAuditListByPromptId(String promptId) {
        log.info("Retrieving prompt audit list for prompt ID: {}", promptId);
        return auditService.getPromptAuditListByPromptId(promptId);
    }

    @Override
    public List<PromptAuditDto> searchPromptAudit(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        return auditService.searchPromptAudit(filter, offset, size, orderBy, orderType);
    }

    @Override
    public Long countPromptAudit(String filter) {
        return auditService.countPromtAudit(filter);
    }

    @Override
    public Map<String, String> saveToolAudit(ToolAuditDto toolAuditDto) {
        return auditService.saveToolAudit(toolAuditDto);
    }

    @Override
    public List<ToolAudit> getToolAuditListByAuditId(String auditId) {
        return auditService.getToolAuditListByAuditId(auditId);
    }

}
