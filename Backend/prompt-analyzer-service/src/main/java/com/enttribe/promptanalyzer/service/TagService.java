package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;

import java.util.List;
import java.util.Map;


/**
 * Service interface for managing Tag operations.
 * This interface defines the contract for handling tag-related business operations
 * such as searching, creating, updating, and deleting tags within the application.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface TagService {

    List<TagResponseDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType);
    Long count(String filter);
    Map<String, String> save(TagRequestDto dto);
    Map<String, String> softDelete(Integer id);
    Map<String, String> update(TagRequestDto dto);

}
