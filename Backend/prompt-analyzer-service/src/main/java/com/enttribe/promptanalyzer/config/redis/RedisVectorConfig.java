package com.enttribe.promptanalyzer.config.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.TokenCountBatchingStrategy;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.redis.RedisVectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import redis.clients.jedis.DefaultJedisClientConfig;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisClientConfig;
import redis.clients.jedis.JedisPooled;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;

@Configuration
@EnableConfigurationProperties({RedisProperties.class})
@ConditionalOnProperty(
        name = {"spring.ai.vectorstore.type"},
        havingValue = "redis",
        matchIfMissing = true
)
public class RedisVectorConfig {

    private static final Logger log = LoggerFactory.getLogger(RedisVectorConfig.class);

    @Value("${vector.redis.trustStorePath:}")
    private String trustStorePath;

    @Value("${vector.redis.trustStorePassword:}")
    private String trustStorePassword;

    @Value("${commons.ai.sdk.keyStoreInstance:jks}")
    private String keyStoreInstance;

    @Value("${spring.ai.vectorstore.redis.index-name}")
    private String indexName;

    @Value("${spring.ai.vectorstore.redis.prefix}")
    private String redisPrefix;

    @Value("${hint.collection.name}")
    private String hintCollectionName;

    @Value("${spring.data.redis.ssl.enable}")
    private boolean sslEnable;

    @Bean
    @Primary
    public VectorStore vectorStore(JedisPooled jedisPooled, EmbeddingModel embeddingModel) {
        log.info("creating redis vector store");
        RedisVectorStore redisVectorStore = RedisVectorStore.builder(jedisPooled, embeddingModel)
                .indexName(indexName)
                .prefix(redisPrefix)
                .metadataFields(
                        RedisVectorStore.MetadataField.tag("doc_id"),
                        RedisVectorStore.MetadataField.tag("custom_agent_id")
                )
                .initializeSchema(true)
                .batchingStrategy(new TokenCountBatchingStrategy())
                .build();

        redisVectorStore.afterPropertiesSet();
        return redisVectorStore;
    }

    @Bean
    public VectorStore vectorStoreHint(JedisPooled jedisPooled, EmbeddingModel embeddingModel) {
        RedisVectorStore redisVectorStore = RedisVectorStore.builder(jedisPooled, embeddingModel)
                .indexName(hintCollectionName)
                .prefix("emp_base")
                .metadataFields(
                        RedisVectorStore.MetadataField.tag("name"),
                        RedisVectorStore.MetadataField.numeric("id"),
                        RedisVectorStore.MetadataField.tag("first_name"),
                        RedisVectorStore.MetadataField.tag("last_name"),
                        RedisVectorStore.MetadataField.tag("employeeName"),
                        RedisVectorStore.MetadataField.tag("type"),
                        RedisVectorStore.MetadataField.tag("filter"),
                        RedisVectorStore.MetadataField.text("employeeData"),
                        RedisVectorStore.MetadataField.tag("entity"),
                        RedisVectorStore.MetadataField.tag("reference id")
                )
                .initializeSchema(true)
                .batchingStrategy(new TokenCountBatchingStrategy())
                .build();

        redisVectorStore.afterPropertiesSet();
        return redisVectorStore;
    }

    @Bean
    public JedisPooled jedisPooled(RedisProperties redisProperties) throws CertificateException, KeyStoreException, IOException, NoSuchAlgorithmException, KeyManagementException {
        JedisConnectionFactory jedisConnectionFactory = this.getJedisConnectionFactory(redisProperties);
        return this.jedisPooled(jedisConnectionFactory);
    }

    private JedisPooled jedisPooled(JedisConnectionFactory jedisConnectionFactory) {
        String host = jedisConnectionFactory.getHostName();
        int port = jedisConnectionFactory.getPort();

        if (sslEnable) {
            SSLSocketFactory sslSocketFactory = jedisConnectionFactory.getClientConfiguration()
                    .getSslSocketFactory().orElseThrow(() -> new IllegalStateException("SSL socket factory is not available"));
            JedisClientConfig clientConfig = DefaultJedisClientConfig.builder()
                    .ssl(jedisConnectionFactory.isUseSsl())
                    .sslSocketFactory(sslSocketFactory)
                    .clientName(jedisConnectionFactory.getClientName())
                    .timeoutMillis(jedisConnectionFactory.getTimeout())
                    .password(jedisConnectionFactory.getPassword())
                    .build();

            return new JedisPooled(new HostAndPort(host, port), clientConfig);
        } else {
            JedisClientConfig clientConfig = DefaultJedisClientConfig.builder()
                    .clientName(jedisConnectionFactory.getClientName())
                    .timeoutMillis(jedisConnectionFactory.getTimeout())
                    .password(jedisConnectionFactory.getPassword())
                    .build();

            return new JedisPooled(new HostAndPort(host, port), clientConfig);
        }
    }

    private JedisConnectionFactory getJedisConnectionFactory(RedisProperties redisProperties) throws KeyStoreException, IOException, NoSuchAlgorithmException, KeyManagementException, CertificateException {

        RedisStandaloneConfiguration standaloneConfig = new RedisStandaloneConfiguration();
        standaloneConfig.setHostName(redisProperties.getHost());
        standaloneConfig.setPort(redisProperties.getPort());
        standaloneConfig.setDatabase(redisProperties.getDatabase());

        if (redisProperties.getPassword() != null) {
            standaloneConfig.setPassword(redisProperties.getPassword());
        }

        if (sslEnable) {
            log.info("setting up ssl for redis connection");
            KeyStore trustStore = KeyStore.getInstance(keyStoreInstance);
            trustStore.load(new FileInputStream(trustStorePath), trustStorePassword.toCharArray());

            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance("X509");
            trustManagerFactory.init(trustStore);

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustManagerFactory.getTrustManagers(), null);

            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();


            JedisClientConfiguration clientConfig = JedisClientConfiguration.builder()
                    .useSsl()
                    .sslSocketFactory(sslSocketFactory)
                    .build();

            return new JedisConnectionFactory(standaloneConfig, clientConfig);
        }

        JedisClientConfiguration clientConfig = JedisClientConfiguration.builder().build();

        return new JedisConnectionFactory(standaloneConfig, clientConfig);
    }

}
