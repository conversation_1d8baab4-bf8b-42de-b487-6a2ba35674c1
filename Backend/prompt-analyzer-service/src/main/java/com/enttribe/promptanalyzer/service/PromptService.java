package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDtoSdk;
import com.enttribe.promptanalyzer.dto.prompt.PromptRequestDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVersionDetailsDto;
import com.enttribe.promptanalyzer.dto.AssertionTemplateDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVariableDto;
import com.enttribe.promptanalyzer.model.Prompt;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Manages prompt-related operations and lifecycle management.
 * This service handles all aspects of prompt management including creation,
 * versioning, searching, and variable management.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface PromptService {

    Map<String, String> savePrompt(PromptDto prompt);

    List<PromptVersionDetailsDto> getVersionsOfPrompt(String application, String name, String category, String status);

    Map<String, String> softDelete(int id);

    PromptConvertorDto getPromptById(Integer id);

    PromptDtoSdk findPromptById(Integer id);

    Map<String, Object> exists(PromptRequestDto promptRequestDto);

    Map<String, String> updateAssertionTemplate(AssertionTemplateDto assertionTemplateDto);

    Map<String, String> updatePrompt(PromptDto prompt);

    List<PromptConvertorDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType);

    Long count(String filter);

    List<PromptDtoSdk> getPromptByApplication(String appName);

    List<String> getDistinctApplications(String applicationName);

    List<String> getDistinctCategoriesByApp(String applicationName);

    List<Prompt> filter(Map<String, Object> filterMap);

    List<Prompt> searchV1(String filter, Integer offset, Integer size, String orderBy, String orderType);

    List<Map<String, String>> getPromptBasicDetailByApplication(String applicationName);


    ResponseEntity<Resource> exportPrompt(String appName);

    ResponseEntity<Resource> importPrompt(MultipartFile file);

    PromptVariableDto getPromptVariablesById(String promptId);

    List<PromptVariableDto> getPromptVariablesByAppName(String appName);
    
    Map<String, String> updateTagById(Integer id, Map<String, String> tags);

    ResponseEntity<Resource> exportPromptsByIds(List<Integer> promptIds);

    PromptDto fetchPromptById(Integer id);

    PromptDto getPromptByName(String promptName);
}
