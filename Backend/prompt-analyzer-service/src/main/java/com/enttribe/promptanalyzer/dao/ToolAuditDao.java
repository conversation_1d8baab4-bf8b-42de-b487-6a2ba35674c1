package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.ToolAudit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ToolAuditDao extends JpaRepository<ToolAudit, Integer> {

    @Query("SELECT t FROM ToolAudit t WHERE t.auditId = :auditId")
    List<ToolAudit> getToolAuditListByAuditId(String auditId);
}
