package com.enttribe.promptanalyzer.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class S3Service {

    private final AmazonS3 amazonS3;

    @Value("${s3BucketName}")
    private String bucketName;

    public List<String> uploadFileToS3(List<MultipartFile> files, String uniqueFilter, String s3FileNames) {
        log.info("Inside uploadFileToS3 method");

        // Execute case of update Delete existing files if file Path Json is provided
        if (StringUtils.hasText(s3FileNames)) {
            log.info("Going to delete existing files from S3");
            deleteExistingFilesFromS3(s3FileNames);
        }
        List<String> uploadedFileUrls = new ArrayList<>();
        for (MultipartFile file : files) {
            String newFileName = uniqueFilter + "_" + file.getOriginalFilename();
            try (InputStream inputStream = file.getInputStream()) {
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentLength(file.getSize());
                metadata.setContentType(file.getContentType());

                PutObjectRequest request = new PutObjectRequest(bucketName, newFileName, inputStream, metadata);
                amazonS3.putObject(request);
                log.info("New file uploaded successfully: {}", newFileName);
                uploadedFileUrls.add(newFileName);
            } catch (Exception e) {
                log.error("S3 upload failed for file {}: {}", newFileName, e.getMessage(), e);
                throw new BusinessException("S3 upload failed for bucket and connection of seaweedfs: " + bucketName + " for file: " + newFileName, e);
            }
        }
        return uploadedFileUrls;
    }

  

    private void deleteExistingFilesFromS3(String s3FileNames)  {
        log.info("Inside deleteExistingFilesFromS3 method");
        List<String> fileNames = extractFileName(s3FileNames);
        for (String fileName : fileNames) {
            amazonS3.deleteObject(bucketName, fileName);
            log.info("Deleted existing file from S3: {}", fileName);
        }
    }

    private List<String> extractFileName(String s3FileNames) {
        try {
            return JsonUtils.convertJsonToList(s3FileNames, String.class);
        } catch (JsonProcessingException e) {
            throw new BusinessException("Failed to extract file names from JSON", e);
        }
    }


}
