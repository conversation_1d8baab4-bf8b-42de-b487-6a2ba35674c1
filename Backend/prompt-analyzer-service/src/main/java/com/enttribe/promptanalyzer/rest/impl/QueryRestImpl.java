package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.query.QueryRequestDto;
import com.enttribe.promptanalyzer.dto.query.QueryResponseDto;
import com.enttribe.promptanalyzer.rest.QueryRest;
import com.enttribe.promptanalyzer.service.QueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing query operations.
 * Provides endpoints for creating, searching, and managing queries
 * with support for pagination and filtering.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/query")
@RequiredArgsConstructor
public class QueryRestImpl implements QueryRest {

    private final QueryService service;

    /**
     * Creates a new query.
     *
     * @param dto The query request data to save
     * @return Map containing the result of the create operation
     */
    @Override
    public Map<String, String> save(QueryRequestDto dto) {
        log.info("Creating a new query");
        return service.createQuery(dto);
    }

    /**
     * Searches for queries with pagination and sorting options.
     *
     * @param filter Optional filter criteria for searching queries
     * @param offset Required pagination offset
     * @param size Required pagination size
     * @param orderBy Optional field to order results by
     * @param orderType Optional order direction (asc/desc)
     * @return List of matching queries as QueryResponseDto objects
     */
    @Override
    public List<QueryResponseDto> search(
            String filter,
            Integer offset,
            Integer size,
            String orderBy,
            String orderType) {
        log.info("Searching queries");
        return service.search(filter, offset, size, orderBy, orderType);
    }

    /**
     * Counts the number of queries matching the optional filter.
     *
     * @param filter Optional filter criteria
     * @return Total count of matching queries
     */
    @Override
    public Long count(String filter) {
        return service.count(filter);
    }

    /**
     * Performs a soft delete of a query by its ID.
     *
     * @param id The ID of the query to delete
     * @return Map containing the result of the delete operation
     */
    @Override
    public Map<String, String> deleteById(Integer id) {
        log.info("Performing soft delete for query with ID: {}", id);
        return service.softDelete(id);
    }

}

