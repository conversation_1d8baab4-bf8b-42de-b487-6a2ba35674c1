package com.enttribe.promptanalyzer.dao;


import com.enttribe.promptanalyzer.model.Trigger;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for Trigger entity operations.
 * Provides basic CRUD operations for managing triggers in the database.
 * Extends JpaRepository to inherit standard database operations.
 *
 * <AUTHOR>
 * @version 1.0
 * @see Trigger
 * @see JpaRepository
 */
@Repository
public interface TriggerDao extends JpaRepository<Trigger, Integer> {

    @Query("SELECT t FROM Trigger t WHERE t.name = :name and t.deleted = false")
    Trigger findByName(String name);
}
