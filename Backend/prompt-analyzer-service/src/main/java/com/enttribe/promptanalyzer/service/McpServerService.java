package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import com.enttribe.promptanalyzer.model.McpServer;

import java.util.List;
import java.util.Map;

/**
 * Service interface for managing MCP servers.
 * Provides methods for creating, updating, searching, and deleting MCP servers.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface McpServerService {

    /**
     * Creates a new MCP server.
     *
     * @param serverDto The DTO containing the server details
     * @return Map containing the operation result
     */
    Map<String, String> create(McpServerDto serverDto);

    /**
     * Updates an existing MCP server.
     *
     * @param serverDto The DTO containing the updated server details
     * @return Map containing the operation result
     */
    Map<String, String> update(McpServerDto serverDto);

    /**
     * Searches for MCP servers based on specified criteria and pagination parameters.
     *
     * @param filter The search filter criteria
     * @param offset The starting position in the result set
     * @param size The maximum number of results to return
     * @param orderBy The field to sort results by
     * @param orderType The sort direction (ascending/descending)
     * @return List of MCP servers matching the search criteria
     */
    List<McpServer> search(String filter, Integer offset, Integer size, String orderBy, String orderType);

    /**
     * Counts MCP servers based on the provided filter.
     *
     * @param filter The filter criteria to count matching servers
     * @return Count of matching servers
     */
    Long count(String filter);

    /**
     * Soft deletes an MCP server by ID.
     *
     * @param id The ID of the server to delete
     * @return Map containing the operation result
     */
    Map<String, String> softDelete(Integer id);

    List<McpServerDto> getMcpServerByIds(List<Integer> ids);
}