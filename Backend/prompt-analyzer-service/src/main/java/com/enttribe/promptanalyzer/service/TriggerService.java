package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.trigger.TriggerRequestDto;
import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;

import java.util.List;
import java.util.Map;

/**
 * Manages trigger operations and event handling.
 * This service handles the creation and management of triggers,
 * providing functionality for trigger updates, searching, and tracking
 * with support for pagination and filtering.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface TriggerService {


    List<TriggerResponseDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType);

    Long count(String filter);

    Map<String, String> createTrigger(TriggerRequestDto dto);

    Map<String, String> updateTrigger(TriggerRequestDto dto);

    TriggerResponseDto getTriggerByName(String name);
}
