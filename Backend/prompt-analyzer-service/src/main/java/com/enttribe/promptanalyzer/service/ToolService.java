package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.tool.ToolWorkflowDto;
import com.enttribe.promptanalyzer.dto.tool.SwaggerDto;
import com.enttribe.promptanalyzer.dto.tool.ToolConvertorDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDtoSdk;
import com.enttribe.promptanalyzer.model.Tool;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Manages tool operations and integrations across the system.
 * This service handles tool lifecycle management including creation, updates,
 * and search operations. It also provides functionality for tool import/export
 * and integration with agents.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface ToolService {

    Map<String, String> createTool(ToolDto toolDto);

    List<ToolConvertorDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType);

    Long count(String filter);

    Map<String, String> softDelete(Integer id);

    Map<String, String> updateTool(ToolDto tool);

    List<ToolDtoSdk> getToolsByApplication(String appName);

    Map<String, Integer> generateTools(SwaggerDto swaggerDto);

    Map<String, String> changeToolStatus(Integer id, String status);

    void generateToolsFromWorkflow(ToolWorkflowDto toolWorkflowDto);

    ToolConvertorDto getToolById(Integer id);

    List<Tool> getToolByAgentId(Long agentId);

    List<ToolDtoSdk> getToolsByIds(List<Integer> ids);

    List<ToolConvertorDto> getToolsByIdsV1(List<Integer> ids);

    Map<String, String> registerAgentAsTool(Long agentId);

    boolean checkCompilation(String sourceCode, String className);

    ResponseEntity<Resource> exportTool(String appName);

    ResponseEntity<Resource> importTool(MultipartFile file);

    Map<String, Boolean> existsTool(String toolName);

    Map<String, String> updateTagById(Integer id, Map<String, String> tags);

    List<HashMap<String, String>> getToolCallbackProvider();

    ToolDto getToolByName(String toolName);

    List<ToolDto> getToolsDtoByAgentId(Long agentId);

    List<ToolDto> findToolsByIds(List<Integer> ids);
}
