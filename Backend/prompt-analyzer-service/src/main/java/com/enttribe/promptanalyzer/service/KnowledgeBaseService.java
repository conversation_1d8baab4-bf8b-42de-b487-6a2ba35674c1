package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.knowledge_base.DocumentRequestDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.VectorResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseSdkDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Manages knowledge base operations and document handling.
 * This service provides comprehensive functionality for managing knowledge bases,
 * including CRUD operations for documents and knowledge base entries, along with
 * search capabilities.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface KnowledgeBaseService {

    KnowledgeBaseResponseDto getKnowledgBaseById(Integer id);

    Map<String, String> saveDocument(DocumentRequestDto documentDto);

    Long count(String filter);

    List<KnowledgeBaseResponseDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType);

    Map<String, String> updateDocument(Integer id, DocumentRequestDto documentDto) throws JsonProcessingException;

    Map<String, String> softDelete(Integer id);

    List<KnowledgeBaseSdkDto> getKnowledgeBaseByIds(List<Integer> ids);

    VectorResponseDto saveInVector(List<String> contents);

    Map<String, String> updateTagById(Integer id, Map<String, String> tags);

    List<String> getTablesName(String name);

    Map<String, Object> existsWebsiteUrl(String webSiteUrl);

    Map<String, String> saveContent(DocumentRequestDto dto);

    ResponseEntity<String> milvusImportCsv(MultipartFile file);

    Map<String, String> getAnswer(String knowledgeBaseName, String userQuestion);

    ResponseEntity<Resource> exportKnowledgeBases(List<Integer> ids);

    ResponseEntity<Resource> importKnowledgeBases(MultipartFile file);

    Map<String, String> updateReturnDirect(Integer id, Boolean returnDirectValue);

    Map<String, String> saveWebSite(DocumentRequestDto documentDto);
    
    Map<String, Boolean> existsKnowledgeBaseName(String name);
}
