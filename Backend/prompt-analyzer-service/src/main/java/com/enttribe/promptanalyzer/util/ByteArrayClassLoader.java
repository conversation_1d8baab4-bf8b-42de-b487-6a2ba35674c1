package com.enttribe.promptanalyzer.util;

import java.util.Map;

/**
 * Custom class loader that loads classes from a map of byte arrays.
 * Allows dynamic loading of classes from bytecode stored in memory.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ByteArrayClassLoader extends ClassLoader {

    private final Map<String, byte[]> byteCodeMap;

    /**
     * Constructs a ByteArrayClassLoader with the specified map of class bytecode.
     *
     * @param byteCodeMap a map where the key is the class name and the value is the bytecode
     */
    public ByteArrayClassLoader(Map<String, byte[]> byteCodeMap) {
        this.byteCodeMap = byteCodeMap;
    }

    /**
     * Finds and loads a class with the specified name from the bytecode map.
     *
     * @param name the name of the class to load
     * @return the resulting Class object
     * @throws ClassNotFoundException if the class cannot be found in the bytecode map
     */
    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        byte[] byteCode = byteCodeMap.get(name);
        if (byteCode == null) {
            throw new ClassNotFoundException("Class not found: " + name);
        }
        return defineClass(name, byteCode, 0, byteCode.length);
    }
}
