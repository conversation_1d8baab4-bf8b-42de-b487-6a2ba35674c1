package com.enttribe.promptanalyzer.util;

/**
 * Utility class for generating formatted test case IDs.
 * Provides methods to convert integer IDs into a standardized string format.
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IdUtil {

    // Private constructor to prevent instantiation
    private IdUtil() {
    }

    private static final int LENGTH_TWO = 2;
    private static final int LENGTH_THREE = 3;
    private static final int LENGTH_FOUR = 4;

    public static String getTestCaseId( Integer id) {
        String entity = "TEST";
        String sid = String.valueOf(id);
        if (sid.length() == 1) {
            sid = entity + "-0000" + sid;
        } else if (sid.length() == LENGTH_TWO) {
            sid = entity + "-000" + sid;
        } else if (sid.length() == LENGTH_THREE) {
            sid = entity + "-00" + sid;
        } else if (sid.length() == LENGTH_FOUR) {
            sid = entity + "-0" + sid;
        } else {
            sid = entity + "-" + sid;
        }
        return sid;
    }

}
