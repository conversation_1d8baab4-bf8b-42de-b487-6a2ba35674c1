package com.enttribe.promptanalyzer.util;


import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import com.enttribe.promptanalyzer.model.Processor;

import java.util.List;

/**
 * Utility class for handling Processor entity and DTO conversions.
 * Provides methods for mapping between Processor entities and DTOs.
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ProcessorUtils {

    /**
     * Private constructor to prevent instantiation of utility class.
     */
    private ProcessorUtils() {
    }

    /**
     * Maps data from a ProcessorRequestDto to a Processor entity.
     *
     * @param requestDto The source DTO containing processor data
     * @param processor The target Processor entity to be updated
     * @return The updated Processor entity
     */
    public static Processor mapDtoToEntity(ProcessorRequestDto requestDto, Processor processor) {
        processor.setKey(requestDto.getKey());
        processor.setDisplayName(requestDto.getDisplayName());
        processor.setIcon(requestDto.getIcon());
        processor.setStyleType(requestDto.getStyleType());
        processor.setCategory(requestDto.getCategory());
        processor.setJsonStructure(requestDto.getJsonStructure());
        processor.setSubCategory(requestDto.getSubCategory());
        return processor;
    }

    /**
     * Maps data from a Processor entity to a ProcessorResponseDto.
     *
     * @param processor The source Processor entity
     * @param processorDto The target DTO to be updated
     * @return The updated ProcessorResponseDto
     */
    public static ProcessorResponseDto mapEntityToDto(Processor processor, ProcessorResponseDto processorDto) {
        processorDto.setId(processor.getId());
        processorDto.setKey(processor.getKey());
        processorDto.setDisplayName(processor.getDisplayName());
        processorDto.setIcon(processor.getIcon());
        processorDto.setStyleType(processor.getStyleType());
        processorDto.setCategory(processor.getCategory());
        processorDto.setSubCategory(processor.getSubCategory());
        return processorDto;
    }

    /**
     * Converts a Processor entity to a new ProcessorResponseDto.
     *
     * @param processor The Processor entity to convert
     * @return A new ProcessorResponseDto containing the processor data, or null if the input is null
     */
    public static ProcessorResponseDto convertToDTO(Processor processor) {
        if (processor == null) {
            return null;
        }
        return ProcessorResponseDto.builder()
                .id(processor.getId())
                .key(processor.getKey())
                .icon(processor.getIcon())
                .displayName(processor.getDisplayName())
                .category(processor.getCategory())
                .subCategory(processor.getSubCategory())
                .styleType(processor.getStyleType())
                .jsonStructure(processor.getJsonStructure())
                .build();

    }

    /**
     * Converts a list of Processor entities to a list of ProcessorResponseDtos.
     *
     * @param processors The list of Processor entities to convert
     * @return A list of ProcessorResponseDtos, or an empty list if the input is null or empty
     */
    public static List<ProcessorResponseDto> getProcessorList(List<Processor> processors) {
        if (processors == null || processors.isEmpty()) {
            return List.of();
        }
        return processors.stream()
                .map(ProcessorUtils::convertToDTO)
                .toList();
    }


}
