package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import com.enttribe.promptanalyzer.model.McpServer;
import com.enttribe.promptanalyzer.rest.McpServerRest;
import com.enttribe.promptanalyzer.service.McpServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * REST controller implementation for MCP server operations.
 * Provides endpoints for creating, updating, searching, counting, and deleting MCP servers.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/mcpserver")
@RequiredArgsConstructor
public class McpServerRestImpl implements McpServerRest {

    private final McpServerService mcpServerService;

    /**
     * Creates a new MCP server.
     *
     * @param serverDto The MCP server details to create
     * @return Map containing the result of the operation
     */
    @Override
    public Map<String, String> create(McpServerDto serverDto) {
        log.info("Creating new MCP server: {}", serverDto.getName());
        return mcpServerService.create(serverDto);
    }

    /**
     * Updates an existing MCP server.
     *
     * @param serverDto The MCP server details to update
     * @return Map containing the result of the operation
     */
    @Override
    public Map<String, String> update(McpServerDto serverDto) {
        log.info("Updating MCP server with ID: {}", serverDto.getId());
        return mcpServerService.update(serverDto);
    }

    /**
     * Searches for MCP servers based on provided filters and pagination parameters.
     *
     * @param filter    Optional filter string to search servers
     * @param offset    Optional pagination offset
     * @param size      Optional pagination size
     * @param orderBy   Optional field to order results by
     * @param orderType Optional order direction (asc/desc)
     * @return List of matching MCP servers
     */
    @Override
    public List<McpServer> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        log.info("Searching MCP servers with filter: {}", filter);
        return mcpServerService.search(filter, offset, size, orderBy, orderType);
    }

    /**
     * Counts MCP servers based on provided filter.
     *
     * @param filter Optional filter string to count matching servers
     * @return Map containing the count of matching servers
     */
    @Override
    public Long count(String filter) {
        log.info("Counting MCP servers with filter: {}", filter);
        return mcpServerService.count(filter);
    }

    /**
     * Deletes an MCP server by ID.
     *
     * @param id The ID of the MCP server to delete
     * @return Map containing the result of the operation
     */
    @Override
    public Map<String, String> softDelete(Integer id) {
        log.info("Deleting MCP server with ID: {}", id);
        return mcpServerService.softDelete(id);
    }

    @Override
    public List<McpServerDto> getMcpServerByIds(List<Integer> ids) {
        return mcpServerService.getMcpServerByIds(ids);
    }
}