package com.enttribe.promptanalyzer.sql;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Component for applying custom filters to database queries.
 * Provides methods for searching and counting entities based on dynamic query conditions.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
@Slf4j
public class CustomFilter {

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Searches for entities of the specified type based on the given filter criteria.
     *
     * @param type the entity class type
     * @param query the filter query string
     * @param orderBy the field to order by
     * @param orderType the order type (asc or desc)
     * @param offset the starting position of the result set
     * @param size the maximum number of results to return
     * @param <T> the type of the entity
     * @return a list of entities matching the filter criteria
     */
    public <T> List<T> searchByFilter(
            Class<T> type,
            String query,
            String orderBy,
            String orderType,
            Integer offset,
            Integer size) {
        // Initialize CriteriaBuilder and CriteriaQuery
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> criteria = builder.createQuery(type);
        Root<T> root = criteria.from(type);

        // Build predicates from query
        List<Predicate> predicates = parseQueryToPredicates(builder, root, query);

        // Apply the predicates to the query
        if (!predicates.isEmpty()) {
            criteria.where(predicates.toArray(new Predicate[0]));
        }

        // Apply ordering if provided
        applyOrdering(builder, criteria, root, orderBy, orderType);

        // Create and configure the query
        TypedQuery<T> typedQuery = entityManager.createQuery(criteria);
        applyPagination(typedQuery, offset, size);

        // Execute and return results
        return typedQuery.getResultList();
    }

    /**
     * Counts the number of entities of the specified type based on the given filter criteria.
     *
     * @param type the entity class type
     * @param query the filter query string
     * @param <T> the type of the entity
     * @return the count of entities matching the filter criteria
     */
    public <T> Long countByFilter(Class<T> type, String query) {
        // Initialize CriteriaBuilder and CriteriaQuery for counting
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> criteria = builder.createQuery(Long.class);
        Root<T> root = criteria.from(type);

        // Set the select clause to count rows
        criteria.select(builder.count(root));

        // Build predicates from query
        List<Predicate> predicates = parseQueryToPredicates(builder, root, query);

        // Apply the predicates to the query
        if (!predicates.isEmpty()) {
            criteria.where(predicates.toArray(new Predicate[0]));
        }

        // Execute the count query
        return entityManager.createQuery(criteria).getSingleResult();
    }

    /**
     * Parses a query string into a list of predicates for filtering.
     *
     * @param builder the CriteriaBuilder
     * @param root the root entity
     * @param query the filter query string
     * @param <T> the type of the entity
     * @return a list of predicates for filtering
     */
    private <T> List<Predicate> parseQueryToPredicates(CriteriaBuilder builder, Root<T> root, String query) {
        List<Predicate> predicates = new ArrayList<>();

        if (query == null || query.trim().isEmpty()) {
            return predicates;
        }

        String[] conditions = query.split(";");

        for (String condition : conditions) {
            condition = condition.trim();
            if (isOrGroup(condition)) {
                predicates.add(processOrConditionGroup(condition, builder, root));
            } else {
                addSinglePredicate(condition, builder, root, predicates);
            }
        }

        return predicates;
    }

    private boolean isOrGroup(String condition) {
        return condition.startsWith("(") && condition.endsWith(")");
    }

    private <T> Predicate processOrConditionGroup(String condition, CriteriaBuilder builder, Root<T> root) {
        String[] orConditions = condition.substring(1, condition.length() - 1).split(",");
        List<Predicate> orPredicates = new ArrayList<>();

        for (String orCondition : orConditions) {
            orCondition = orCondition.trim();
            Predicate predicate = createPredicate(builder, root, orCondition);
            if (predicate != null) {
                orPredicates.add(predicate);
            }
        }

        return orPredicates.isEmpty() ? null : builder.or(orPredicates.toArray(new Predicate[0]));
    }

    private <T> void addSinglePredicate(String condition, CriteriaBuilder builder, Root<T> root, List<Predicate> predicates) {
        Predicate predicate = createPredicate(builder, root, condition);
        if (predicate != null) {
            predicates.add(predicate);
        }
    }

    private <T> Predicate createPredicate(CriteriaBuilder builder, Root<T> root, String condition) {
        if (condition.contains("==")) {
            String[] keyValue = condition.split("==");
            String key = keyValue[0].trim();
            String value = keyValue[1].trim().replace("'", ""); // Remove single quotes

            if (key.contains(".")) {
                return createNestedPropertyPredicate(builder, root, key, value, true);
            } else {
                Class<?> fieldType = root.get(key).getJavaType();
                Object convertedValue = convertValueToFieldType(fieldType, value);
                if (value.contains("*")) {
                    return builder.like(root.get(key), "%" + convertedValue.toString().replace("*", "") + "%");
                } else {
                    return builder.equal(root.get(key), convertedValue);
                }
            }
        } else if (condition.contains("!=")) {
            return getPredicate(builder, root, condition);
        }
        return null;
    }

    private <T> Predicate getPredicate(CriteriaBuilder builder, Root<T> root, String condition) {
       log.debug("Condition: {}", condition);
        String[] keyValue = condition.split("!=");
        String key = keyValue[0].trim();
        String value = keyValue[1].trim().replace("'", ""); // Remove single quotes

        if (key.contains(".")) {
            return createNestedPropertyPredicate(builder, root, key, value, false);
        } else {
            Class<?> fieldType = root.get(key).getJavaType();
            Object convertedValue = convertValueToFieldType(fieldType, value);
            if (value.contains("*")) {
                return builder.notLike(root.get(key), "%" + convertedValue.toString().replace("*", "") + "%");
            } else {
                return builder.notEqual(root.get(key), convertedValue);
            }
        }
    }


    /**
     * Creates a predicate for a nested property query.
     *
     * @param builder the CriteriaBuilder
     * @param root the root entity
     * @param key the nested property key
     * @param value the value to compare
     * @param isEqual true for equality, false for inequality
     * @param <T> the type of the entity
     * @return the created predicate
     */
    private <T> Predicate createNestedPropertyPredicate(CriteriaBuilder builder, Root<T> root, String key, String value, boolean isEqual) {
        String[] parts = key.split("\\."); // Split nested properties (e.g., "prompt.id")
        Path<Object> path = root.get(parts[0]); // Start with the root entity

        for (int i = 1; i < parts.length; i++) {
            path = path.get(parts[i]); // Navigate through nested properties
        }

        // Convert the value to the correct type
        Class<?> fieldType = path.getJavaType();
        Object convertedValue = convertValueToFieldType(fieldType, value);

        // Return the appropriate predicate
        return isEqual ? builder.equal(path, convertedValue) : builder.notEqual(path, convertedValue);
    }

    /**
     * Converts a string value to the specified field type.
     *
     * @param fieldType the field type
     * @param value the string value to convert
     * @return the converted value
     */
    private Object convertValueToFieldType(Class<?> fieldType, String value) {
        if (fieldType == Boolean.class || fieldType == boolean.class) {
            return Boolean.parseBoolean(value);
        } else if (fieldType == Integer.class || fieldType == int.class) {
            return Integer.parseInt(value);
        } else if (fieldType == Long.class || fieldType == long.class) {
            return Long.parseLong(value);
        } else if (fieldType == Double.class || fieldType == double.class) {
            return Double.parseDouble(value);
        } else if (fieldType == Float.class || fieldType == float.class) {
            return Float.parseFloat(value);
        } else if (fieldType == String.class) {
            return value;
        }
        // Handle other types as needed
        throw new IllegalArgumentException("Unsupported field type: " + fieldType);
    }

    /**
     * Applies ordering to the criteria query based on the specified order field and type.
     *
     * @param builder the CriteriaBuilder
     * @param criteria the CriteriaQuery
     * @param root the root entity
     * @param orderBy the field to order by
     * @param orderType the order type (asc or desc)
     * @param <T> the type of the entity
     */
    private <T> void applyOrdering(CriteriaBuilder builder, CriteriaQuery<T> criteria, Root<T> root, String orderBy, String orderType) {
        if (orderBy != null && orderType != null) {
            if ("desc".equalsIgnoreCase(orderType)) {
                criteria.orderBy(builder.desc(root.get(orderBy)));
            } else {
                criteria.orderBy(builder.asc(root.get(orderBy)));
            }
        }
    }

    /**
     * Applies pagination to the typed query based on the specified offset and size.
     *
     * @param query the TypedQuery
     * @param offset the starting position of the result set
     * @param size the maximum number of results to return
     */
    private void applyPagination(TypedQuery<?> query, Integer offset, Integer size) {
        if (offset != null && offset >= 0) {
            query.setFirstResult(offset);
        }
        if (size != null && size > 0) {
            query.setMaxResults(size);
        }
    }
}
