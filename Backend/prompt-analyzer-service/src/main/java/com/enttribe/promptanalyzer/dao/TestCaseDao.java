package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.TestCase;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for TestCase entity operations.
 * Provides methods to interact with the TestCase table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface TestCaseDao extends JpaRepository<TestCase, Integer> {

}
