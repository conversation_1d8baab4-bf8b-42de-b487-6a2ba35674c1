# Use a full-featured base image with glibc (required by DJL native libs)
FROM eclipse-temurin:21-jdk-jammy

RUN apt-get update && \
    apt-get install -y curl vim unzip fonts-dejavu && \
    rm -rf /var/lib/apt/lists/*


# Set environment variables
ENV LANG=en_US.UTF-8 \
    LANGUAGE=en_US:en \
    LC_ALL=en_US.UTF-8 \
    SSL_VAULT_PATH=/opt/visionwaves/sql_ssl \
    SERVICE_ARCHIVE=prompt-analyzer \
    SERVICE_PATH=/opt/visionwaves/prompt-analyzer \
    BASE_PATH=/opt/visionwaves/prompt-analyzer

# Create system group and user with specific UID and GID
RUN groupadd --system --gid 1001 visionwaves && \
    useradd --system --uid 1001 --gid 1001 --shell /bin/bash --home /home/<USER>
    mkdir -p /home/<USER>
    chown 1001:1001 /home/<USER>


# Create necessary directories
RUN mkdir -p "$BASE_PATH" "$SSL_VAULT_PATH" && \
    chown -R visionwaves:visionwaves "$BASE_PATH" "$SSL_VAULT_PATH"

# Copy and extract the service archive
COPY ./prompt-analyzer.tar $BASE_PATH/
RUN tar -xf "$BASE_PATH/$SERVICE_ARCHIVE.tar" -C "$BASE_PATH" && \
    rm "$BASE_PATH/$SERVICE_ARCHIVE.tar"

# Switch to non-root user
USER visionwaves

# Set working directory
WORKDIR $SERVICE_PATH

# Start the application
CMD ["bash", "run.sh", "start"]
