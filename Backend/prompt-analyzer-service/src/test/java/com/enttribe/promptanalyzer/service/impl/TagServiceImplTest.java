package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.TagDao;
import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Tag;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class TagServiceImplTest {

   @Mock
    private CustomFilter customFilter;

   @Mock
    private TagDao tagDao;

    @InjectMocks
    private TagServiceImpl tagServiceImpl;

    private TagRequestDto testTagRequestDto;
    private Tag testTag;

    @BeforeEach
    void setUp() {
        testTagRequestDto = new TagRequestDto();
        testTagRequestDto.setId(1);
        testTagRequestDto.setName("Test Tag");
        testTagRequestDto.setType("Test Type");

        testTag = new Tag();
        testTag.setId(1);
        testTag.setName("Test Tag");
        testTag.setType("Test Type");
    }

    @Test
    @DisplayName("Search tags success case")
    void tagSearchSuccess() {
        List<Tag> mockTags = Arrays.asList(testTag);
        when(customFilter.searchByFilter(eq(Tag.class), anyString(), anyString(), anyString(), anyInt(), anyInt()
        )).thenReturn(mockTags);
        List<TagResponseDto> result = tagServiceImpl.search("filter", 0, 10, "name", "ASC");
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(customFilter).searchByFilter(Tag.class, "filter", "name", "ASC", 0, 10);
    }

    @Test
    @DisplayName("Search tags failure case")
    void tagSearchFailure() {
        when(customFilter.searchByFilter(eq(Tag.class), anyString(), anyString(), anyString(), anyInt(), anyInt()
        )).thenThrow(new RuntimeException("Search failed"));
        BusinessException exception = assertThrows(BusinessException.class,
                () -> tagServiceImpl.search("filter", 0, 0, "orderBy", "orderType"));
        assertEquals("Error while searching tags: Search failed", exception.getMessage());
    }

    @Test
    @DisplayName("Count tags success case")
    void tagCountSuccess() {
        when(customFilter.countByFilter(eq(Tag.class), anyString())).thenReturn(1L);
        Long result = tagServiceImpl.count("filter");
        assertNotNull(result);
        assertEquals(1L, result);
    }

    @Test
    @DisplayName("Save tag success case")
    void tagSaveSuccess() {
        when(tagDao.save(any(Tag.class))).thenReturn(testTag);
        Map<String, String> result = tagServiceImpl.save(testTagRequestDto);
        assertNotNull(result);
        assertEquals(Map.of("status", "success"), result);
        verify(tagDao, times(1)).save(any(Tag.class));
    }

    @Test
    @DisplayName("Save tag failure case")
    void tagSaveFailure() {
        TagRequestDto requestDto = new TagRequestDto();
        when(tagDao.save(any(Tag.class))).thenThrow(new RuntimeException("Database error"));
        BusinessException exception = assertThrows(BusinessException.class,
                () -> tagServiceImpl.save(requestDto));
        assertEquals("Unable to save tag: Database error", exception.getMessage());
        verify(tagDao, times(1)).save(any(Tag.class));
    }

    @Test
    @DisplayName("Update tag success case")
    void tagUpdateSuccess() {
        when(tagDao.findById(anyInt())).thenReturn(Optional.of(testTag));
        when(tagDao.save(any(Tag.class))).thenReturn(testTag);
        Map<String, String> result = tagServiceImpl.update(testTagRequestDto);
        assertNotNull(result);
        assertEquals(Map.of("status", "success"), result);
        verify(tagDao, times(1)).findById(anyInt());
        verify(tagDao, times(1)).save(any(Tag.class));
    }

    @Test
    @DisplayName("Update non-existent tag failure case")
    void tagUpdateFailure() {
        TagRequestDto requestDto = new TagRequestDto();
        requestDto.setId(1);
        when(tagDao.findById(anyInt())).thenReturn(Optional.empty());
        BusinessException exception = assertThrows(BusinessException.class,
                () -> tagServiceImpl.update(requestDto));
        assertEquals("Unable to update Tag: Tag record not found for ID: 1", exception.getMessage());
        verify(tagDao, times(1)).findById(anyInt());
        verify(tagDao, never()).save(any(Tag.class));
    }

    @Test
    @DisplayName("Soft delete tag success case")
    void tagSoftDeleteSuccess() {
        when(tagDao.findById(anyInt())).thenReturn(Optional.of(testTag));
        when(tagDao.save(any(Tag.class))).thenReturn(testTag);
        Map<String, String> result = tagServiceImpl.softDelete(1);
        assertNotNull(result);
        assertEquals(Map.of("result", "success"), result);
        verify(tagDao, times(1)).findById(anyInt());
        verify(tagDao, times(1)).save(any(Tag.class));
    }

    @Test
    @DisplayName("Soft delete non-existent tag failure case")
    void tagSoftDeleteFailure() {
        int id = 1;
        when(tagDao.findById(anyInt())).thenReturn(Optional.empty());
        Map<String, String> result = tagServiceImpl.softDelete(id);
        assertNotNull(result);
        assertEquals(Map.of("result", "failed"), result);
        verify(tagDao, times(1)).findById(anyInt());
    }
}
