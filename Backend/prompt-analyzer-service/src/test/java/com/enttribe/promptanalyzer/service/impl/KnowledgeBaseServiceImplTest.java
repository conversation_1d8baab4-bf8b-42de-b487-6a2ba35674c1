package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.KnowledgeBaseDao;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.manager.InferenceManager;
import com.enttribe.promptanalyzer.util.ChunkCounter;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.dto.knowledge_base.DocumentRequestDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.VectorResponseDto;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseSdkDto;
import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.constants.PromptConstants;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.ArgumentCaptor;

import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class KnowledgeBaseServiceImplTest {

    @Mock
    private KnowledgeBaseDao knowledgeDao;

    @Mock
    private CustomFilter customFilter;

    @Mock
    private VectorStore vectorStore;

    @Mock
    private ApiService apiService;

    @Mock
    private InferenceManager inferenceManager;

    @Mock
    private S3Service s3Service;

    @Mock
    private ChunkCounter chunkCounter;

    @InjectMocks
    private KnowledgeBaseServiceImpl knowledgeBaseService;

    private KnowledgeBase mockKnowledgeBase;
    private DocumentRequestDto mockDocumentRequestDto;

    private String databaseName = "test_database";
    private String collectionName = "test_collection";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(knowledgeBaseService, "databaseName", databaseName);
        ReflectionTestUtils.setField(knowledgeBaseService, "collectionName", collectionName);

        mockKnowledgeBase = new KnowledgeBase();
        mockKnowledgeBase.setId(1);
        mockKnowledgeBase.setName("Test Knowledge Base");
        mockKnowledgeBase.setDeleted(false);
        mockKnowledgeBase.setDocId("[]");
        mockKnowledgeBase.setS3FileNames("[]");
        mockKnowledgeBase.setDocMetaData("{}");

        mockDocumentRequestDto = new DocumentRequestDto();
        mockDocumentRequestDto.setName("Test Document");
        mockDocumentRequestDto.setType("DOCUMENT");
        mockDocumentRequestDto.setFile(new ArrayList<>());
        mockDocumentRequestDto.setTags("tag1,tag2");
        mockDocumentRequestDto.setSimilarityThreshold(0.8);
        mockDocumentRequestDto.setTopK(5);
        mockDocumentRequestDto.setDeleted(false);
        mockDocumentRequestDto.setReturnDirect(false);
    }

    @Test
    @DisplayName("Get Knowledge Base By ID - Success")
    void getKnowledgeBaseByIdSuccess() {
        when(knowledgeDao.findById(1)).thenReturn(Optional.of(mockKnowledgeBase));

        KnowledgeBaseResponseDto result = knowledgeBaseService.getKnowledgBaseById(1);

        assertNotNull(result);
        assertEquals(mockKnowledgeBase.getName(), result.getName());
        verify(knowledgeDao, times(1)).findById(1);
    }

    @Test
    @DisplayName("Get Knowledge Base By ID - Not Found")
    void getKnowledgeBaseByIdNotFound() {
        when(knowledgeDao.findById(anyInt())).thenReturn(Optional.empty());

        assertThrows(BusinessException.class, () -> knowledgeBaseService.getKnowledgBaseById(99));

        verify(knowledgeDao, times(1)).findById(99);
    }

    @Test
    @DisplayName("Soft Delete Knowledge Base - Success")
    void softDeleteSuccess() {
        when(knowledgeDao.findById(1)).thenReturn(Optional.of(mockKnowledgeBase));
        when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);

        Map<String, String> result = knowledgeBaseService.softDelete(1);

        assertNotNull(result);
        assertEquals("success", result.get(APIConstants.RESULT));
        assertTrue(mockKnowledgeBase.getDeleted());
        verify(knowledgeDao, times(1)).findById(1);
        verify(knowledgeDao, times(1)).save(mockKnowledgeBase);
    }

    @Test
    @DisplayName("Soft Delete Knowledge Base - Not Found")
    void softDeleteNotFound() {
        when(knowledgeDao.findById(anyInt())).thenReturn(Optional.empty());

        Map<String, String> result = knowledgeBaseService.softDelete(99);

        assertNotNull(result);
        assertEquals("failed", result.get(APIConstants.RESULT));
        verify(knowledgeDao, times(1)).findById(99);
        verify(knowledgeDao, never()).save(any(KnowledgeBase.class));
    }

    @Test
    @DisplayName("Count Knowledge Bases - Success")
    void countKnowledgeBasesSuccess() {
        String filter = "someFilter";
        when(customFilter.countByFilter(eq(KnowledgeBase.class), eq(filter))).thenReturn(10L);

        Long result = knowledgeBaseService.count(filter);

        assertEquals(10L, result);
        verify(customFilter, times(1)).countByFilter(eq(KnowledgeBase.class), eq(filter));
    }

    @Test
    @DisplayName("Search Knowledge Bases - Success")
    void searchKnowledgeBasesSuccess() {
        String filter = "someFilter";
        List<KnowledgeBase> mockList = new ArrayList<>();
        mockList.add(mockKnowledgeBase);

        when(customFilter.searchByFilter(
            eq(KnowledgeBase.class), eq(filter), anyString(), anyString(), anyInt(), anyInt()
        )).thenReturn(mockList);

        List<KnowledgeBaseResponseDto> result = knowledgeBaseService.search(filter, 0, 10, "name", "asc");

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(mockKnowledgeBase.getName(), result.get(0).getName());
        verify(customFilter, times(1)).searchByFilter(
            eq(KnowledgeBase.class), eq(filter), eq("name"), eq("asc"), eq(0), eq(10)
        );
    }

    @Test
    @DisplayName("Exists Website URL - Exists")
    void existsWebsiteUrlExists() {
        List<KnowledgeBase> mockList = new ArrayList<>();
        mockList.add(mockKnowledgeBase);
        when(knowledgeDao.existsWebsiteUrl(anyString())).thenReturn(mockList);

        Map<String, Object> result = knowledgeBaseService.existsWebsiteUrl("http://test.com");

        assertNotNull(result);
        assertTrue((Boolean) result.get("exists"));
        assertTrue(result.containsKey("ids"));
        verify(knowledgeDao, times(1)).existsWebsiteUrl("http://test.com");
    }

    @Test
    @DisplayName("Exists Website URL - Does Not Exist")
    void existsWebsiteUrlDoesNotExist() {
        when(knowledgeDao.existsWebsiteUrl(anyString())).thenReturn(new ArrayList<>());

        Map<String, Object> result = knowledgeBaseService.existsWebsiteUrl("http://test.com");

        assertNotNull(result);
        assertFalse((Boolean) result.get("exists"));
        assertFalse(result.containsKey("ids"));
        verify(knowledgeDao, times(1)).existsWebsiteUrl("http://test.com");
    }

    @Test
    @DisplayName("Exists Knowledge Base Name - Exists")
    void existsKnowledgeBaseNameExists() {
        when(knowledgeDao.findByName(anyString())).thenReturn(mockKnowledgeBase);

        Map<String, Boolean> result = knowledgeBaseService.existsKnowledgeBaseName("Test Name");

        assertNotNull(result);
        assertTrue(result.get(PromptConstants.RESULT));
        verify(knowledgeDao, times(1)).findByName("Test Name");
    }

    @Test
    @DisplayName("Exists Knowledge Base Name - Does Not Exist")
    void existsKnowledgeBaseNameDoesNotExist() {
        when(knowledgeDao.findByName(anyString())).thenReturn(null);

        Map<String, Boolean> result = knowledgeBaseService.existsKnowledgeBaseName("Non Existent Name");

        assertNotNull(result);
        assertFalse(result.get(PromptConstants.RESULT));
        verify(knowledgeDao, times(1)).findByName("Non Existent Name");
    }

//    @Test
//    @DisplayName("Save Document - Type DOCUMENT Success")
//    void saveDocumentTypeDocumentSuccess() throws IOException {
//        mockDocumentRequestDto.setType("DOCUMENT");
//        MockMultipartFile mockFile = new MockMultipartFile(
//                "file", "test.pdf", MediaType.APPLICATION_PDF_VALUE, "PDF content".getBytes()
//        );
//        mockDocumentRequestDto.setFile(List.of(mockFile));
//
//        VectorResponseDto mockVectorResponse = new VectorResponseDto(UUID.randomUUID().toString(), Map.of("key", "value"), List.of("id1", "id2"));
//
//        KnowledgeBase savedKnowledgeBase = new KnowledgeBase();
//         when(knowledgeDao.save(any(KnowledgeBase.class))).thenAnswer(invocation -> {
//            KnowledgeBase kb = invocation.getArgument(0);
//            kb.setFilter(mockVectorResponse.getFilter());
//            kb.setDocMetaData(JsonUtils.convertToJSON(mockVectorResponse.getMetadata()));
//            kb.setDocId(JsonUtils.convertToJSON(mockVectorResponse.getDocIds()));
//             kb.setS3FileNames(JsonUtils.convertToJSON(List.of("s3_filename")));
//             kb.setId(1);
//            return kb;
//        });
//
//        Map<String, String> result = knowledgeBaseService.saveDocument(mockDocumentRequestDto);
//
//        assertNotNull(result);
//        assertEquals(PromptConstants.SUCCESS, result.get(PromptConstants.RESULT));
//        assertEquals("1", result.get("id"));
//
//        verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
//    }

    @Test
    @DisplayName("Save Document - Type SQL Success")
    void saveDocumentTypeSqlSuccess() {
        mockDocumentRequestDto.setType("SQL");
        mockDocumentRequestDto.setIntegration("test_integration");
        mockDocumentRequestDto.setTables(List.of("table1", "table2"));

        when(apiService.getTableSchema(anyString(), anyString())).thenReturn("table schema");
        when(knowledgeDao.save(any(KnowledgeBase.class))).thenAnswer(invocation -> {
            KnowledgeBase kb = invocation.getArgument(0);
            kb.setId(1);
            return kb;
        });
        doNothing().when(vectorStore).accept(anyList());

        Map<String, String> result = knowledgeBaseService.saveDocument(mockDocumentRequestDto);

        assertNotNull(result);
        assertEquals(PromptConstants.SUCCESS, result.get(PromptConstants.RESULT));
        assertEquals("1", result.get("id"));

        verify(apiService, times(mockDocumentRequestDto.getTables().size())).getTableSchema(eq("test_integration"), anyString());
        verify(vectorStore, times(mockDocumentRequestDto.getTables().size())).accept(anyList());
        verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
    }

    @Test
    @DisplayName("Save Document - Type WEBSITE Success")
    void saveDocumentTypeWebsiteSuccess() {
        mockDocumentRequestDto.setType("WEBSITE");
        mockDocumentRequestDto.setWebsiteUrl("http://testwebsite.com");

        when(apiService.triggerCrawl(anyString())).thenReturn("mockTaskId");
         when(knowledgeDao.save(any(KnowledgeBase.class))).thenAnswer(invocation -> {
            KnowledgeBase kb = invocation.getArgument(0);
            kb.setId(1);
            return kb;
        });

        Map<String, String> result = knowledgeBaseService.saveDocument(mockDocumentRequestDto);

        assertNotNull(result);
        assertEquals(PromptConstants.SUCCESS, result.get(PromptConstants.RESULT));
        assertEquals("1", result.get("id"));

        verify(apiService, times(1)).triggerCrawl(eq("http://testwebsite.com"));
        verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
    }

    @Test
    @DisplayName("Save Document - Type COLLECTION Success")
    void saveDocumentTypeCollectionSuccess() {
        mockDocumentRequestDto.setType("COLLECTION");
        mockDocumentRequestDto.setCollectionName("test_collection_name");

         when(knowledgeDao.save(any(KnowledgeBase.class))).thenAnswer(invocation -> {
            KnowledgeBase kb = invocation.getArgument(0);
            kb.setId(1);
            return kb;
        });

        Map<String, String> result = knowledgeBaseService.saveDocument(mockDocumentRequestDto);

        assertNotNull(result);
        assertEquals(PromptConstants.SUCCESS, result.get(PromptConstants.RESULT));
        assertEquals("1", result.get("id"));

        verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
    }

//        @Test
//        @DisplayName("Save Document - Unsupported Type")
//        void saveDocumentUnsupportedType() {
//            mockDocumentRequestDto.setType("UNSUPPORTED");
//
//            assertThrows(UnsupportedOperationException.class, () -> knowledgeBaseService.saveDocument(mockDocumentRequestDto));
//            verify(knowledgeDao, never()).save(any(KnowledgeBase.class));
//        }

//        @Test
//        @DisplayName("Save Document - Business Exception during processing")
//        void saveDocumentBusinessException() throws IOException {
//             mockDocumentRequestDto.setType("DOCUMENT");
//            MockMultipartFile mockFile = new MockMultipartFile(
//                    "file", "test.pdf", MediaType.APPLICATION_PDF_VALUE, "PDF content".getBytes()
//            );
//            mockDocumentRequestDto.setFile(List.of(mockFile));
//
//             when(knowledgeDao.save(any(KnowledgeBase.class))).thenThrow(new RuntimeException("DB Error"));
//
//            mockDocumentRequestDto.setType("COLLECTION");
//
//            assertThrows(BusinessException.class, () -> knowledgeBaseService.saveDocument(mockDocumentRequestDto));
//
//             verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
//        }

    @Test
    @DisplayName("Update Document - Knowledge Base Not Found")
    void updateDocumentNotFound() {
        when(knowledgeDao.findById(anyInt())).thenReturn(Optional.empty());

        assertThrows(BusinessException.class, () -> knowledgeBaseService.updateDocument(99, mockDocumentRequestDto));

        verify(knowledgeDao, times(1)).findById(99);
        verify(knowledgeDao, never()).save(any(KnowledgeBase.class));
    }

    @Test
    @DisplayName("Update Document - Type SQL Success")
    void updateDocumentTypeSqlSuccess() throws JsonProcessingException {
        mockKnowledgeBase.setDocId(JsonUtils.convertToJSON(List.of("old_sql_id")));
        when(knowledgeDao.findById(1)).thenReturn(Optional.of(mockKnowledgeBase));

        mockDocumentRequestDto.setType("SQL");
        mockDocumentRequestDto.setIntegration("new_test_integration");
        mockDocumentRequestDto.setTables(List.of("new_table1"));

        when(apiService.getTableSchema(anyString(), anyString())).thenReturn("new table schema");
        when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);
         doNothing().when(vectorStore).accept(anyList());
        doNothing().when(vectorStore).delete(anyString());
        doNothing().when(vectorStore).delete(anyList());

        Map<String, String> result = knowledgeBaseService.updateDocument(1, mockDocumentRequestDto);

        assertNotNull(result);
        assertEquals(PromptConstants.SUCCESS, result.get(PromptConstants.RESULT));

        verify(knowledgeDao, times(1)).findById(1);
        verify(apiService, times(mockDocumentRequestDto.getTables().size())).getTableSchema(eq("new_test_integration"), anyString());
        verify(vectorStore, times(mockDocumentRequestDto.getTables().size())).accept(anyList());
        verify(vectorStore, times(1)).delete(eq("'doc_id' == 'null'"));
        verify(vectorStore, times(1)).delete(eq(List.of("old_sql_id")));
        verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
    }

    @Test
    @DisplayName("Update Document - Type WEBSITE Success")
    void updateDocumentTypeWebsiteSuccess() throws JsonProcessingException {
        mockKnowledgeBase.setDocId(JsonUtils.convertToJSON(List.of("old_website_id")));
        when(knowledgeDao.findById(1)).thenReturn(Optional.of(mockKnowledgeBase));

        mockDocumentRequestDto.setType("WEBSITE");
        mockDocumentRequestDto.setWebsiteUrl("http://newwebsite.com");

        when(apiService.triggerCrawl(anyString())).thenReturn("newMockTaskId");
        when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);
        doNothing().when(vectorStore).delete(anyString());
        doNothing().when(vectorStore).delete(anyList());

        Map<String, String> result = knowledgeBaseService.updateDocument(1, mockDocumentRequestDto);

        assertNotNull(result);
        assertEquals(PromptConstants.SUCCESS, result.get(PromptConstants.RESULT));

        verify(knowledgeDao, times(1)).findById(1);
        verify(apiService, times(1)).triggerCrawl(eq("http://newwebsite.com"));
        verify(vectorStore, times(1)).delete(eq("'doc_id' == 'null'"));
         verify(vectorStore, times(1)).delete(eq(List.of("old_website_id")));
        verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
    }

    @Test
    @DisplayName("Update Document - Type COLLECTION Success")
    void updateDocumentTypeCollectionSuccess() throws JsonProcessingException {
        mockKnowledgeBase.setDocId(JsonUtils.convertToJSON(List.of("old_collection_id")));
         when(knowledgeDao.findById(1)).thenReturn(Optional.of(mockKnowledgeBase));

        mockDocumentRequestDto.setType("COLLECTION");
        mockDocumentRequestDto.setCollectionName("new_test_collection_name");

        when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);
        doNothing().when(vectorStore).delete(anyString());
        doNothing().when(vectorStore).delete(anyList());

        Map<String, String> result = knowledgeBaseService.updateDocument(1, mockDocumentRequestDto);

        assertNotNull(result);
        assertEquals(PromptConstants.SUCCESS, result.get(PromptConstants.RESULT));

        verify(knowledgeDao, times(1)).findById(1);
        verify(vectorStore, times(1)).delete(eq("'doc_id' == 'null'"));
         verify(vectorStore, times(1)).delete(eq(List.of("old_collection_id")));
        verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
    }

//    @Test
//    @DisplayName("Update Document - Unsupported Type")
//    void updateDocumentUnsupportedType() {
//        when(knowledgeDao.findById(1)).thenReturn(Optional.of(mockKnowledgeBase));
//        mockDocumentRequestDto.setType("UNSUPPORTED_UPDATE");
//
//        assertThrows(UnsupportedOperationException.class, () -> knowledgeBaseService.updateDocument(1, mockDocumentRequestDto));
//        verify(knowledgeDao, times(1)).findById(1);
//        verify(knowledgeDao, never()).save(any(KnowledgeBase.class));
//    }

//    @Test
//    @DisplayName("Update Document - Business Exception during S3 upload")
//    void updateDocumentBusinessExceptionDuringS3Upload() throws IOException {
//        // Arrange
//        mockKnowledgeBase.setDocId(JsonUtils.convertToJSON(List.of("old_id1", "old_id2")));
//        mockKnowledgeBase.setFilter("old_filter");
//        when(knowledgeDao.findById(1)).thenReturn(Optional.of(mockKnowledgeBase));
//
//        mockDocumentRequestDto.setType("DOCUMENT");
//        MockMultipartFile mockFile = new MockMultipartFile(
//                "file", "test.pdf", MediaType.APPLICATION_PDF_VALUE, "PDF content".getBytes()
//        );
//        mockDocumentRequestDto.setFile(List.of(mockFile));
//
//        // Mock the S3 upload to throw an IOException, which should be wrapped in a BusinessException
//        // Need to mock the specific upload method signature used in KnowledgeBaseServiceImpl
//        // Assuming the signature is something like s3Service.uploadFile(MultipartFile file) or similar
//        // Adjust the mock below based on the actual method signature in S3Service
//        doThrow(new BusinessException("Simulated S3 upload failed")).when(s3Service).uploadFileToS3(anyList(), anyString(), anyString());
//
//
//        // Act and Assert
//        assertThrows(BusinessException.class, () -> knowledgeBaseService.updateDocument(1, mockDocumentRequestDto));
//
//        // Verify
//        verify(knowledgeDao, times(1)).findById(1);
//        // Verify that S3 upload was attempted
//         verify(s3Service, times(1)).uploadFileToS3(anyList(), anyString(), anyString());
//        // Verify that save was NEVER called, as the exception happened before saving
//         verify(knowledgeDao, never()).save(any(KnowledgeBase.class));
//         // Also verify vectorStore.delete is called if that happens before S3 upload
//         // If vectorStore.delete happens after S3 upload failure, this verification should be times(0) or never()
//         // Let's assume delete happens before upload for now
//         verify(vectorStore, times(1)).delete(eq("'doc_id' == 'old_filter'"));
//         verify(vectorStore, times(1)).delete(eq(List.of("old_id1", "old_id2")));
//
//    }

    @Test
    @DisplayName("Get Knowledge Base By IDs - Success")
    void getKnowledgeBaseByIdsSuccess() {
        List<Integer> ids = List.of(1, 2);
        KnowledgeBase kb2 = new KnowledgeBase();
        kb2.setId(2);
        kb2.setName("KB Two");
        List<KnowledgeBase> mockList = List.of(mockKnowledgeBase, kb2);

        when(knowledgeDao.findAllById(eq(ids))).thenReturn(mockList);

        List<KnowledgeBaseSdkDto> result = knowledgeBaseService.getKnowledgeBaseByIds(ids);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Test Knowledge Base", result.get(0).getName());
        assertEquals("KB Two", result.get(1).getName());
        verify(knowledgeDao, times(1)).findAllById(eq(ids));
    }

    @Test
    @DisplayName("Update Tag By ID - Success")
    void updateTagByIdSuccess() {
        Map<String, String> tags = Map.of("tags", "newtag1,newtag2");
        when(knowledgeDao.findById(1)).thenReturn(Optional.of(mockKnowledgeBase));
        when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);

        Map<String, String> result = knowledgeBaseService.updateTagById(1, tags);

        assertNotNull(result);
        assertEquals(APIConstants.SUCCESS, result.get(APIConstants.RESULT));
        assertEquals("newtag1,newtag2", mockKnowledgeBase.getTag());
        verify(knowledgeDao, times(1)).findById(1);
        verify(knowledgeDao, times(1)).save(mockKnowledgeBase);
    }

    @Test
    @DisplayName("Update Tag By ID - Knowledge Base Not Found")
    void updateTagByIdNotFound() {
        Map<String, String> tags = Map.of("tags", "newtag1,newtag2");
        when(knowledgeDao.findById(anyInt())).thenReturn(Optional.empty());

         assertThrows(BusinessException.class, () -> knowledgeBaseService.updateTagById(99, tags));

        verify(knowledgeDao, times(1)).findById(99);
        verify(knowledgeDao, never()).save(any(KnowledgeBase.class));
    }

    @Test
    @DisplayName("Save Content - Success")
    void saveContentSuccess() throws JsonProcessingException {
        mockDocumentRequestDto.setContent("Some raw text content");
        mockDocumentRequestDto.setType("CONTENT");

        VectorResponseDto mockVectorResponse = new VectorResponseDto("contentFilter", Map.of("key", "value"), List.of("content_id"));
        
        when(knowledgeDao.save(any(KnowledgeBase.class))).thenAnswer(invocation -> {
            KnowledgeBase kb = invocation.getArgument(0);
            kb.setFilter(mockVectorResponse.getFilter());
            kb.setDocMetaData(JsonUtils.convertToJSON(mockVectorResponse.getMetadata()));
            kb.setDocId(JsonUtils.convertToJSON(mockVectorResponse.getDocIds()));
             kb.setId(1);
            return kb;
        });

        Map<String, String> result = knowledgeBaseService.saveContent(mockDocumentRequestDto);

        assertNotNull(result);
        assertEquals(PromptConstants.SUCCESS, result.get(PromptConstants.RESULT));
        assertEquals("1", result.get("id"));

        verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
        ArgumentCaptor<KnowledgeBase> kbCaptor = ArgumentCaptor.forClass(KnowledgeBase.class);
        verify(knowledgeDao).save(kbCaptor.capture());
        KnowledgeBase savedKb = kbCaptor.getValue();
        assertEquals(mockVectorResponse.getFilter(), savedKb.getFilter());
        assertEquals(JsonUtils.convertToJSON(mockVectorResponse.getMetadata()), savedKb.getDocMetaData());
        assertEquals(JsonUtils.convertToJSON(mockVectorResponse.getDocIds()), savedKb.getDocId());
    }

    @Test
    @DisplayName("Milvus Import Csv - Success")
    void milvusImportCsvSuccess() throws IOException {
        String csvContent = "header1,header2,content\nvalue1,value2,content1\nvalue3,value4,content2";
        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "test.csv", "text/csv", csvContent.getBytes()
        );

        doNothing().when(vectorStore).delete(anyString());
        doNothing().when(vectorStore).accept(anyList());

        ResponseEntity<String> result = knowledgeBaseService.milvusImportCsv(mockFile);

        assertNotNull(result);
        assertEquals(200, result.getStatusCodeValue());
        assertEquals("CSV data imported successfully.", result.getBody());
        verify(vectorStore, times(1)).delete(eq("'custom_agent_id' == 'f0b90d7d-c386-4b18-a1c5-9cf99b497324-abc'"));
        verify(vectorStore, times(1)).accept(anyList());
    }

    @Test
    @DisplayName("Milvus Import Csv - Empty File")
    void milvusImportCsvEmptyFile() throws IOException {
         String csvContent = "";
        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "test.csv", "text/csv", csvContent.getBytes()
        );

         ResponseEntity<String> result = knowledgeBaseService.milvusImportCsv(mockFile);

        assertNotNull(result);
        assertEquals(400, result.getStatusCodeValue());
        assertEquals("CSV file is empty.", result.getBody());
        verify(vectorStore, never()).delete(anyString());
        verify(vectorStore, never()).accept(anyList());
    }

    @Test
    @DisplayName("Milvus Import Csv - No Content Column")
    void milvusImportCsvNoContentColumn() throws IOException {
        String csvContent = "header1,header2\nvalue1,value2";
        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "test.csv", "text/csv", csvContent.getBytes()
        );

         ResponseEntity<String> result = knowledgeBaseService.milvusImportCsv(mockFile);

        assertNotNull(result);
        assertEquals(400, result.getStatusCodeValue());
        assertEquals("CSV file does not contain 'content' column.", result.getBody());
        verify(vectorStore, never()).delete(anyString());
        verify(vectorStore, never()).accept(anyList());
    }

    @Test
    @DisplayName("Milvus Import Csv - No Valid Content Rows")
    void milvusImportCsvNoValidContentRows() throws IOException {
        String csvContent = "header1,header2,content\nvalue1,value2,";
        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "test.csv", "text/csv", csvContent.getBytes()
        );

         ResponseEntity<String> result = knowledgeBaseService.milvusImportCsv(mockFile);

        assertNotNull(result);
        assertEquals(400, result.getStatusCodeValue());
        assertEquals("No valid content found in CSV.", result.getBody());
        verify(vectorStore, never()).delete(anyString());
        verify(vectorStore, never()).accept(anyList());
    }

    @Test
    @DisplayName("Update Return Direct - Success")
    void updateReturnDirectSuccess() {
        when(knowledgeDao.findById(1)).thenReturn(Optional.of(mockKnowledgeBase));
        when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);

        Map<String, String> result = knowledgeBaseService.updateReturnDirect(1, true);

        assertNotNull(result);
        assertEquals(APIConstants.SUCCESS, result.get(APIConstants.RESULT));
        assertTrue(mockKnowledgeBase.getReturnDirect());
        verify(knowledgeDao, times(1)).findById(1);
        verify(knowledgeDao, times(1)).save(mockKnowledgeBase);
    }

    @Test
    @DisplayName("Update Return Direct - Knowledge Base Not Found")
    void updateReturnDirectNotFound() {
        when(knowledgeDao.findById(anyInt())).thenReturn(Optional.empty());

        Map<String, String> result = knowledgeBaseService.updateReturnDirect(99, true);

        assertNotNull(result);
        assertEquals(APIConstants.FAILED, result.get(APIConstants.RESULT));
        verify(knowledgeDao, times(1)).findById(99);
        verify(knowledgeDao, never()).save(any(KnowledgeBase.class));
    }

    @Test
    @DisplayName("Save WebSite - Success")
    void saveWebSiteSuccess() throws JsonProcessingException {
         mockDocumentRequestDto.setType("WEBSITE");
        mockDocumentRequestDto.setWebsiteUrl("http://testwebsite.com/save");

         when(apiService.triggerCrawl(anyString())).thenReturn("mockTaskId");
         when(knowledgeDao.save(any(KnowledgeBase.class))).thenAnswer(invocation -> {
            KnowledgeBase kb = invocation.getArgument(0);
            kb.setId(1);
            return kb;
        });

        Map<String, String> result = knowledgeBaseService.saveWebSite(mockDocumentRequestDto);

        assertNotNull(result);
        assertEquals(PromptConstants.SUCCESS, result.get(PromptConstants.RESULT));
        assertEquals("1", result.get("id"));

        verify(apiService, times(1)).triggerCrawl(eq("http://testwebsite.com/save"));
        verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
    }

    @Test
    @DisplayName("Get Tables Name - Success")
    void getTablesNameSuccess() {
        List<String> mockTableNames = List.of("table1", "table2");
        when(apiService.getTablesName(anyString())).thenReturn(mockTableNames);

        List<String> result = knowledgeBaseService.getTablesName("test_integration");

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("table1", result.get(0));
        assertEquals("table2", result.get(1));
        verify(apiService, times(1)).getTablesName(eq("test_integration"));
    }
}