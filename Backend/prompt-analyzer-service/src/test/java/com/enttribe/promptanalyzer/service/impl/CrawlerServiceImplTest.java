package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.KnowledgeBaseDao;
import com.enttribe.promptanalyzer.dto.crawl.CrawlResponse;
import com.enttribe.promptanalyzer.dto.knowledge_base.VectorResponseDto;
import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.service.KnowledgeBaseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CrawlerServiceImplTest {

   @Mock
    private ApiService apiService;

   @Mock
    private KnowledgeBaseDao knowledgeBaseDao;

   @Mock
    private KnowledgeBaseService knowledgeBaseService;

    @InjectMocks
    private CrawlerServiceImpl crawlerService;

    private KnowledgeBase testKnowledgeBase;

    @BeforeEach
    void setUp() {
        testKnowledgeBase = new KnowledgeBase();
        testKnowledgeBase.setId(1);
        testKnowledgeBase.setWebSiteUrl("https://test.com");
        testKnowledgeBase.setWebsiteTaskId("task-123");
    }

    @Test
    @DisplayName("process knowledge base success case")
    void processWebSiteKbSuccess() {
        CrawlResponse successResponse = new CrawlResponse();
        successResponse.setStatus("completed");
        successResponse.setResult(new CrawlResponse.Result("Test markdown content"));

        when(knowledgeBaseDao.getWebsiteTypeUnprocessedKB()).thenReturn(List.of(testKnowledgeBase));
        when(apiService.getTaskStatus("task-123")).thenReturn(successResponse);
        when(knowledgeBaseService.saveInVector(List.of("Test markdown content")))
                .thenReturn(new VectorResponseDto("test-filter", Map.of(), List.of("doc-123")));

        crawlerService.processWebSiteKB();

        verify(apiService).getTaskStatus("task-123");
        verify(knowledgeBaseService).saveInVector(List.of("Test markdown content"));
        verify(knowledgeBaseDao).save(any(KnowledgeBase.class));
    }

    @Test
    @DisplayName("process knowledge base failure case")
    void processWebSiteKbFailure() {
        CrawlResponse errorResponse = new CrawlResponse();
        errorResponse.setError("API error occurred");

        when(knowledgeBaseDao.getWebsiteTypeUnprocessedKB()).thenReturn(List.of(testKnowledgeBase));
        when(apiService.getTaskStatus("task-123")).thenReturn(errorResponse);
        when(apiService.triggerCrawl("https://test.com")).thenReturn("new-task-456");

        doThrow(new RuntimeException("Database error")).when(knowledgeBaseDao).save(any(KnowledgeBase.class));

        crawlerService.processWebSiteKB();

        verify(apiService).triggerCrawl("https://test.com");
        verify(knowledgeBaseDao).save(any(KnowledgeBase.class));
    }
}
