package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.ProcessorService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.security.test.context.support.WithMockUser;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static java.util.Collections.singletonList;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.hamcrest.Matchers.hasSize;

@WebMvcTest(ProcessorRestImpl.class)
class ProcessorRestImplTest {

    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_ERROR = "error";
    private static final String MESSAGE_SUCCESS = "Operation completed successfully";
    private static final String MESSAGE_ERROR = "Invalid request parameters";
    private static final String FILTER_CATEGORY_TEST = "category:test";
    private static final String JSON_PATH_STATUS = "$.status";
    private static final String JSON_PATH_MESSAGE = "$.message";
    private static final Integer TEST_PROCESSOR_ID = 1;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockitoBean
    private ProcessorService processorService;

    private ProcessorRequestDto requestDto;
    private ProcessorResponseDto responseDto;
    private List<ProcessorResponseDto> processorList;
    private Map<String, Object> successResponse;
    private Map<String, Object> errorResponse;

    @BeforeEach
    void setUp() {
        requestDto = createProcessorRequest();
        responseDto = createProcessorResponse();

        // Create a list of processors for search tests
        ProcessorResponseDto processor2 = ProcessorResponseDto.builder()
                .id(2)
                .key("test-key-2")
                .displayName("Test Processor 2")
                .icon("test-icon-2.png")
                .styleType("secondary")
                .category("test-category")
                .subCategory("test-subcategory-2")
                .jsonStructure("{\"type\":\"secondary\"}")
                .build();

        processorList = List.of(responseDto, processor2);

        successResponse = Map.of("status", STATUS_SUCCESS, "message", MESSAGE_SUCCESS);
        errorResponse = Map.of("status", STATUS_ERROR, "message", MESSAGE_ERROR);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROCESSOR_WRITE"})
    @DisplayName("Save Processor - Success")
    void saveProcessorSuccess() throws Exception {
        // Arrange
        when(processorService.save(any(ProcessorRequestDto.class))).thenReturn(successResponse);

        // Act & Assert
        mockMvc.perform(post("/processor/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_PATH_STATUS).value(STATUS_SUCCESS))
                .andExpect(jsonPath(JSON_PATH_MESSAGE).value(MESSAGE_SUCCESS))
                .andDo(print());

        verify(processorService).save(any(ProcessorRequestDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROCESSOR_WRITE"})
    @DisplayName("Save Processor - Validation Error")
    void saveProcessorValidationError() throws Exception {
        // Arrange
        when(processorService.save(any(ProcessorRequestDto.class)))
                .thenThrow(new BusinessException("Validation failed"));

        // Act & Assert
        mockMvc.perform(post("/processor/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto))
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(processorService).save(any(ProcessorRequestDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROCESSOR_WRITE"})
    @DisplayName("Update Processor - Success")
    void updateProcessorSuccess() throws Exception {
        // Arrange
        Integer id = TEST_PROCESSOR_ID;
        when(processorService.update(eq(id), any(ProcessorRequestDto.class))).thenReturn(successResponse);

        // Act & Assert
        mockMvc.perform(post("/processor/update/{id}", id)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_PATH_STATUS).value(STATUS_SUCCESS))
                .andExpect(jsonPath(JSON_PATH_MESSAGE).value(MESSAGE_SUCCESS))
                .andDo(print());

        verify(processorService).update(eq(id), any(ProcessorRequestDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROCESSOR_WRITE"})
    @DisplayName("Update Processor - Not Found")
    void updateProcessorNotFound() throws Exception {
        // Arrange
        Integer id = 999;
        when(processorService.update(eq(id), any(ProcessorRequestDto.class)))
                .thenThrow(new BusinessException("Processor not found"));

        // Act & Assert
        mockMvc.perform(post("/processor/update/{id}", id)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto))
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(processorService).update(eq(id), any(ProcessorRequestDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROCESSOR_READ"})
    @DisplayName("Search Processors - Success")
    void searchProcessorsSuccess() throws Exception {
        // Arrange
        when(processorService.search(FILTER_CATEGORY_TEST, 0, 10, "displayName", "asc"))
                .thenReturn(processorList);

        // Act & Assert
        mockMvc.perform(get("/processor/search")
                        .param("filter", FILTER_CATEGORY_TEST)
                        .param("offset", "0")
                        .param("size", "10")
                        .param("orderBy", "displayName")
                        .param("orderType", "asc"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].displayName").value("Test Processor"))
                .andExpect(jsonPath("$[1].id").value(2))
                .andExpect(jsonPath("$[1].displayName").value("Test Processor 2"))
                .andDo(print());

        verify(processorService).search(FILTER_CATEGORY_TEST, 0, 10, "displayName", "asc");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROCESSOR_READ"})
    @DisplayName("Search Processors - Empty Results")
    void searchProcessorsEmpty() throws Exception {
        // Arrange
        when(processorService.search(anyString(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        mockMvc.perform(get("/processor/search")
                        .param("filter", "nonexistent")
                        .param("offset", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(0)))
                .andDo(print());

        verify(processorService).search("nonexistent", 0, 10, null, null);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROCESSOR_READ"})
    @DisplayName("Count Processors - Success")
    void countProcessorsSuccess() throws Exception {
        // Arrange
        when(processorService.count(FILTER_CATEGORY_TEST)).thenReturn(5L);

        // Act & Assert
        mockMvc.perform(get("/processor/count").param("filter", FILTER_CATEGORY_TEST))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(5))
                .andDo(print());

        verify(processorService).count(FILTER_CATEGORY_TEST);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROCESSOR_READ"})
    @DisplayName("Count Processors - No Filter")
    void countProcessorsNoFilter() throws Exception {
        // Arrange
        when(processorService.count(null)).thenReturn(10L);

        // Act & Assert
        mockMvc.perform(get("/processor/count"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(10));

        verify(processorService).count(null);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROCESSOR_WRITE"})
    @DisplayName("Save Processor - Invalid Request")
    void saveProcessorInvalidRequest() throws Exception {
        // Arrange
        when(processorService.save(any(ProcessorRequestDto.class))).thenReturn(errorResponse);

        // Act & Assert
        mockMvc.perform(post("/processor/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(new ProcessorRequestDto()))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_PATH_STATUS).value(STATUS_ERROR))
                .andExpect(jsonPath(JSON_PATH_MESSAGE).value(MESSAGE_ERROR))
                .andDo(print());

        verify(processorService).save(any(ProcessorRequestDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROCESSOR_READ"})
    @DisplayName("Count Processors - Service Error")
    void countProcessorsServiceError() throws Exception {
        // Arrange
        when(processorService.count(anyString()))
                .thenThrow(new BusinessException("Count service unavailable"));

        // Act & Assert
        mockMvc.perform(get("/processor/count")
                        .param("filter", "test"))
                .andExpect(status().isBadRequest());

        verify(processorService).count("test");
    }

    private ProcessorRequestDto createProcessorRequest() {
        ProcessorRequestDto dto = new ProcessorRequestDto();
        dto.setKey("test-key");
        dto.setDisplayName("Test Processor");
        dto.setIcon("test-icon.png");
        dto.setStyleType("default");
        dto.setCategory("test-category");
        dto.setSubCategory("test-subcategory");
        return dto;
    }

    private ProcessorResponseDto createProcessorResponse() {
        return ProcessorResponseDto.builder()
                .id(1)
                .key("test-key")
                .displayName("Test Processor")
                .icon("test-icon.png")
                .styleType("default")
                .category("test-category")
                .subCategory("test-subcategory")
                .jsonStructure("{}")
                .build();
    }
}
