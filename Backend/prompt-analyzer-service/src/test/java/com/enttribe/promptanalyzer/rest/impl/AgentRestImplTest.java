package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.nififlow.NifiFlowDto;
import com.enttribe.promptanalyzer.dto.agent.AgentConvertDto;
import com.enttribe.promptanalyzer.dto.agent.AgentDto;
import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.AgentHistory;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.service.AgentHistoryService;
import com.enttribe.promptanalyzer.service.AgentService;
import com.enttribe.promptanalyzer.service.CustomAgentService;
import com.enttribe.promptanalyzer.service.NifiFlowService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;

import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.security.test.context.support.WithMockUser;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AgentRestImpl.class)
class AgentRestImplTest {

    private static final String TEST_AGENT = "testAgent";
    private static final String TEST_QUERY = "test_query";
    private static final String TEST_PROCESS_GROUP_ID = "test-process-group-123";
    private static final String TEST_CONVERSATION_ID = "conv-123";

    @MockitoBean
    private AgentService agentService;

    @MockitoBean
    private CustomAgentService customAgentService;

    @MockitoBean
    private NifiFlowService nifiFlowService;

    @MockitoBean
    private AgentHistoryService agentHistoryService;

    @Autowired
    private MockMvc mockMvc;

    private AgentDto agentDto;
    private CustomAgentDto customAgentDto;
    private NifiFlowDto flowDto;
    private AgentHistory agentHistory;
    private Map<String, String> expectedResponse;
    private List<AgentConvertDto> agentConvertDtos;

    @BeforeEach
    void setUp() {
        // Setup AgentDto with complete data
        agentDto = new AgentDto();
        agentDto.setDisplayName("Test Agent");
        agentDto.setName("test-agent");
        agentDto.setIcon("agent-icon.png");
        agentDto.setPurpose("Testing purposes");
        agentDto.setStatus("ACTIVE");
        agentDto.setVariables("{}");

        Prompt prompt = new Prompt();
        agentDto.setPrompt(prompt);

        // Setup CustomAgentDto
        customAgentDto = new CustomAgentDto();
        customAgentDto.setConversationId(TEST_CONVERSATION_ID);
        customAgentDto.setUserQuery(TEST_QUERY);
        customAgentDto.setProcessGroupId(TEST_PROCESS_GROUP_ID);
        customAgentDto.setTimeStamp(System.currentTimeMillis());

        // Setup NifiFlowDto
        flowDto = new NifiFlowDto();
        flowDto.setProcessGroupId(TEST_PROCESS_GROUP_ID);
        flowDto.setFlowObject("{\"flow\": \"test\"}");

        // Setup AgentHistory
        agentHistory = new AgentHistory();
        agentHistory.setId(1);
        agentHistory.setAgentName(TEST_AGENT);
        agentHistory.setCurrentPlan("Test plan");
        agentHistory.setLastFlow("Test flow");

        // Setup AgentConvertDto list
        AgentConvertDto agentConvertDto1 = AgentConvertDto.builder()
                .id(1L)
                .name("Agent 1")
                .build();
        AgentConvertDto agentConvertDto2 = AgentConvertDto.builder()
                .id(2L)
                .name("Agent 2")
                .build();
        agentConvertDtos = Arrays.asList(agentConvertDto1, agentConvertDto2);

        // Setup expected responses
        expectedResponse = Map.of("status", "success", "message", "Operation completed successfully");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})
    @DisplayName("Create agent successfully")
    void createAgentSuccess() throws Exception {
        // Arrange
        when(agentService.createAgent(any(AgentDto.class))).thenReturn(expectedResponse);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/agent/create")
                        .content(asJsonString(agentDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("success"))
                .andExpect(jsonPath("$.message").value("Operation completed successfully"));

        verify(agentService).createAgent(any(AgentDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})
    @DisplayName("Create agent with validation error")
    void createAgentValidationError() throws Exception {
        // Arrange
        when(agentService.createAgent(any(AgentDto.class))).thenThrow(new BusinessException("Validation failed"));

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/agent/create")
                        .content(asJsonString(agentDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isBadRequest()); // Changed from isInternalServerError to isBadRequest

        verify(agentService).createAgent(any(AgentDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_READ"})
    @DisplayName("Search agents with all parameters")
    void searchAgentsWithAllParameters() throws Exception {
        // Arrange
        String filter = "test";
        Integer offset = 0;
        Integer size = 10;
        String orderBy = "name";
        String orderType = "asc";

        when(agentService.search(filter, offset, size, orderBy, orderType)).thenReturn(agentConvertDtos);

        // Act & Assert - Note: Using "orderby" (lowercase) to match implementation
        mockMvc.perform(MockMvcRequestBuilders.get("/agent/search")
                        .param("filter", filter)
                        .param("offset", String.valueOf(offset))
                        .param("size", String.valueOf(size))
                        .param("orderby", orderBy)  // Changed from "orderBy" to "orderby"
                        .param("orderType", orderType)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].name").value("Agent 1"))
                .andExpect(jsonPath("$[1].id").value(2))
                .andExpect(jsonPath("$[1].name").value("Agent 2"));

        verify(agentService).search(filter, offset, size, orderBy, orderType);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_READ"})
    @DisplayName("Search agents with no parameters")
    void searchAgentsWithNoParameters() throws Exception {
        // Arrange
        when(agentService.search(null, null, null, null, null)).thenReturn(agentConvertDtos);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/agent/search")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2));

        verify(agentService).search(null, null, null, null, null);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_READ"})
    @DisplayName("Search agents returns empty list")
    void searchAgentsReturnsEmptyList() throws Exception {
        // Arrange
        when(agentService.search(anyString(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/agent/search")
                        .param("filter", "nonexistent")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));

        verify(agentService).search(anyString(), isNull(), isNull(), isNull(), isNull());
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})
    @DisplayName("Get plan for user query successfully")
    void getPlanForUserQuerySuccess() throws Exception {
        // Arrange
        Map<String, String> expectedPlan = Map.of(
                "plan", "Generated plan for user query",
                "status", "success"
        );
        when(customAgentService.getPlanforUserQuery(any(CustomAgentDto.class))).thenReturn(expectedPlan);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/agent/getPlanforUserQuery")
                        .content(asJsonString(customAgentDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.plan").value("Generated plan for user query"))
                .andExpect(jsonPath("$.status").value("success"));

        verify(customAgentService).getPlanforUserQuery(any(CustomAgentDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})
    @DisplayName("Get plan for user query with error")
    void getPlanForUserQueryError() throws Exception {
        // Arrange
        when(customAgentService.getPlanforUserQuery(any(CustomAgentDto.class)))
                .thenThrow(new BusinessException("Failed to generate plan"));

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/agent/getPlanforUserQuery")
                        .content(asJsonString(customAgentDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isBadRequest()); // Changed from isInternalServerError to isBadRequest

        verify(customAgentService).getPlanforUserQuery(any(CustomAgentDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_READ"})
    @DisplayName("Get agent history successfully")
    void getAgentHistorySuccess() throws Exception {
        // Arrange
        when(agentHistoryService.getAgentHistory(TEST_AGENT)).thenReturn(agentHistory);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/agent/getAgentHistory")
                        .param("processGroupId", TEST_AGENT))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.processGroupId").value(TEST_AGENT))
                .andExpect(jsonPath("$.currentPlan").value("Test plan"))
                .andExpect(jsonPath("$.lastFlow").value("Test flow"));

        verify(agentHistoryService).getAgentHistory(TEST_AGENT);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_READ"})
    @DisplayName("Get agent history not found")
    void getAgentHistoryNotFound() throws Exception {
        // Arrange
        when(agentHistoryService.getAgentHistory(anyString())).thenReturn(null);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/agent/getAgentHistory")
                        .param("processGroupId", "nonexistent"))
                .andExpect(status().isOk());

        verify(agentHistoryService).getAgentHistory("nonexistent");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})
    @DisplayName("Create trigger in NiFi successfully")
    void createTriggerInNifiSuccess() throws Exception {
        // Arrange
        Map<String, String> expectedNifiResponse = Map.of(
                "status", "success",
                "triggerId", "trigger-123",
                "message", "Trigger created successfully"
        );
        when(nifiFlowService.createTriggerNifi(any(NifiFlowDto.class))).thenReturn(expectedNifiResponse);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/agent/createTriggerInNifi")
                        .content(asJsonString(flowDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("success"))
                .andExpect(jsonPath("$.triggerId").value("trigger-123"))
                .andExpect(jsonPath("$.message").value("Trigger created successfully"));

        verify(nifiFlowService).createTriggerNifi(any(NifiFlowDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})
    @DisplayName("Create trigger in NiFi with error")
    void createTriggerInNifiError() throws Exception {
        // Arrange
        when(nifiFlowService.createTriggerNifi(any(NifiFlowDto.class)))
                .thenThrow(new BusinessException("Failed to create trigger in NiFi"));

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/agent/createTriggerInNifi")
                        .content(asJsonString(flowDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isBadRequest()); // Changed from isInternalServerError to isBadRequest

        verify(nifiFlowService).createTriggerNifi(any(NifiFlowDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})
    @DisplayName("Create trigger name successfully")
    void createTriggerNameSuccess() throws Exception {
        // Arrange
        Map<String, String> requestBody = Map.of("userQuery", TEST_QUERY);
        Map<String, String> expectedTriggerNameResponse = Map.of(
                "triggerName", "GeneratedTriggerName",
                "description", "Generated description for trigger"
        );

        when(customAgentService.createTriggerNameDescription(TEST_QUERY)).thenReturn(expectedTriggerNameResponse);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/agent/createTriggerName")
                        .content(asJsonString(requestBody))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.triggerName").value("GeneratedTriggerName"))
                .andExpect(jsonPath("$.description").value("Generated description for trigger"));

        verify(customAgentService).createTriggerNameDescription(TEST_QUERY);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})
    @DisplayName("Create trigger name with missing userQuery")
    void createTriggerNameMissingUserQuery() throws Exception {
        // Arrange
        Map<String, String> requestBody = new HashMap<>(); // Empty request body

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/agent/createTriggerName")
                        .content(asJsonString(requestBody))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isOk()); // The controller doesn't validate, it passes null to service

        verify(customAgentService).createTriggerNameDescription(null);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})
    @DisplayName("Create trigger name with service error")
    void createTriggerNameServiceError() throws Exception {
        // Arrange
        Map<String, String> requestBody = Map.of("userQuery", TEST_QUERY);
        when(customAgentService.createTriggerNameDescription(TEST_QUERY))
                .thenThrow(new BusinessException("Failed to generate trigger name"));

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/agent/createTriggerName")
                        .content(asJsonString(requestBody))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isBadRequest()); // Changed from isInternalServerError to isBadRequest

        verify(customAgentService).createTriggerNameDescription(TEST_QUERY);
    }

    /**
     * Utility method to convert objects to JSON string for testing
     */
    private static String asJsonString(final Object obj) {
        try {
            return new ObjectMapper().writeValueAsString(obj);
        } catch (Exception e) {
            throw new BusinessException("Failed to convert object to JSON", e);
        }
    }
}
