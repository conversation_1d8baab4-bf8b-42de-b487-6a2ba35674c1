package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.trigger.TriggerRequestDto;
import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.TriggerService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.security.test.context.support.WithMockUser;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.hasSize;

@WebMvcTest(TriggerRestImpl.class)
class TriggerRestImplTest {

    private static final String TEST_FILTER = "test-filter";
    private static final String TEST_ORDER_BY = "id";
    private static final String TEST_ORDER_TYPE = "asc";
    private static final Integer TEST_TRIGGER_ID = 1;

    @Autowired
    private MockMvc mockMvc;

    @MockitoBean
    private TriggerService service;

    @Autowired
    private ObjectMapper objectMapper;

    private TriggerRequestDto triggerRequestDto;
    private TriggerResponseDto triggerResponseDto;
    private List<TriggerResponseDto> triggerResponseList;
    private Map<String, String> successResponse;

    @BeforeEach
    void setUp() {
        // Setup TriggerRequestDto
        triggerRequestDto = new TriggerRequestDto();
        triggerRequestDto.setName("Test Trigger");
        triggerRequestDto.setDescription("Test Description");


        // Setup TriggerResponseDto
        triggerResponseDto = new TriggerResponseDto();
        triggerResponseDto.setId(TEST_TRIGGER_ID);
        triggerResponseDto.setName("Test Trigger");
        triggerResponseDto.setDescription("Test Description");
        triggerResponseDto.setType("manual");


        // Setup TriggerResponseDto list
        TriggerResponseDto trigger2 = new TriggerResponseDto();
        trigger2.setId(2);
        trigger2.setName("Test Trigger 2");
        trigger2.setDescription("Test Description 2");
        trigger2.setType("automatic");


        triggerResponseList = Arrays.asList(triggerResponseDto, trigger2);

        // Setup success response
        successResponse = Map.of("message", "Trigger saved successfully");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TRIGGER_READ"})
    @DisplayName("Search triggers successfully")
    void search() throws Exception {
        // Arrange
        when(service.search(anyString(), any(), any(), anyString(), anyString())).thenReturn(triggerResponseList);

        // Act & Assert
        mockMvc.perform(get("/trigger/search")
                        .param("filter", TEST_FILTER)
                        .param("offset", "0")
                        .param("size", "10")
                        .param("orderBy", TEST_ORDER_BY)
                        .param("orderType", TEST_ORDER_TYPE)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].name").value("Test Trigger"))
                .andExpect(jsonPath("$[1].id").value(2))
                .andExpect(jsonPath("$[1].name").value("Test Trigger 2"))
                .andDo(print());

        verify(service).search(TEST_FILTER, 0, 10, TEST_ORDER_BY, TEST_ORDER_TYPE);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TRIGGER_READ"})
    @DisplayName("Search triggers returns empty list")
    void searchEmpty() throws Exception {
        // Arrange
        when(service.search(anyString(), any(), any(), anyString(), anyString())).thenReturn(Collections.emptyList());

        // Act & Assert
        mockMvc.perform(get("/trigger/search")
                        .param("filter", "nonexistent")
                        .param("offset", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(0)));

        verify(service).search("nonexistent", 0, 10, null, null);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TRIGGER_READ"})
    @DisplayName("Count triggers successfully")
    void count() throws Exception {
        // Arrange
        when(service.count(anyString())).thenReturn(5L);

        // Act & Assert
        mockMvc.perform(get("/trigger/count")
                        .param("filter", TEST_FILTER)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("5"));

        verify(service).count(TEST_FILTER);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TRIGGER_READ"})
    @DisplayName("Count triggers with no filter")
    void countWithNoFilter() throws Exception {
        // Arrange
        when(service.count(null)).thenReturn(10L);

        // Act & Assert
        mockMvc.perform(get("/trigger/count"))
                .andExpect(status().isOk())
                .andExpect(content().string("10"));

        verify(service).count(null);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TRIGGER_WRITE"})
    @DisplayName("Save trigger successfully")
    void save() throws Exception {
        // Arrange
        when(service.createTrigger(any(TriggerRequestDto.class))).thenReturn(successResponse);

        // Act & Assert
        mockMvc.perform(post("/trigger/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(triggerRequestDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(successResponse)));

        verify(service).createTrigger(any(TriggerRequestDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TRIGGER_WRITE"})
    @DisplayName("Save trigger with validation error")
    void saveValidationError() throws Exception {
        // Arrange
        when(service.createTrigger(any(TriggerRequestDto.class)))
                .thenThrow(new BusinessException("Validation failed"));

        // Act & Assert
        mockMvc.perform(post("/trigger/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(triggerRequestDto))
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(service).createTrigger(any(TriggerRequestDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TRIGGER_WRITE"})
    @DisplayName("Update trigger successfully")
    void update() throws Exception {
        // Arrange
        Map<String, String> updateResponse = Map.of("message", "Trigger updated successfully");
        when(service.updateTrigger(any(TriggerRequestDto.class))).thenReturn(updateResponse);

        // Act & Assert
        mockMvc.perform(post("/trigger/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(triggerRequestDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(updateResponse)));

        verify(service).updateTrigger(any(TriggerRequestDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TRIGGER_WRITE"})
    @DisplayName("Update trigger with error")
    void updateError() throws Exception {
        // Arrange
        when(service.updateTrigger(any(TriggerRequestDto.class)))
                .thenThrow(new BusinessException("Update failed"));

        // Act & Assert
        mockMvc.perform(post("/trigger/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(triggerRequestDto))
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(service).updateTrigger(any(TriggerRequestDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TRIGGER_READ"})
    @DisplayName("Search triggers with service error")
    void searchServiceError() throws Exception {
        // Arrange - Return empty list instead of throwing exception
        when(service.search(anyString(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        mockMvc.perform(get("/trigger/search")
                        .param("filter", "test")
                        .param("offset", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(0)));

        verify(service).search("test", 0, 10, null, null);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TRIGGER_READ"})
    @DisplayName("Count triggers with service error")
    void countServiceError() throws Exception {
        // Arrange
        when(service.count(anyString())).thenThrow(new BusinessException("Count service unavailable"));

        // Act & Assert
        mockMvc.perform(get("/trigger/count")
                        .param("filter", "test"))
                .andExpect(status().isBadRequest());

        verify(service).count("test");
    }
}
