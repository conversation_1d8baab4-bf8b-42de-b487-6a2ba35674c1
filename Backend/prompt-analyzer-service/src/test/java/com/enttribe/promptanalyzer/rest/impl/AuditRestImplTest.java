package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.audit.ExceptionAuditDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.PromptAudit;
import com.enttribe.promptanalyzer.service.AuditService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;

@WebMvcTest(AuditRestImpl.class)
class AuditRestImplTest {

   @MockitoBean
    private AuditService auditService;

    @Autowired
    private MockMvc mockMvc;

    private ExceptionAuditDto exceptionAuditDto;
    private PromptAuditDto promptAuditDto;

    @BeforeEach
    void setUp() {
        exceptionAuditDto = new ExceptionAuditDto();
        exceptionAuditDto.setAuditId("123");

        promptAuditDto = new PromptAuditDto();
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_EXCEPTION_SAVE"})
    @DisplayName("Save Exception Audit Successfully")
    void saveExceptionAuditSuccess() throws Exception {
        when(auditService.saveExceptionAudit(any())).thenReturn("123");

        mockMvc.perform(post("/audit/exception/save")
                        .content(asJsonString(exceptionAuditDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(content().string("123"));

        verify(auditService).saveExceptionAudit(any());
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_EXCEPTION_SAVE"})
    @DisplayName("Save Prompt Audit Successfully")
    void savePromptAuditSuccess() throws Exception {
        when(auditService.savePromptAudit(any())).thenReturn("456");

        mockMvc.perform(post("/audit/prompt/save")
                        .content(asJsonString(promptAuditDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(content().string("456"));

        verify(auditService).savePromptAudit(any());
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_EXCEPTION_READ"})
    @DisplayName("Get Prompt Audit List by Audit ID Successfully")
    void getPromptAuditListByAuditIdSuccess() throws Exception {
        String auditId = "EMAIL_SINGULARITY_APP_NAME_d8f894cb-2440-49e7-ab08-b57d04f402ac";
        List<PromptAudit> expectedList = Collections.singletonList(new PromptAudit());
        when(auditService.getPromptAuditListByAuditId(auditId)).thenReturn(expectedList);

        mockMvc.perform(get("/audit/getPromptAuditListByAuditId/" + auditId))
                .andExpect(status().isOk())
                .andExpect(content().json(asJsonString(expectedList)));

        verify(auditService).getPromptAuditListByAuditId(auditId);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_EXCEPTION_SAVE"})
    @DisplayName("Save Exception Audit Failure")
    void saveExceptionAuditFailure() throws Exception {
        when(auditService.saveExceptionAudit(any())).thenThrow(new RuntimeException("Service Error"));

        mockMvc.perform(post("/audit/exception/save")
                        .content(asJsonString(exceptionAuditDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isInternalServerError());

        verify(auditService).saveExceptionAudit(any());
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_EXCEPTION_SAVE"})
    @DisplayName("Save Prompt Audit Failure")
    void savePromptAuditFailure() throws Exception {
        when(auditService.savePromptAudit(any())).thenThrow(new RuntimeException("Service Error"));

        mockMvc.perform(post("/audit/prompt/save")
                        .content(asJsonString(promptAuditDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isInternalServerError());

        verify(auditService).savePromptAudit(any());
    }

    static String asJsonString(final Object obj) {
        try {
            return new ObjectMapper().writeValueAsString(obj);
        } catch (Exception e) {
            throw new BusinessException("Error converting to JSON", e);
        }
    }
}
