package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.TestCaseRequestDto;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.TestCase;
import com.enttribe.promptanalyzer.service.TestCaseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(TestCaseRestImpl.class)
class TestCaseRestImplTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

   @MockitoBean
    private TestCaseService testCaseService;



    @Test
    @DisplayName("Update test case success")
    void updateTestCaseSuccess() throws Exception {
        TestCase updatedTestcase = new TestCase();
        Map<String, String> response = Map.of("message", "Test case updated successfully");

        Mockito.when(testCaseService.update(any(TestCase.class))).thenReturn(response);

        /*mockMvc.perform(post("/test-case/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updatedTestcase)))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(response)));*/
    }



}
