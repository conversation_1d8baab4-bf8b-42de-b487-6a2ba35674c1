package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.AssertionTemplateDto;
import com.enttribe.promptanalyzer.dto.prompt.*;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.service.PromptService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import com.fasterxml.jackson.databind.ObjectMapper;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.when;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.containsString;

@WebMvcTest(PromptRestImpl.class)
class PromptRestImplTest {

    @Autowired
    private MockMvc mockMvc;

   @MockitoBean
    private PromptService promptService;

    @Autowired
    private ObjectMapper objectMapper;

    private PromptDto basePromptDto;
    private PromptRequestDto basePromptRequestDto;

    @BeforeEach
    void setUp() {
        basePromptDto = new PromptDto();
        basePromptDto.setApplication("testApplication");
        basePromptDto.setPromptId("test-prompt-id");
        basePromptDto.setTemperature(1.0);
        basePromptDto.setMaxTokens(1000);
        basePromptDto.setTopP(0.7);
        basePromptDto.setName("Test Prompt");

        basePromptRequestDto = new PromptRequestDto();
        basePromptRequestDto.setApplication("testApplication");
        basePromptRequestDto.setName("Test Prompt");
        basePromptRequestDto.setCategory("testCategory");
        basePromptRequestDto.setStatus("active");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Health check endpoint success")
    void success() throws Exception {
        mockMvc.perform(get("/prompt/ping"))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"status\":\"success\"}"))
                .andDo(print());
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_WRITE"})
    @DisplayName("Save prompt success")
    void promptSave() throws Exception {
        // Arrange
        when(promptService.savePrompt(any(PromptDto.class))).thenReturn(Map.of("status", "saved"));

        // Act & Assert
        mockMvc.perform(post("/prompt/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"status\":\"saved\"}"))
                .andDo(print());

        verify(promptService).savePrompt(any(PromptDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_WRITE"})
    @DisplayName("Save prompt with validation error")
    void promptSaveValidationError() throws Exception {
        // Arrange
        when(promptService.savePrompt(any(PromptDto.class)))
                .thenThrow(new BusinessException("Validation failed"));

        // Act & Assert
        mockMvc.perform(post("/prompt/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptDto))
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(promptService).savePrompt(any(PromptDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Get versions of prompt success")
    void getVersionsOfPrompt() throws Exception {
        // Arrange
        List<PromptVersionDetailsDto> versions = List.of(
                new PromptVersionDetailsDto(1, "1.0"),
                new PromptVersionDetailsDto(2, "2.0")
        );
        when(promptService.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString())).thenReturn(versions);

        // Act & Assert
        mockMvc.perform(post("/prompt/getVersionsOfPrompt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptRequestDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].version").value("1.0"))
                .andDo(print());

        verify(promptService).getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Get versions of prompt with error")
    void getVersionsOfPromptError() throws Exception {
        // Arrange
        when(promptService.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString()))
                .thenThrow(new BusinessException("Prompt not found"));

        // Act & Assert
        mockMvc.perform(post("/prompt/getVersionsOfPrompt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptRequestDto))
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(promptService).getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_WRITE"})
    @DisplayName("Edit prompt success")
    void promptEdit() throws Exception {
        // Arrange
        Map<String, String> response = Map.of("status", "updated");
        when(promptService.updatePrompt(any(PromptDto.class))).thenReturn(response);

        // Act & Assert
        mockMvc.perform(post("/prompt/edit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"status\":\"updated\"}"))
                .andDo(print());

        verify(promptService).updatePrompt(any(PromptDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_WRITE"})
    @DisplayName("Edit prompt with error")
    void promptEditError() throws Exception {
        // Arrange
        when(promptService.updatePrompt(any(PromptDto.class)))
                .thenThrow(new BusinessException("Update failed"));

        // Act & Assert
        mockMvc.perform(post("/prompt/edit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptDto))
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(promptService).updatePrompt(any(PromptDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_WRITE"})
    @DisplayName("Delete prompt by ID success")
    void deleteById() throws Exception {
        // Arrange
        Map<String, Integer> request = Map.of("id", 1);
        Map<String, String> response = Map.of("status", "deleted");
        when(promptService.softDelete(1)).thenReturn(response);

        // Act & Assert
        mockMvc.perform(post("/prompt/deleteById")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"status\":\"deleted\"}"))
                .andDo(print());

        verify(promptService).softDelete(1);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_WRITE"})
    @DisplayName("Delete prompt by ID with error")
    void deleteByIdError() throws Exception {
        // Arrange
        Map<String, Integer> request = Map.of("id", 999);
        when(promptService.softDelete(999)).thenThrow(new BusinessException("Prompt not found"));

        // Act & Assert
        mockMvc.perform(post("/prompt/deleteById")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(promptService).softDelete(999);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Get prompt basic details by application success")
    void getPromptBasicDetailByApplication() throws Exception {
        // Arrange
        Map<String, String> request = Map.of("application", "testApp");
        List<Map<String, String>> response = List.of(
                Map.of("name", "Test Prompt", "status", "active")
        );
        when(promptService.getPromptBasicDetailByApplication("testApp")).thenReturn(response);

        // Act & Assert
        mockMvc.perform(post("/prompt/getPromptBasicDetailByApplication")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].name").value("Test Prompt"))
                .andDo(print());

        verify(promptService).getPromptBasicDetailByApplication("testApp");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Get prompt basic details by application returns empty")
    void getPromptBasicDetailByApplicationEmpty() throws Exception {
        // Arrange
        Map<String, String> request = Map.of("application", "nonexistent");
        when(promptService.getPromptBasicDetailByApplication("nonexistent")).thenReturn(Collections.emptyList());

        // Act & Assert
        mockMvc.perform(post("/prompt/getPromptBasicDetailByApplication")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(0)));

        verify(promptService).getPromptBasicDetailByApplication("nonexistent");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Get prompt by ID success")
    void getPromptById() throws Exception {
        // Arrange
        Map<String, Integer> request = Map.of("id", 1);
        PromptConvertorDto response = PromptConvertorDto.builder()
                .id(1)
                .name("Test Prompt")
                .build();
        when(promptService.getPromptById(1)).thenReturn(response);

        // Act & Assert
        mockMvc.perform(post("/prompt/viewById")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("Test Prompt"))
                .andDo(print());

        verify(promptService).getPromptById(1);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Get prompt by ID not found")
    void getPromptByIdNotFound() throws Exception {
        // Arrange
        Map<String, Integer> request = Map.of("id", 999);
        when(promptService.getPromptById(999)).thenThrow(new BusinessException("Prompt not found"));

        // Act & Assert
        mockMvc.perform(post("/prompt/viewById")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(promptService).getPromptById(999);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Get SDK prompt by ID success")
    void getPromptByIdSdk() throws Exception {
        // Arrange
        PromptDtoSdk response = PromptDtoSdk.builder()
                .id("1")
                .promptName("Test Prompt")
                .build();
        when(promptService.findPromptById(1)).thenReturn(response);

        // Act & Assert
        mockMvc.perform(get("/prompt/getPromptById/{id}", 1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value("1"))
                .andExpect(jsonPath("$.promptName").value("Test Prompt"))
                .andDo(print());

        verify(promptService).findPromptById(1);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Get SDK prompt by ID not found")
    void getPromptByIdSdkNotFound() throws Exception {
        // Arrange
        when(promptService.findPromptById(999)).thenThrow(new BusinessException("Prompt not found"));

        // Act & Assert
        mockMvc.perform(get("/prompt/getPromptById/{id}", 999)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        verify(promptService).findPromptById(999);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Check prompt exists success")
    void exists() throws Exception {
        // Arrange
        Map<String, Object> response = Map.of("exists", true);
        when(promptService.exists(any(PromptRequestDto.class))).thenReturn(response);

        // Act & Assert
        mockMvc.perform(post("/prompt/exists")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptRequestDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.exists").value(true))
                .andDo(print());

        verify(promptService).exists(any(PromptRequestDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Check prompt exists - not found")
    void existsNotFound() throws Exception {
        // Arrange
        Map<String, Object> response = Map.of("exists", false);
        when(promptService.exists(any(PromptRequestDto.class))).thenReturn(response);

        // Act & Assert
        mockMvc.perform(post("/prompt/exists")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptRequestDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.exists").value(false));

        verify(promptService).exists(any(PromptRequestDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Get distinct applications success")
    void getDistinctApplications() throws Exception {
        // Arrange
        List<String> response = List.of("app1", "app2");
        when(promptService.getDistinctApplications(anyString())).thenReturn(response);

        // Act & Assert
        mockMvc.perform(get("/prompt/getDistinctApplications")
                        .param("applicationName", "test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0]").value("app1"))
                .andDo(print());

        verify(promptService).getDistinctApplications("test");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Get distinct applications returns empty")
    void getDistinctApplicationsEmpty() throws Exception {
        // Arrange
        when(promptService.getDistinctApplications(anyString())).thenReturn(Collections.emptyList());

        // Act & Assert
        mockMvc.perform(get("/prompt/getDistinctApplications")
                        .param("applicationName", "nonexistent"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(0)));

        verify(promptService).getDistinctApplications("nonexistent");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_PROMPT_READ"})
    @DisplayName("Get distinct categories by app success")
    void getDistinctCategoriesByAppSuccess() throws Exception {
        // Arrange
        String applicationName = "testApp";
        List<String> expectedCategories = List.of(
                "category1",
                "category2",
                "category3"
        );
        when(promptService.getDistinctCategoriesByApp(applicationName))
                .thenReturn(expectedCategories);

        // Act & Assert
        mockMvc.perform(get("/prompt/getDistinctCategoriesByApp")
                        .param("applicationName", applicationName)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(3)))
                .andExpect(jsonPath("$[0]").value("category1"))
                .andExpect(jsonPath("$[1]").value("category2"))
                .andExpect(jsonPath("$[2]").value("category3"))
                .andDo(print());

        verify(promptService, times(1)).getDistinctCategoriesByApp(applicationName);
    }





}
