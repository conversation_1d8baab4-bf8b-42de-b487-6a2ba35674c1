package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.tool.*;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Tool;
import com.enttribe.promptanalyzer.service.ToolService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.security.test.context.support.WithMockUser;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.hasSize;

@WebMvcTest(ToolRestImpl.class)
class ToolRestImplTest {

    private static final String SOURCE_CODE = "sourceCode";
    private static final String CLASS_NAME = "className";
    private static final String STATUS = "status";
    private static final String JSON_STATUS = "$.status";
    private static final String SUCCESS = "success";
    private static final String TEST_APP_NAME = "testApp";
    private static final Integer TEST_TOOL_ID = 1;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockitoBean
    private ToolService toolService;

    private ToolDto toolDto;
    private SwaggerDto swaggerDto;
    private ToolWorkflowDto toolWorkflowDto;
    private List<ToolConvertorDto> toolConvertorDtos;
    private List<ToolDtoSdk> toolDtoSdks;
    private List<Tool> tools;
    private Map<String, String> successResponse;

    @BeforeEach
    void setUp() {
        // Setup ToolDto
        toolDto = new ToolDto();
        toolDto.setName("Test Tool");
        toolDto.setDescription("Test Description");
        toolDto.setType("function");
        toolDto.setStatus("active");

        // Setup SwaggerDto
        swaggerDto = new SwaggerDto();
        swaggerDto.setSwaggerJson("swagger: '2.0'");
        swaggerDto.setApplicationName(TEST_APP_NAME);

        // Setup ToolWorkflowDto
        toolWorkflowDto = new ToolWorkflowDto();
        toolWorkflowDto.setName("Test Workflow");
        toolWorkflowDto.setDescription("Test Workflow Description");

        // Setup ToolConvertorDto list
        ToolConvertorDto tool1 = new ToolConvertorDto();
        tool1.setId(1);
        tool1.setName("Tool 1");
        ToolConvertorDto tool2 = new ToolConvertorDto();
        tool2.setId(2);
        tool2.setName("Tool 2");
        toolConvertorDtos = Arrays.asList(tool1, tool2);

        // Setup ToolDtoSdk list
        ToolDtoSdk toolSdk1 = new ToolDtoSdk();
        toolSdk1.setId(1);
        toolSdk1.setToolName("SDK Tool 1");
        ToolDtoSdk toolSdk2 = new ToolDtoSdk();
        toolSdk2.setId(2);
        toolSdk2.setToolName("SDK Tool 2");
        toolDtoSdks = Arrays.asList(toolSdk1, toolSdk2);

        // Setup Tool list
        Tool tool = new Tool();
        tool.setId(TEST_TOOL_ID);
        tool.setToolName("Test Tool");
        tools = Arrays.asList(tool);

        // Setup success response
        successResponse = Map.of(STATUS, SUCCESS, "message", "Operation completed successfully");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("Save success")
    void saveToolSuccess() throws Exception {
        // Arrange
        when(toolService.createTool(any(ToolDto.class))).thenReturn(successResponse);

        // Act & Assert
        mockMvc.perform(post("/tool/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(toolDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS))
                .andExpect(jsonPath("$.message").value("Operation completed successfully"))
                .andDo(print());

        verify(toolService).createTool(any(ToolDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("Save tool with validation error")
    void saveToolValidationError() throws Exception {
        // Arrange
        when(toolService.createTool(any(ToolDto.class)))
                .thenThrow(new BusinessException("Validation failed"));

        // Act & Assert
        mockMvc.perform(post("/tool/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(toolDto))
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(toolService).createTool(any(ToolDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("create tool from swagger success")
    void createToolFromSwaggerSuccess() throws Exception {
        // Arrange
        when(toolService.generateTools(any(SwaggerDto.class)))
                .thenReturn(Map.of(SUCCESS, 1, "failed", 0));

        // Act & Assert
        mockMvc.perform(post("/tool/toolFromSwagger")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(swaggerDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS))
                .andDo(print());

        verify(toolService).generateTools(any(SwaggerDto.class));
    }


    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("search tools success")
    void searchSuccess() throws Exception {
        // Arrange
        when(toolService.search(anyString(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn(toolConvertorDtos);

        // Act & Assert
        mockMvc.perform(get("/tool/search")
                        .param("filter", "test")
                        .param("offset", "0")
                        .param("size", "10")
                        .param("orderBy", "name")
                        .param("orderType", "asc"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].name").value("Tool 1"))
                .andExpect(jsonPath("$[1].id").value(2))
                .andExpect(jsonPath("$[1].name").value("Tool 2"))
                .andDo(print());

        verify(toolService).search("test", 0, 10, "name", "asc");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("search tools returns empty")
    void searchEmpty() throws Exception {
        // Arrange
        when(toolService.search(anyString(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        mockMvc.perform(get("/tool/search")
                        .param("filter", "nonexistent")
                        .param("offset", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(0)));

        verify(toolService).search("nonexistent", 0, 10, null, null);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("count tools success")
    void countSuccess() throws Exception {
        // Arrange
        when(toolService.count(anyString())).thenReturn(5L);

        // Act & Assert
        mockMvc.perform(get("/tool/count")
                        .param("filter", "test"))
                .andExpect(status().isOk())
                .andExpect(content().string("5"));

        verify(toolService).count("test");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("count tools with no filter")
    void countWithNoFilter() throws Exception {
        // Arrange
        when(toolService.count(null)).thenReturn(10L);

        // Act & Assert
        mockMvc.perform(get("/tool/count"))
                .andExpect(status().isOk())
                .andExpect(content().string("10"));

        verify(toolService).count(null);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("Delete By Id success")
    void deleteByIdSuccess() throws Exception {
        // Arrange
        when(toolService.softDelete(anyInt())).thenReturn(Map.of(STATUS, SUCCESS));

        // Act & Assert
        mockMvc.perform(post("/tool/deleteById")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of("id", 1)))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));

        verify(toolService).softDelete(1);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("Delete By Id with error")
    void deleteByIdError() throws Exception {
        // Arrange
        when(toolService.softDelete(anyInt())).thenThrow(new BusinessException("Tool not found"));

        // Act & Assert
        mockMvc.perform(post("/tool/deleteById")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of("id", 999)))
                        .with(csrf()))
                .andExpect(status().isBadRequest());

        verify(toolService).softDelete(999);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("get tool by id success")
    void getToolByIdSuccess() throws Exception {
        // Arrange
        ToolConvertorDto tool = new ToolConvertorDto();
        tool.setId(1);
        tool.setName("Test Tool");
        when(toolService.getToolById(anyInt())).thenReturn(tool);

        // Act & Assert
        mockMvc.perform(get("/tool/findById/{toolId}", 1))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("Test Tool"));

        verify(toolService).getToolById(1);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("get tool by id not found")
    void getToolByIdNotFound() throws Exception {
        // Arrange
        when(toolService.getToolById(anyInt())).thenThrow(new BusinessException("Tool not found"));

        // Act & Assert
        mockMvc.perform(get("/tool/findById/{toolId}", 999))
                .andExpect(status().isBadRequest());

        verify(toolService).getToolById(999);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("change tool status success")
    void changeToolStatusSuccess() throws Exception {
        // Arrange
        Map<String, Object> request = Map.of("id", 1, STATUS, "active");
        when(toolService.changeToolStatus(anyInt(), anyString()))
                .thenReturn(Map.of(STATUS, SUCCESS));

        // Act & Assert
        mockMvc.perform(post("/tool/changeToolStatus")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));

        verify(toolService).changeToolStatus(1, "active");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("update tool success")
    void updateSuccess() throws Exception {
        ToolDto toolDto = new ToolDto();

        when(toolService.updateTool(any(ToolDto.class)))
                .thenReturn(Map.of(STATUS, SUCCESS));

        mockMvc.perform(post("/tool/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(toolDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("get tools by application success")
    void getToolsByApplicationSuccess() throws Exception {
        List<ToolDtoSdk> tools = List.of(new ToolDtoSdk());
        when(toolService.getToolsByApplication(anyString())).thenReturn(tools);

        mockMvc.perform(get("/tool/getToolsByApplication/{appName}", "testApp"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(tools)));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("create tool from workflow success")
    void createToolFromWorkflowSuccess() throws Exception {
        ToolWorkflowDto workflowDto = new ToolWorkflowDto();
        doNothing().when(toolService).generateToolsFromWorkflow(any(ToolWorkflowDto.class));

        mockMvc.perform(post("/tool/toolFromWorkflow")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(workflowDto))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));
    }


    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("get tool by agent id success")
    void getToolByAgentIdSuccess() throws Exception {
        List<Tool> tools = List.of(new Tool());
        when(toolService.getToolByAgentId(anyLong())).thenReturn(tools);

        mockMvc.perform(get("/tool/getToolByAgentId/{agentId}", 1))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(tools)));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("get tools by ids success")
    void getToolsByIdsSuccess() throws Exception {
        List<Integer> ids = List.of(1, 2);
        when(toolService.getToolsByIds(anyList())).thenReturn(toolDtoSdks);

        mockMvc.perform(post("/tool/getToolsByIds")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(ids))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].toolName").value("SDK Tool 1"));

        verify(toolService).getToolsByIds(ids);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("get tools by ids v1 success")
    void getToolsByIdsV1Success() throws Exception {
        List<Integer> ids = List.of(1, 2);
        when(toolService.getToolsByIdsV1(anyList())).thenReturn(toolConvertorDtos);

        mockMvc.perform(post("/tool/v1/getToolsByIds")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(ids))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].name").value("Tool 1"));

        verify(toolService).getToolsByIdsV1(ids);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("check compilation success")
    void checkCompilationSuccess() throws Exception {
        Map<String, String> request = Map.of(
                SOURCE_CODE , "public class TestClass{}",
                CLASS_NAME, "TestClass"
        );
        when(toolService.checkCompilation(anyString(), anyString())).thenReturn(true);

        mockMvc.perform(post("/tool/checkCompilation")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result").value(true));

        verify(toolService).checkCompilation("public class TestClass{}", "TestClass");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("register agent as tool success")
    void registerAgentAsToolSuccess() throws Exception {
        when(toolService.registerAgentAsTool(anyLong()))
                .thenReturn(Map.of(STATUS, SUCCESS));

        mockMvc.perform(get("/tool/registerAgentAsTool/{id}", 1))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));

        verify(toolService).registerAgentAsTool(1L);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("export tool success")
    void exportToolSuccess() throws Exception {
        mockMvc.perform(get("/tool/exportTool/{appName}", TEST_APP_NAME)
                        .accept(MediaType.APPLICATION_OCTET_STREAM_VALUE))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("import tool success")
    void importToolSuccess() throws Exception {
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.json",
                MediaType.APPLICATION_JSON_VALUE,
                "{\"test\": \"data\"}".getBytes()
        );

        mockMvc.perform(multipart("/tool/importTool")
                        .file(file)
                        .with(csrf()))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("exists tool success")
    void existsToolSuccess() throws Exception {
        when(toolService.existsTool(anyString()))
                .thenReturn(Map.of("exists", true));

        mockMvc.perform(post("/tool/existsTool")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of("toolName", "testTool")))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.exists").value(true));

        verify(toolService).existsTool("testTool");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_WRITE"})
    @DisplayName("update tag by id success")
    void updateTagByIdSuccess() throws Exception {
        when(toolService.updateTagById(anyInt(), any()))
                .thenReturn(Map.of(STATUS, SUCCESS));

        mockMvc.perform(post("/tool/updateTagById/{id}", 1)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of("tags", "tag1,tag2")))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));

        verify(toolService).updateTagById(eq(1), any());
    }

    // Additional error handling tests
    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("search tools with service error")
    void searchServiceError() throws Exception {
        // Arrange - Return empty list instead of throwing exception
        when(toolService.search(anyString(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        mockMvc.perform(get("/tool/search")
                        .param("filter", "test")
                        .param("offset", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(0)));

        verify(toolService).search("test", 0, 10, null, null);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_TOOL_READ"})
    @DisplayName("count tools with service error")
    void countServiceError() throws Exception {
        // Arrange
        when(toolService.count(anyString())).thenThrow(new BusinessException("Count service unavailable"));

        // Act & Assert
        mockMvc.perform(get("/tool/count")
                        .param("filter", "test"))
                .andExpect(status().isBadRequest());

        verify(toolService).count("test");
    }
}