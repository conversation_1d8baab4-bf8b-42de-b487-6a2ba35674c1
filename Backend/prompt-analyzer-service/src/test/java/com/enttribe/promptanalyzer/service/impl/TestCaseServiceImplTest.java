package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.TestCaseDao;
import com.enttribe.promptanalyzer.dto.TestCaseRequestDto;
import com.enttribe.promptanalyzer.model.TestCase;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import com.enttribe.promptanalyzer.exception.BusinessException;
import static org.junit.jupiter.api.Assertions.*;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)

class TestCaseServiceImplTest {

   @Mock
    TestCaseDao testCaseDao;

   @Mock
    CustomFilter customFilter;

    @InjectMocks
    TestCaseServiceImpl testCaseServiceImpl;

    private TestCase testCase;
    private TestCase existingTestCase;

    @BeforeEach
    void setUp() {
        testCase = new TestCase();
        testCase.setId(1);

        existingTestCase = new TestCase();
        existingTestCase.setId(1);
        existingTestCase.setRemark("Remark");
    }

    @Test
    @DisplayName("Create TestCase successfully")
    void create() {
        when(testCaseDao.save(any(TestCase.class))).thenReturn(testCase);
        TestCaseRequestDto requestDto = new TestCaseRequestDto();
        Map<String, String> result = testCaseServiceImpl.create(requestDto);
        assertEquals("success", result.get("result"));
        verify(testCaseDao, times(2)).save(any(TestCase.class));
    }

    @Test
    @DisplayName("Create TestCase failure")
    void createTestFailure() {
        TestCaseRequestDto requestDto = new TestCaseRequestDto();
        when(testCaseDao.save(any(TestCase.class))).thenThrow(new RuntimeException("DB error"));
        assertThrows(BusinessException.class, () -> testCaseServiceImpl.create(requestDto));
        verify(testCaseDao, times(1)).save(any(TestCase.class));
    }

    @Test
    @DisplayName("Update TestCase successfully")
    void updateSuccess() {
        when(testCaseDao.findById(1)).thenReturn(Optional.of(existingTestCase));
        when(testCaseDao.save(any(TestCase.class))).thenReturn(existingTestCase);
        TestCase updatedTestCase = new TestCase();
        updatedTestCase.setId(1);
        updatedTestCase.setRemark("Updated Remark");
        Map<String, String> result = testCaseServiceImpl.update(updatedTestCase);
        assertEquals("success", result.get("result"));
        verify(testCaseDao).save(any(TestCase.class));
    }

    @Test
    @DisplayName("Update TestCase failure")
    void updateFailure() {
        when(testCaseDao.findById(1)).thenReturn(Optional.of(existingTestCase));
        when(testCaseDao.save(any(TestCase.class))).thenThrow(new RuntimeException("Database error"));
        TestCase updatedTestCase = new TestCase();
        updatedTestCase.setId(1);
        updatedTestCase.setRemark("Updated Remark");
        assertThrows(RuntimeException.class, () -> testCaseServiceImpl.update(updatedTestCase));
        verify(testCaseDao).findById(1);
        verify(testCaseDao).save(any(TestCase.class));
    }

    @Test
    @DisplayName("Update TestCase - Not Found")
    void updateNotFound() {
        when(testCaseDao.findById(1)).thenReturn(Optional.empty());
        TestCase updatedTestCase = new TestCase();
        updatedTestCase.setId(1);
        updatedTestCase.setRemark("Updated Remark");
        assertThrows(BusinessException.class, () -> testCaseServiceImpl.update(updatedTestCase));
        verify(testCaseDao, times(1)).findById(1);
        verify(testCaseDao, never()).save(any(TestCase.class));
    }

    @Test
    @DisplayName("Delete TestCase - Not Found")
    void deleteNotFound() {
        when(testCaseDao.findById(1)).thenReturn(Optional.empty());
        assertThrows(BusinessException.class, () -> testCaseServiceImpl.deleteTestcase(1));
        verify(testCaseDao).findById(1);
        verify(testCaseDao, never()).save(any());
    }

    @Test
    @DisplayName("Search Test Cases")
    void search() {
        when(customFilter.searchByFilter(any(), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(List.of(testCase));
        List<TestCase> result = testCaseServiceImpl.search("filter", 0, 10, "id", "asc");
        assertFalse(result.isEmpty());
        verify(customFilter, times(1)).searchByFilter(any(), anyString(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("Empty list when no testCase are found")
    void searchFailure() {
        when(customFilter.searchByFilter(any(), anyString(), anyString(), anyString(), anyInt(), anyInt())).thenReturn(List.of());
        List<TestCase> result = testCaseServiceImpl.search("filter", 0, 10, "id", "asc");
        assertNotNull(result, "Result should not be null");
        assertEquals(0, result.size(), "Result size should be 0 when no data is returned");
    }

    @Test
    @DisplayName("Count Test Cases")
    void count() {
        when(customFilter.countByFilter(any(), anyString())).thenReturn(5L);
        Long count = testCaseServiceImpl.count("filter");
        assertEquals(5L, count);
        verify(customFilter, times(1)).countByFilter(any(), anyString());
    }
}
