package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.ProviderModelDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.LlmModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)

class LlmModelServiceImplTest {

   @Mock
    private LlmModelDao llmModelDao;

    @InjectMocks
    private LlmModelServiceImpl llmModelService;

    private LlmModel mockLlmModel;
    private List<Object[]> mockResults;
    private List<LlmModelSdkDto> mockResponse;

    @BeforeEach
    void setUp() {
        mockLlmModel = new LlmModel();
        mockLlmModel.setId(1);
        mockLlmModel.setProvider("OpenAI");
        mockLlmModel.setModel("GPT-4");
        mockLlmModel.setType("chat");

        mockResponse = List.of(
                new LlmModelSdkDto("OpenAI", "https://api.openai.com", "dummy-key", "GPT-3"),
                new LlmModelSdkDto("OpenAI", "https://api.openai.com", "dummy-key", "GPT-4")
        );

        mockResults = Arrays.asList(
                new Object[]{"Google", "Gemini"},
                new Object[]{"OpenAI", "GPT-3"},
                new Object[]{"OpenAI", "GPT-4"}
        );
    }

    @Test
    @DisplayName("Test getAllProvidersWithModels success")
    void getModelsByProvider() {
        when(llmModelDao.getModelsByProvider("OpenAI", "chat")).thenReturn(List.of("GPT-3", "GPT-4"));
        ProviderModelDto result = llmModelService.getModelsByProvider("OpenAI");
        assertNotNull(result);
        assertEquals("OpenAI", result.getProvider());
        assertEquals(2, result.getModels().size());
        verify(llmModelDao, times(1)).getModelsByProvider("OpenAI", "chat");
    }

    @Test
    @DisplayName("Test getAllProvidersWithModels failed")
    void getAllProvidersWithModels() {
        when(llmModelDao.getAllProvidersWithModels("chat")).thenReturn(mockResults);
        List<ProviderModelDto> result = llmModelService.getAllProvidersWithModels();
        assertEquals("Google", result.get(0).getProvider());
        assertEquals("OpenAI", result.get(1).getProvider());
        verify(llmModelDao, times(1)).getAllProvidersWithModels("chat");
    }

    @Test
    @DisplayName("Test getAllProvidersWithModels no records")
    void getAllProvidersWithModelsNoRecords() {
        when(llmModelDao.getAllProvidersWithModels("chat")).thenReturn(Collections.emptyList());
        BusinessException exception = assertThrows(BusinessException.class, () -> llmModelService.getAllProvidersWithModels());
        assertEquals("No records found in LlmModel for the given query", exception.getMessage());
        verify(llmModelDao, times(1)).getAllProvidersWithModels("chat");
    }

    @Test
    @DisplayName("Create LlmModel success")
    void createLlmModelSuccess() {
        when(llmModelDao.save(any(LlmModel.class))).thenReturn(mockLlmModel);
        Map<String, String> response = llmModelService.create(mockLlmModel);
        assertEquals("success", response.get("result"));
        verify(llmModelDao, times(1)).save(any(LlmModel.class));
    }


    @Test
    @DisplayName("Create LlmModel failed")
    void createLlmModelFailure() {
        doThrow(new RuntimeException("Database error")).when(llmModelDao).save(any(LlmModel.class));
        BusinessException exception = assertThrows(BusinessException.class, () -> llmModelService.create(mockLlmModel));
        assertEquals("Database error", exception.getMessage());
        verify(llmModelDao, times(1)).save(any(LlmModel.class));
    }

    @Test
    @DisplayName("Test get unique inferences by type success")
    void getUniqueInferencesByType() {
        when(llmModelDao.getUniqueInferencesByType("chat")).thenReturn(mockResponse);
        List<LlmModelSdkDto> result = llmModelService.getUniqueInferencesByType("chat");
        assertEquals(2, result.size());
        verify(llmModelDao, times(1)).getUniqueInferencesByType("chat");
    }

    @Test
    @DisplayName("Test get LlmModelsForSDK success")
    void getLlmModelsForSDK() {
        when(llmModelDao.getLlmModelsForSDK("AppX", "chat")).thenReturn(mockResponse);
        List<LlmModelSdkDto> result = llmModelService.getLlmModelsForSDK("AppX", "chat");
        assertEquals(2, result.size());
        verify(llmModelDao, times(1)).getLlmModelsForSDK("AppX", "chat");
    }

    @Test
    @DisplayName("Test get LlmModelsForSDK failed")
    void getLlmModelsByTypeForSDK() {
        when(llmModelDao.getLlmModelsByTypeForSDK("chat")).thenReturn(mockResponse);
        List<LlmModelSdkDto> result = llmModelService.getLlmModelsByTypeForSDK("chat");
        assertEquals(2, result.size());
        verify(llmModelDao, times(1)).getLlmModelsByTypeForSDK("chat");
    }
}
