package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.dao.AgentDao;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dto.agent.AgentConvertDto;
import com.enttribe.promptanalyzer.dto.agent.AgentDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Agent;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

@ExtendWith(MockitoExtension.class)

class AgentServiceImplTest {

  @Mock
    private AgentDao agentDao;

  @Mock
    private PromptDao promptDao;

  @Mock
    private CustomFilter customFilter;

    @InjectMocks
    private AgentServiceImpl agentService;

    private AgentDto agentDto;
    private Prompt prompt;
    private Agent agent;

    @BeforeEach
    void setUp() {
        agentDto = new AgentDto();
        agentDto.setDisplayName("Test Agent");
        agentDto.setName("Agent1");
        agentDto.setVariables("var1,var2");
        agentDto.setIcon("icon.png");
        agentDto.setPurpose("Testing purpose");
        agentDto.setStatus("ACTIVE");

        prompt = new Prompt();
        prompt.setId(321);
        prompt.setName("Test Prompt for testing");
        prompt.setLlmModel(new LlmModel());

        agentDto.setPrompt(prompt);

        agent = new Agent();
        agent.setDisplayName("Test Agent");
        agent.setName("Agent1");
        agent.setVariables("var1,var2");
        agent.setIcon("icon.png");
        agent.setPurpose("Testing purpose");
        agent.setStatus("ACTIVE");
        agent.setPrompt(prompt);
    }

    @Test
    @DisplayName("Create agent successfully")
    void createAgentSuccess() {
        when(promptDao.findById(agentDto.getPrompt().getId())).thenReturn(Optional.of(prompt));
        when(agentDao.save(any(Agent.class))).thenReturn(new Agent());

        Map<String, String> result = agentService.createAgent(agentDto);

        assertNotNull(result);
        assertEquals("success", result.get("result"));
        verify(agentDao, times(1)).save(any(Agent.class));
    }

    @Test
    @DisplayName("Create agent failure")
    void createAgentFailure() {
        when(promptDao.findById(agentDto.getPrompt().getId())).thenReturn(Optional.empty());

        BusinessException exception = assertThrows(BusinessException.class, () -> agentService.createAgent(agentDto));

        assertTrue(exception.getMessage().contains("unable to save agent"));
        verify(promptDao, times(1)).findById(agentDto.getPrompt().getId());
        verify(agentDao, never()).save(any(Agent.class));
    }
    @Test
    @DisplayName("Search agents success")
    void searchAgentSuccess() {
        when(customFilter.searchByFilter(eq(Agent.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(Collections.singletonList(agent));

        List<AgentConvertDto> result = agentService.search("name='Test'", 0, 10, "id", "asc");

        assertNotNull(result, "Result should not be null");
        assertEquals(1, result.size(), "Result size should be 1");
        verify(customFilter, times(1)).searchByFilter(eq(Agent.class), anyString(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("Search agents failure")
    void searchAgentFailure() {
        when(customFilter.searchByFilter(eq(Agent.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(Collections.emptyList());
        List<AgentConvertDto> result = agentService.search("name='NonExistent'", 0, 10, "id", "asc");
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty when no agents are found");
        verify(customFilter, times(1)).searchByFilter(eq(Agent.class), anyString(), anyString(), anyString(), anyInt(), anyInt());
    }
}

