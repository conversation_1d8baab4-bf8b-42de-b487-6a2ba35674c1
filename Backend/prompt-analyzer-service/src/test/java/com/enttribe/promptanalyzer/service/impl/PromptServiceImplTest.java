package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dto.AssertionTemplateDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDtoSdk;
import com.enttribe.promptanalyzer.dto.prompt.PromptRequestDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVariableDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVersionDetailsDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.CSVUtils;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.util.PromptConvertor;
import com.enttribe.promptanalyzer.util.SdkUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.apache.commons.csv.CSVRecord;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.function.Consumer;
import javax.validation.Path;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT) // Use lenient to allow some flexibility in mocking
class PromptServiceImplTest {

    @Mock
    private PromptDao promptDao;

    @Mock
    private CustomFilter customFilter;

    @Mock
    private LlmModelDao llmModelDao;

    @Mock
    private EntityManager entityManager;

    @InjectMocks
    private PromptServiceImpl promptService;

    private PromptDto mockPromptDto;
    private Prompt mockPrompt;
    private LlmModel mockLlmModel;
    private PromptRequestDto mockPromptRequestDto;
    private AssertionTemplateDto mockAssertionTemplateDto;

    private MockedStatic<PromptConvertor> mockedPromptConvertor;
    private MockedStatic<SdkUtils> mockedSdkUtils;
    private MockedStatic<CSVUtils> mockedCSVUtils;
    private MockedStatic<JsonUtils> mockedJsonUtils;


    private static final List<String> csvColumnHeader = List.of("Application", "Name", "Category",
            "PromptId", "Temperature", "MaxToken", "Version", "JsonMode", "TopP", "Type", "LlmGuard", "AssertionTemplate", "DefaultFormat","Tags","Messages", "LLMModel");


    @BeforeEach
    void setUp() {
        mockPromptDto = new PromptDto();
        mockPromptDto.setApplication("TestApp");
        mockPromptDto.setName("TestPrompt");
        mockPromptDto.setCategory("TestCategory");
        mockPromptDto.setStatus("DRAFT");
        mockPromptDto.setTemperature(0.7);
        mockPromptDto.setMaxTokens(100);
        mockPromptDto.setTopP(1.0);
        mockPromptDto.setJsonMode(true);
        mockPromptDto.setLlmGuard(false);
        mockPromptDto.setType("chat");
        mockPromptDto.setTags("tag1,tag2");
        mockPromptDto.setAssertionTemplate("template");
        mockPromptDto.setDefaultFormat("format");
        mockPromptDto.setModel("gpt-4");
        mockPromptDto.setProvider("openai");
        List<Message> messages = new ArrayList<>();
        messages.add(createMessage(1L, "system", "You are a helpful assistant.", mockPrompt));
        messages.add(createMessage(2L, "user", "What is the capital of France?", mockPrompt));
        mockPromptDto.setMessages(messages);

        mockLlmModel = new LlmModel();
        mockLlmModel.setId(1);
        mockLlmModel.setModel("gpt-4");
        mockLlmModel.setProvider("openai");
        mockLlmModel.setType("chat");

        mockPrompt = new Prompt();
        mockPrompt.setId(1);
        mockPrompt.setApplication("TestApp");
        mockPrompt.setName("TestPrompt");
        mockPrompt.setCategory("TestCategory");
        mockPrompt.setStatus("DRAFT");
        mockPrompt.setVersion("v-0");
        mockPrompt.setPromptId("TestApp-TestCategory-TestPrompt-v-0");
        mockPrompt.setTemperature(0.7);
        mockPrompt.setMaxToken(100);
        mockPrompt.setTopP(1.0);
        mockPrompt.setJsonMode(true);
        mockPrompt.setLlmGuard(false);
        mockPrompt.setType("chat");
        mockPrompt.setTag("tag1,tag2");
        mockPrompt.setAssertionTemplate("template");
        mockPrompt.setDefaultFormat("format");
        mockPrompt.setLlmModel(mockLlmModel);
        mockPrompt.setMessages(messages);
        mockPrompt.setDeleted(false);
        mockPrompt.setCreatedTime(new Date());
        mockPrompt.setModifiedTime(new Date());

        mockPromptRequestDto = new PromptRequestDto();
        mockPromptRequestDto.setApplication("TestApp");
        mockPromptRequestDto.setName("TestPrompt");
        mockPromptRequestDto.setCategory("TestCategory");
        mockPromptRequestDto.setStatus("DRAFT");

        mockAssertionTemplateDto = new AssertionTemplateDto();
        mockAssertionTemplateDto.setPromptId(1);
        mockAssertionTemplateDto.setAssertionTemplate("updated template");

        // Mock static classes
        mockedPromptConvertor = mockStatic(PromptConvertor.class);
        mockedSdkUtils = mockStatic(SdkUtils.class);
        mockedCSVUtils = mockStatic(CSVUtils.class);
        mockedJsonUtils = mockStatic(JsonUtils.class);
    }

    @AfterEach
    void tearDown() {
        // Close mocked static classes
        mockedPromptConvertor.close();
        mockedSdkUtils.close();
        mockedCSVUtils.close();
        mockedJsonUtils.close();
    }


    // Test cases for savePrompt

    @Test
    @DisplayName("Save Prompt - Success")
    void savePromptSuccess() {
        List<PromptVersionDetailsDto> versions = new ArrayList<>();
        when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString())).thenReturn(versions);
        when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString())).thenReturn(mockLlmModel);
        when(promptDao.save(any(Prompt.class))).thenReturn(mockPrompt);

        Map<String, String> result = promptService.savePrompt(mockPromptDto);

        assertNotNull(result);
        assertEquals("success", result.get("result"));
        assertEquals("1", result.get("promptId"));
        verify(promptDao, times(1)).getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
        verify(llmModelDao, times(1)).findByModelAndProvider(anyString(), anyString(), anyString());
        verify(promptDao, times(1)).save(any(Prompt.class));
    }

     @Test
    @DisplayName("Save Prompt - Constraint Violation Exception")
    void savePromptConstraintViolationException() {
         List<PromptVersionDetailsDto> versions = new ArrayList<>();
        when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString())).thenReturn(versions);
        when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString())).thenReturn(mockLlmModel);

        // Simulate a ConstraintViolationException
        ConstraintViolation<?> violation = mock(ConstraintViolation.class);
        Path mockPath = mock(Path.class);
        when(violation.getPropertyPath()).thenReturn((Path) mockPath);
        when(mockPath.toString()).thenReturn("testField"); // Mock toString() as the assertion checks the string representation
        when(violation.getMessage()).thenReturn("test message");
        Set<ConstraintViolation<?>> violations = Set.of(violation);
        ConstraintViolationException cve = new ConstraintViolationException("test", violations);

        when(promptDao.save(any(Prompt.class))).thenThrow(cve);

        BusinessException thrown = assertThrows(BusinessException.class, () -> promptService.savePrompt(mockPromptDto));

        assertTrue(thrown.getMessage().contains("testField test message"));
        verify(promptDao, times(1)).save(any(Prompt.class));
    }

    @Test
    @DisplayName("Save Prompt - Generic Exception")
    void savePromptGenericException() {
        List<PromptVersionDetailsDto> versions = new ArrayList<>();
        when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString())).thenReturn(versions);
        when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString())).thenReturn(mockLlmModel);

        // Simulate a generic RuntimeException
        when(promptDao.save(any(Prompt.class))).thenThrow(new RuntimeException("Database error"));

        BusinessException thrown = assertThrows(BusinessException.class, () -> promptService.savePrompt(mockPromptDto));

        assertEquals("Unable to save prompt", thrown.getMessage());
        assertTrue(thrown.getCause() instanceof RuntimeException);
        verify(promptDao, times(1)).save(any(Prompt.class));
    }

    @Test
    @DisplayName("Save Prompt - LLM Model Not Found")
    void savePromptLlmModelNotFound() {
        List<PromptVersionDetailsDto> versions = new ArrayList<>();
        when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString())).thenReturn(versions);
        when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString())).thenReturn(null); // Simulate LLM Model not found

        // Assert that an IllegalArgumentException is thrown
        BusinessException thrown = assertThrows(BusinessException.class, () -> promptService.savePrompt(mockPromptDto));

        // Verify the cause of the BusinessException is IllegalArgumentException
        assertTrue(thrown.getCause() instanceof IllegalArgumentException);

        // Verify the specific message of the caused IllegalArgumentException
        assertEquals("llm model is not provided", thrown.getCause().getMessage());

        // Verify interactions
        verify(llmModelDao, times(1)).findByModelAndProvider(anyString(), anyString(), anyString());
        verify(promptDao, never()).save(any(Prompt.class)); // Verify save is not called
    }

    @Test
    @DisplayName("Save Prompt - Existing Versions")
    void savePromptExistingVersions() {
        List<PromptVersionDetailsDto> versions = new ArrayList<>();
        PromptVersionDetailsDto v1 = new PromptVersionDetailsDto();
        v1.setVersion("v-1");
        versions.add(v1);
        PromptVersionDetailsDto v2 = new PromptVersionDetailsDto();
        v2.setVersion("v-2");
        versions.add(v2);

        when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString())).thenReturn(versions);
        when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString())).thenReturn(mockLlmModel);
        when(promptDao.save(any(Prompt.class))).thenAnswer(invocation -> invocation.getArgument(0)); // Return the saved prompt to check version

        mockPromptDto.setStatus("PUBLISH"); // Change status to PUBLISH to trigger versioning logic

        promptService.savePrompt(mockPromptDto);

        ArgumentCaptor<Prompt> promptCaptor = ArgumentCaptor.forClass(Prompt.class);
        verify(promptDao).save(promptCaptor.capture());
        Prompt savedPrompt = promptCaptor.getValue();

        assertEquals("v-3", savedPrompt.getVersion()); // Verify the next version is correctly calculated
        assertEquals("TestApp-TestCategory-TestPrompt-v-3", savedPrompt.getPromptId()); // Verify promptId
        verify(promptDao, times(1)).getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
        verify(llmModelDao, times(1)).findByModelAndProvider(anyString(), anyString(), anyString());
        verify(promptDao, times(1)).save(any(Prompt.class));
    }

    // Test cases for getVersionsOfPrompt

    @Test
    @DisplayName("Get Versions Of Prompt - Success with v-0")
    void getVersionsOfPromptSuccessWithV0() {
        List<PromptVersionDetailsDto> versions = new ArrayList<>();
        PromptVersionDetailsDto v0 = new PromptVersionDetailsDto();
        v0.setVersion("v-0");
        versions.add(v0);
        PromptVersionDetailsDto v1 = new PromptVersionDetailsDto();
        v1.setVersion("v-1");
        versions.add(v1);

        when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString())).thenReturn(versions);

        List<PromptVersionDetailsDto> result = promptService.getVersionsOfPrompt("app", "name", "cat", "status");

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("v-1", result.get(0).getVersion());
        verify(promptDao, times(1)).getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
    }

    @Test
    @DisplayName("Get Versions Of Prompt - No Versions")
    void getVersionsOfPromptNoVersions() {
        List<PromptVersionDetailsDto> versions = new ArrayList<>();

        when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString())).thenReturn(versions);

        List<PromptVersionDetailsDto> result = promptService.getVersionsOfPrompt("app", "name", "cat", "status");

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(promptDao, times(1)).getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
    }

    // Test cases for softDelete

    @Test
    @DisplayName("Soft Delete - Success")
    void softDeleteSuccess() {
        when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));
        when(promptDao.save(any(Prompt.class))).thenReturn(mockPrompt);

        Map<String, String> result = promptService.softDelete(1);

        assertNotNull(result);
        assertEquals("success", result.get("result"));
        assertTrue(mockPrompt.getDeleted());
        verify(promptDao, times(1)).findById(1);
        verify(promptDao, times(1)).save(any(Prompt.class));
    }

    @Test
    @DisplayName("Soft Delete - Not Found")
    void softDeleteNotFound() {
        when(promptDao.findById(99)).thenReturn(Optional.empty());

        Map<String, String> result = promptService.softDelete(99);

        assertNotNull(result);
        assertEquals("failed", result.get("result"));
        verify(promptDao, times(1)).findById(99);
        verify(promptDao, never()).save(any(Prompt.class));
    }

    // Test cases for getPromptById

    @Test
    @DisplayName("Get Prompt By ID - Success")
    void getPromptByIdSuccess() {
        when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));
        // Mock the static method call
        mockedPromptConvertor.when(() -> PromptConvertor.getPromptDto(any(Prompt.class))).thenReturn(new PromptConvertorDto()); // Return a dummy DTO

        PromptConvertorDto result = promptService.getPromptById(1);

        assertNotNull(result);
        verify(promptDao, times(1)).findById(1);
        mockedPromptConvertor.verify(() -> PromptConvertor.getPromptDto(eq(mockPrompt)), times(1));
    }

    @Test
    @DisplayName("Get Prompt By ID - Not Found")
    void getPromptByIdNotFound() {
        when(promptDao.findById(99)).thenReturn(Optional.empty());

        BusinessException thrown = assertThrows(BusinessException.class, () -> promptService.getPromptById(99));

        assertEquals("Error while fetching data: No prompt found with ID 99", thrown.getMessage());
        verify(promptDao, times(1)).findById(99);
        // Verify static method was not called
         mockedPromptConvertor.verify(() -> PromptConvertor.getPromptDto(any(Prompt.class)), never());
    }

    @Test
    @DisplayName("Get Prompt By ID - Null ID")
    void getPromptByIdNullId() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class, () -> promptService.getPromptById(null));

        assertEquals("ID is required.", thrown.getMessage());
        verify(promptDao, never()).findById(anyInt());
    }

    // Test cases for findPromptById (SDK)

     @Test
    @DisplayName("Find Prompt By ID (SDK) - Success")
    void findPromptByIdSdkSuccess() {
        when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));
        // Mock the static method call
        mockedSdkUtils.when(() -> SdkUtils.getPromptDto(any(Prompt.class))).thenReturn(new PromptDtoSdk()); // Return a dummy DTO

        PromptDtoSdk result = promptService.findPromptById(1);

        assertNotNull(result);
        verify(promptDao, times(1)).findById(1);
        mockedSdkUtils.verify(() -> SdkUtils.getPromptDto(eq(mockPrompt)), times(1));
    }

    @Test
    @DisplayName("Find Prompt By ID (SDK) - Not Found")
    void findPromptByIdSdkNotFound() {
        when(promptDao.findById(99)).thenReturn(Optional.empty());

        BusinessException thrown = assertThrows(BusinessException.class, () -> promptService.findPromptById(99));

        assertEquals("Prompt with ID 99 not found.", thrown.getMessage());
        verify(promptDao, times(1)).findById(99);
         // Verify static method was not called
        mockedSdkUtils.verify(() -> SdkUtils.getPromptDto(any(Prompt.class)), never());
    }

    // Test cases for exists

    @Test
    @DisplayName("Exists Prompt - True")
    void existsPromptTrue() {
        when(promptDao.existsByNameAndApplicationAndCategoryAndStatus(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

        Map<String, Object> result = promptService.exists(mockPromptRequestDto);

        assertNotNull(result);
        assertTrue((Boolean) result.get("result"));
        verify(promptDao, times(1)).existsByNameAndApplicationAndCategoryAndStatus(eq("TestPrompt"), eq("TestApp"), eq("TestCategory"), eq("DRAFT"));
    }

    @Test
    @DisplayName("Exists Prompt - False")
    void existsPromptFalse() {
        when(promptDao.existsByNameAndApplicationAndCategoryAndStatus(anyString(), anyString(), anyString(), anyString())).thenReturn(false);

        Map<String, Object> result = promptService.exists(mockPromptRequestDto);

        assertNotNull(result);
        assertFalse((Boolean) result.get("result"));
        verify(promptDao, times(1)).existsByNameAndApplicationAndCategoryAndStatus(eq("TestPrompt"), eq("TestApp"), eq("TestCategory"), eq("DRAFT"));
    }

    @Test
    @DisplayName("Exists Prompt - Exception")
    void existsPromptException() {
        when(promptDao.existsByNameAndApplicationAndCategoryAndStatus(anyString(), anyString(), anyString(), anyString())).thenThrow(new RuntimeException("DB error"));

        BusinessException thrown = assertThrows(BusinessException.class, () -> promptService.exists(mockPromptRequestDto));

        assertEquals("An error occurred while processing the existence check.", thrown.getMessage());
        assertTrue(thrown.getCause() instanceof RuntimeException);
        verify(promptDao, times(1)).existsByNameAndApplicationAndCategoryAndStatus(eq("TestPrompt"), eq("TestApp"), eq("TestCategory"), eq("DRAFT"));
    }

    // Test cases for updateAssertionTemplate

    @Test
    @DisplayName("Update Assertion Template - Success")
    void updateAssertionTemplateSuccess() {
        mockPrompt.setStatus("PUBLISH"); // Ensure status is PUBLISH
        when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));
        when(promptDao.save(any(Prompt.class))).thenReturn(mockPrompt);

        Map<String, String> result = promptService.updateAssertionTemplate(mockAssertionTemplateDto);

        assertNotNull(result);
        assertEquals("success", result.get("result"));
        assertEquals("updated template", mockPrompt.getAssertionTemplate());
        verify(promptDao, times(1)).findById(1);
        verify(promptDao, times(1)).save(any(Prompt.class));
    }

    @Test
    @DisplayName("Update Assertion Template - Prompt Not Found")
    void updateAssertionTemplateNotFound() {
        // Mock findById to return empty for the ID in the DTO
        when(promptDao.findById(mockAssertionTemplateDto.getPromptId())).thenReturn(Optional.empty());

        // Assert that a BusinessException is thrown when calling the service method
        BusinessException thrown = assertThrows(BusinessException.class, () -> promptService.updateAssertionTemplate(mockAssertionTemplateDto));

        // Assert the specific exception message
        assertEquals("Prompt with ID 1 not found.", thrown.getMessage());

        // Verify that findById was called exactly once with the correct ID from the DTO
        verify(promptDao, times(1)).findById(mockAssertionTemplateDto.getPromptId());

        // Verify that save was never called
        verify(promptDao, never()).save(any(Prompt.class));
    }

    @Test
    @DisplayName("Update Assertion Template - Status Not PUBLISH")
    void updateAssertionTemplateStatusNotPublish() {
        mockPrompt.setStatus("DRAFT"); // Status is not PUBLISH
        when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));

        BusinessException thrown = assertThrows(BusinessException.class, () -> promptService.updateAssertionTemplate(mockAssertionTemplateDto));

        assertEquals("Prompt status is not PUBLISH. Cannot update assertionTemplate.", thrown.getMessage());
        verify(promptDao, times(1)).findById(1);
        verify(promptDao, never()).save(any(Prompt.class));
    }

     // Test cases for updatePrompt

     @Test
    @DisplayName("Update Prompt - Success (Status DRAFT)")
    void updatePromptSuccessStatusDraft() {
        mockPromptDto.setId(1);
        mockPromptDto.setStatus("DRAFT");
        when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));
        when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString())).thenReturn(mockLlmModel);
        when(promptDao.save(any(Prompt.class))).thenReturn(mockPrompt);

        Map<String, String> result = promptService.updatePrompt(mockPromptDto);

        assertNotNull(result);
        assertEquals("success", result.get("result"));
        assertEquals("DRAFT", mockPrompt.getStatus()); // Verify status remains DRAFT
        verify(promptDao, times(1)).findById(1);
        verify(llmModelDao, times(1)).findByModelAndProvider(anyString(), anyString(), anyString());
        verify(promptDao, times(1)).save(any(Prompt.class));
        verify(promptDao, never()).getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString()); // Should not call getVersionsOfPrompt
    }

    @Test
    @DisplayName("Update Prompt - Success (Status DRAFT to PUBLISH)")
    void updatePromptSuccessStatusDraftToPublish() {
        mockPromptDto.setId(1);
        mockPromptDto.setStatus("PUBLISH"); // New status is PUBLISH
        mockPrompt.setStatus("DRAFT"); // Existing status is DRAFT

        List<PromptVersionDetailsDto> existingVersions = new ArrayList<>();
        PromptVersionDetailsDto v1 = new PromptVersionDetailsDto();
        v1.setVersion("v-1");
        existingVersions.add(v1);
        when(promptDao.getVersionsOfPrompt(anyString(), anyString(), anyString(), eq("PUBLISH"))).thenReturn(existingVersions);

        when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));
        when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString())).thenReturn(mockLlmModel);
        when(promptDao.save(any(Prompt.class))).thenAnswer(invocation -> invocation.getArgument(0)); // Return the updated prompt

        Map<String, String> result = promptService.updatePrompt(mockPromptDto);

        assertNotNull(result);
        assertEquals("success", result.get("result"));
        assertEquals("PUBLISH", mockPrompt.getStatus()); // Verify status is updated to PUBLISH
        assertEquals("v-2", mockPrompt.getVersion()); // Verify version is incremented
        assertEquals("TestApp-TestCategory-TestPrompt-v-2", mockPrompt.getPromptId()); // Verify promptId is updated

        verify(promptDao, times(1)).findById(1);
        verify(promptDao, times(1)).getVersionsOfPrompt(anyString(), anyString(), anyString(), eq("PUBLISH"));
        verify(llmModelDao, times(1)).findByModelAndProvider(anyString(), anyString(), anyString());
        verify(promptDao, times(1)).save(any(Prompt.class));
    }

     @Test
    @DisplayName("Update Prompt - Prompt Not Found")
    void updatePromptNotFound() {
        mockPromptDto.setId(99);
        when(promptDao.findById(99)).thenReturn(Optional.empty());

        BusinessException thrown = assertThrows(BusinessException.class, () -> promptService.updatePrompt(mockPromptDto));

        assertEquals("Unable to update prompt", thrown.getMessage());
        verify(promptDao, times(1)).findById(mockPromptDto.getId());
    }

     @Test
    @DisplayName("Update Prompt - Constraint Violation Exception")
    void updatePromptConstraintViolationException() {
         mockPromptDto.setId(1);
        when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));
        when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString())).thenReturn(mockLlmModel);

         // Simulate a ConstraintViolationException
        ConstraintViolation<?> violation = mock(ConstraintViolation.class);
        Path mockPath = mock(Path.class);
        when(violation.getPropertyPath()).thenReturn((Path) mockPath);
        when(mockPath.toString()).thenReturn("testField"); // Mock toString() as the assertion checks the string representation
        when(violation.getMessage()).thenReturn("test message");
        Set<ConstraintViolation<?>> violations = Set.of(violation);
        ConstraintViolationException cve = new ConstraintViolationException("test", violations);

        when(promptDao.save(any(Prompt.class))).thenThrow(cve);

        BusinessException thrown = assertThrows(BusinessException.class, () -> promptService.updatePrompt(mockPromptDto));

        assertTrue(thrown.getMessage().contains("testField test message"));
        verify(promptDao, times(1)).save(any(Prompt.class));
    }

    @Test
    @DisplayName("Update Prompt - Generic Exception")
    void updatePromptGenericException() {
        mockPromptDto.setId(1);
        when(promptDao.findById(1)).thenReturn(Optional.of(mockPrompt));
        when(llmModelDao.findByModelAndProvider(anyString(), anyString(), anyString())).thenReturn(mockLlmModel);

        // Simulate a generic RuntimeException
        when(promptDao.save(any(Prompt.class))).thenThrow(new RuntimeException("Database error"));

        BusinessException thrown = assertThrows(BusinessException.class, () -> promptService.updatePrompt(mockPromptDto));

        assertEquals("Unable to update prompt", thrown.getMessage());
        assertTrue(thrown.getCause() instanceof RuntimeException);
        verify(promptDao, times(1)).save(any(Prompt.class));
    }

    // Test cases for search

    @Test
    @DisplayName("Search Prompts - Success")
    void searchPromptsSuccess() {
        List<Prompt> mockPrompts = List.of(mockPrompt);
        when(customFilter.searchByFilter(eq(Prompt.class), anyString(), anyString(), anyString(), anyInt(), anyInt())).thenReturn(mockPrompts);
        // Mock the static method call
        mockedPromptConvertor.when(() -> PromptConvertor.getPromptDtoList(anyList())).thenReturn(List.of(new PromptConvertorDto())); // Return a dummy list

        List<PromptConvertorDto> result = promptService.search("filter", 0, 10, "name", "asc");

        assertNotNull(result);
        assertFalse(result.isEmpty());
        verify(customFilter, times(1)).searchByFilter(eq(Prompt.class), eq("filter"), eq("name"), eq("asc"), eq(0), eq(10));
        mockedPromptConvertor.verify(() -> PromptConvertor.getPromptDtoList(eq(mockPrompts)), times(1));
    }

     @Test
    @DisplayName("Search Prompts - No Results")
    void searchPromptsNoResults() {
        List<Prompt> mockPrompts = new ArrayList<>();
        when(customFilter.searchByFilter(eq(Prompt.class), anyString(), anyString(), anyString(), anyInt(), anyInt())).thenReturn(mockPrompts);
         // Mock the static method call
        mockedPromptConvertor.when(() -> PromptConvertor.getPromptDtoList(anyList())).thenReturn(new ArrayList<>()); // Return a dummy empty list

        List<PromptConvertorDto> result = promptService.search("filter", 0, 10, "name", "asc");

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(customFilter, times(1)).searchByFilter(eq(Prompt.class), eq("filter"), eq("name"), eq("asc"), eq(0), eq(10));
        mockedPromptConvertor.verify(() -> PromptConvertor.getPromptDtoList(eq(mockPrompts)), times(1));
    }

    // Test cases for count

     @Test
    @DisplayName("Count Prompts - Success")
    void countPromptsSuccess() {
        when(customFilter.countByFilter(eq(Prompt.class), anyString())).thenReturn(10L);

        Long result = promptService.count("filter");

        assertEquals(10L, result);
        verify(customFilter, times(1)).countByFilter(eq(Prompt.class), eq("filter"));
    }

     // Test cases for getPromptByApplication

    @Test
    @DisplayName("Get Prompt By Application - Success")
    void getPromptByApplicationSuccess() {
        List<Prompt> mockPrompts = List.of(mockPrompt);
        when(promptDao.getPromptByApplication(anyString())).thenReturn(mockPrompts);
        // Mock the static method call
        mockedSdkUtils.when(() -> SdkUtils.getPromptDtoList(anyList())).thenReturn(List.of(new PromptDtoSdk())); // Return a dummy list

        List<PromptDtoSdk> result = promptService.getPromptByApplication("TestApp");

        assertNotNull(result);
        assertFalse(result.isEmpty());
        verify(promptDao, times(1)).getPromptByApplication(eq("TestApp"));
        mockedSdkUtils.verify(() -> SdkUtils.getPromptDtoList(eq(mockPrompts)), times(1));
    }

    @Test
    @DisplayName("Get Prompt By Application - Not Found")
    void getPromptByApplicationNotFound() {
         List<Prompt> mockPrompts = new ArrayList<>();
        when(promptDao.getPromptByApplication(anyString())).thenReturn(mockPrompts);
        // Mock the static method call
        mockedSdkUtils.when(() -> SdkUtils.getPromptDtoList(anyList())).thenReturn(new ArrayList<>()); // Return a dummy empty list

        List<PromptDtoSdk> result = promptService.getPromptByApplication("NonExistentApp");

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(promptDao, times(1)).getPromptByApplication(eq("NonExistentApp"));
        mockedSdkUtils.verify(() -> SdkUtils.getPromptDtoList(eq(mockPrompts)), times(1));
    }

    @Test
    @DisplayName("Get Prompt By Application - Null App Name")
    void getPromptByApplicationNullAppName() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class, () -> promptService.getPromptByApplication(null));

        assertEquals("Application name cannot be null or empty.", thrown.getMessage());
        verify(promptDao, never()).getPromptByApplication(anyString());
    }

    @Test
    @DisplayName("Get Prompt By Application - Empty App Name")
    void getPromptByApplicationEmptyAppName() {
         IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class, () -> promptService.getPromptByApplication(""));

        assertEquals("Application name cannot be null or empty.", thrown.getMessage());
        verify(promptDao, never()).getPromptByApplication(anyString());
    }

    // Test cases for getDistinctApplications

    // Helper method to create Message objects for tests
    private Message createMessage(Long id, String role, String content, Prompt prompt) {
        Message message = new Message();
        message.setId(id);
        message.setRole(role);
        message.setContent(content);
        message.setPrompt(prompt);
        return message;
    }
}