package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.ExceptionAuditDao;
import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dao.PromptAuditDao;
import com.enttribe.promptanalyzer.dto.audit.ExceptionAuditDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.model.ExceptionAudit;
import com.enttribe.promptanalyzer.model.PromptAudit;
import com.enttribe.promptanalyzer.model.LlmModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)

class AuditServiceImplTest {

   @Mock
    private ExceptionAuditDao exceptionDao;

   @Mock
    private PromptAuditDao promptAuditDao;

   @Mock
    private LlmModelDao llmModelDao;

    @InjectMocks
    private AuditServiceImpl auditServiceImpl;

    private ExceptionAuditDto exceptionAuditDto;
    private PromptAuditDto promptAuditDto;
    private LlmModel llmModel;

    @BeforeEach
    void setUp() {
        // Setup ExceptionAuditDto
        exceptionAuditDto = new ExceptionAuditDto();
        Map<String, Object> methodParams = new HashMap<>();
        methodParams.put("userId", "user123");
        methodParams.put("requestId", "req-456");
        methodParams.put("timestamp", "2024-03-20T10:30:00Z");
        exceptionAuditDto.setMethodParameters(methodParams);

        Map<String, Object> identifiers = new HashMap<>();
        identifiers.put("className", "UserService");
        identifiers.put("methodName", "createUser");
        identifiers.put("exceptionType", "ValidationException");
        exceptionAuditDto.setIdentifier(identifiers);

        // Setup PromptAuditDto
        promptAuditDto = new PromptAuditDto();
        promptAuditDto.setAuditId("audit-123");
        promptAuditDto.setModel("gpt-4");
        promptAuditDto.setProvider("openai");
        promptAuditDto.setPromptToken(100L);
        promptAuditDto.setTotalToken(200L);

        // Setup LlmModel
        llmModel = new LlmModel();
        llmModel.setId(1);
        llmModel.setModel("gpt-4");
        llmModel.setProvider("openai");
        llmModel.setInputCost(30.0);
        llmModel.setOutputCost(60.0);
    }

    @Test
    @DisplayName("Save Exception Audit - Success Case")
    void saveExceptionAuditSuccess() {
        ExceptionAudit exceptionAudit = new ExceptionAudit();
        exceptionAudit.setMethodParameters("test-parameters");
        exceptionAudit.setIdentifier("test-identifier");
        
        when(exceptionDao.save(any(ExceptionAudit.class))).thenReturn(exceptionAudit);

        String result = auditServiceImpl.saveExceptionAudit(exceptionAuditDto);
        Assertions.assertEquals("success", result);

        exceptionAuditDto.setMethodParameters(null);
        exceptionAuditDto.setIdentifier(null);
        result = auditServiceImpl.saveExceptionAudit(exceptionAuditDto);
        Assertions.assertEquals("success", result);

        verify(exceptionDao, times(2)).save(any(ExceptionAudit.class));
    }

    @Test
    @DisplayName("Save Exception Audit - Failure Case")
    void saveExceptionAuditFailure() {
        when(exceptionDao.save(any(ExceptionAudit.class)))
            .thenThrow(new RuntimeException("DB Error"));

        String result = auditServiceImpl.saveExceptionAudit(exceptionAuditDto);
        
        Assertions.assertEquals("failed", result);
        verify(exceptionDao, times(1)).save(any(ExceptionAudit.class));
    }

    @Test
    @DisplayName("Save Prompt Audit - Success Case")
    void savePromptAuditSuccess() {
        when(promptAuditDao.save(any(PromptAudit.class)))
            .thenReturn(new PromptAudit());
        when(llmModelDao.findModelAndProvider("gpt-4", "openai"))
            .thenReturn(llmModel);

        String result = auditServiceImpl.savePromptAudit(promptAuditDto);
        
        Assertions.assertEquals("success", result);
        verify(promptAuditDao, times(1)).save(any(PromptAudit.class));
        verify(llmModelDao, times(1)).findModelAndProvider("gpt-4", "openai");
    }

    @Test
    @DisplayName("Save Prompt Audit - Failure Case")
    void savePromptAuditFailure() {
        when(promptAuditDao.save(any(PromptAudit.class)))
            .thenThrow(new RuntimeException("DB Error"));

        String result = auditServiceImpl.savePromptAudit(promptAuditDto);
        
        Assertions.assertEquals("failed", result);
        verify(promptAuditDao, times(1)).save(any(PromptAudit.class));
    }

    @Test
    @DisplayName("Get Prompt Audit List By Audit ID - Success Case")
    void getPromptAuditListByAuditId() {
        when(promptAuditDao.getPromptAuditListByAuditId(anyString()))
            .thenReturn(List.of(new PromptAudit()));

        List<PromptAudit> result = auditServiceImpl.getPromptAuditListByAuditId("auditId");
        
        Assertions.assertEquals(1, result.size());
        verify(promptAuditDao, times(1)).getPromptAuditListByAuditId(anyString());
    }
}
